<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS - DeepSeek R1 8B</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh; display: flex; flex-direction: column;
        }
        .header {
            background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);
            padding: 20px; text-align: center; color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); }
        .chat-container {
            flex: 1; display: flex; flex-direction: column;
            max-width: 800px; margin: 0 auto; width: 100%; padding: 20px;
        }
        .messages {
            flex: 1; overflow-y: auto; padding: 20px;
            background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);
            border-radius: 15px; margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .message {
            margin-bottom: 15px; padding: 15px; border-radius: 10px;
            max-width: 80%; word-wrap: break-word;
        }
        .user-message {
            background: rgba(255, 255, 255, 0.2); color: white;
            margin-left: auto; text-align: right;
        }
        .assistant-message {
            background: rgba(255, 255, 255, 0.3); color: white; margin-right: auto;
        }
        .system-message {
            background: rgba(255, 255, 0, 0.2); color: white;
            text-align: center; margin: 0 auto; font-style: italic;
        }
        .input-container {
            display: flex; gap: 10px;
            background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);
            padding: 20px; border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        #messageInput {
            flex: 1; padding: 15px; border: none; border-radius: 10px;
            background: rgba(255, 255, 255, 0.2); color: white;
            font-size: 16px; outline: none;
        }
        #messageInput::placeholder { color: rgba(255, 255, 255, 0.7); }
        #sendButton {
            padding: 15px 30px; background: rgba(255, 255, 255, 0.3);
            color: white; border: none; border-radius: 10px; cursor: pointer;
            font-size: 16px; font-weight: bold; transition: all 0.3s ease;
        }
        #sendButton:hover { background: rgba(255, 255, 255, 0.4); transform: translateY(-2px); }
        #sendButton:disabled { opacity: 0.5; cursor: not-allowed; }
        .loading { display: none; text-align: center; color: white; font-style: italic; padding: 10px; }
        .status {
            position: fixed; top: 20px; right: 20px; padding: 10px 20px;
            background: rgba(0, 255, 0, 0.2); color: white; border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 JARVIS</h1>
        <p>DeepSeek R1 8B - Interface Simple qui Marche</p>
    </div>

    <div class="status" id="status">🟢 Prêt</div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="message system-message">
                ✨ Bienvenue ! Posez votre question à JARVIS avec DeepSeek R1 8B.
            </div>
        </div>

        <div class="loading" id="loading">🤔 JARVIS réfléchit...</div>

        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Tapez votre message ici..." />
            <button id="sendButton">Envoyer</button>
        </div>
    </div>

    <script>
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');
        const status = document.getElementById('status');

        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            messageDiv.textContent = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            addMessage('user', message);
            messageInput.value = '';
            sendButton.disabled = true;
            loading.style.display = 'block';
            status.textContent = '🟡 En cours...';

            try {
                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: message,
                        n_predict: 150,
                        temperature: 0.7,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`Erreur serveur ${response.status}`);
                }

                const data = await response.json();
                let reply = data.content || data.text || 'Pas de réponse';
                reply = reply.trim();
                
                if (reply) {
                    addMessage('assistant', reply);
                    status.textContent = '🟢 Prêt';
                } else {
                    addMessage('system', '⚠️ Réponse vide du serveur');
                    status.textContent = '🟡 Réponse vide';
                }

            } catch (error) {
                console.error('Erreur:', error);
                addMessage('system', `❌ ${error.message}`);
                status.textContent = '🔴 Erreur';
            } finally {
                sendButton.disabled = false;
                loading.style.display = 'none';
            }
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        // Test de connexion
        async function testConnection() {
            try {
                const response = await fetch('http://localhost:8000/health', { timeout: 3000 });
                status.textContent = response.ok ? '🟢 Serveur OK' : '🔴 Serveur KO';
            } catch (error) {
                status.textContent = '🔴 Pas de serveur';
            }
        }

        testConnection();
        setInterval(testConnection, 30000); // Test toutes les 30s
    </script>
</body>
</html>
