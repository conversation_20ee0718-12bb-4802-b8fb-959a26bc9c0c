// 🔍 VALIDATION SYSTÈME JARVIS COMPLET
// Script de validation automatique pour vérifier toutes les fonctionnalités

const http = require('http');
const fs = require('fs');

class ValidationJarvisComplet {
    constructor() {
        this.portInterface = 8083;
        this.portLlama = 8080;
        this.tests = [];
        this.resultats = {
            succes: 0,
            echecs: 0,
            total: 0
        };
    }

    async demarrerValidation() {
        console.log('🔍 VALIDATION SYSTÈME JARVIS COMPLET');
        console.log('=====================================');
        
        await this.testerConnexionInterface();
        await this.testerConnexionLlama();
        await this.testerInterfaceComplete();
        await this.testerFonctionnalitesJarvis();
        await this.testerMemoire();
        
        this.afficherResultats();
    }

    async testerConnexionInterface() {
        console.log('\n🌐 Test connexion interface JARVIS...');
        
        try {
            const reponse = await this.faireRequete(this.portInterface, '/');
            if (reponse.includes('JARVIS') && reponse.includes('jarvis-main-panel')) {
                this.ajouterTest('Connexion Interface', true, 'Interface JARVIS accessible et complète');
            } else {
                this.ajouterTest('Connexion Interface', false, 'Interface incomplète');
            }
        } catch (error) {
            this.ajouterTest('Connexion Interface', false, error.message);
        }
    }

    async testerConnexionLlama() {
        console.log('🦙 Test connexion llama.cpp...');
        
        try {
            const reponse = await this.faireRequete(this.portLlama, '/');
            if (reponse.length > 100) {
                this.ajouterTest('Connexion llama.cpp', true, 'llama.cpp accessible');
            } else {
                this.ajouterTest('Connexion llama.cpp', false, 'Réponse trop courte');
            }
        } catch (error) {
            this.ajouterTest('Connexion llama.cpp', false, error.message);
        }
    }

    async testerInterfaceComplete() {
        console.log('🧠 Test interface complète...');
        
        try {
            const html = await this.faireRequete(this.portInterface, '/');
            
            const elementsRequis = [
                'jarvis-main-panel',
                'jarvis-mic-btn',
                'jarvis-speaker-btn',
                'jarvis-camera-btn',
                'agent-name-config',
                'jarvis-qi-display',
                'JarvisCognitiveSystem'
            ];
            
            let elementsPresents = 0;
            elementsRequis.forEach(element => {
                if (html.includes(element)) {
                    elementsPresents++;
                }
            });
            
            const pourcentage = (elementsPresents / elementsRequis.length) * 100;
            
            if (pourcentage >= 90) {
                this.ajouterTest('Interface Complète', true, `${elementsPresents}/${elementsRequis.length} éléments présents (${pourcentage.toFixed(1)}%)`);
            } else {
                this.ajouterTest('Interface Complète', false, `Seulement ${elementsPresents}/${elementsRequis.length} éléments présents`);
            }
            
        } catch (error) {
            this.ajouterTest('Interface Complète', false, error.message);
        }
    }

    async testerFonctionnalitesJarvis() {
        console.log('⚙️ Test fonctionnalités JARVIS...');
        
        try {
            const html = await this.faireRequete(this.portInterface, '/');
            
            const fonctionnalites = [
                { nom: 'Reconnaissance Vocale', pattern: 'webkitSpeechRecognition' },
                { nom: 'Synthèse Vocale', pattern: 'speechSynthesis' },
                { nom: 'Caméra', pattern: 'getUserMedia' },
                { nom: 'Évolution QI', pattern: 'startEvolution' },
                { nom: 'Mémoire Thermique', pattern: 'jarvisTriggerMemory' },
                { nom: 'Validation Système', pattern: 'validateSystem' },
                { nom: 'Gestion Erreurs', pattern: 'logError' }
            ];
            
            let fonctionnalitesOK = 0;
            fonctionnalites.forEach(func => {
                if (html.includes(func.pattern)) {
                    fonctionnalitesOK++;
                    console.log(`  ✅ ${func.nom}`);
                } else {
                    console.log(`  ❌ ${func.nom}`);
                }
            });
            
            const pourcentage = (fonctionnalitesOK / fonctionnalites.length) * 100;
            
            if (pourcentage >= 85) {
                this.ajouterTest('Fonctionnalités JARVIS', true, `${fonctionnalitesOK}/${fonctionnalites.length} fonctionnalités présentes`);
            } else {
                this.ajouterTest('Fonctionnalités JARVIS', false, `Seulement ${fonctionnalitesOK}/${fonctionnalites.length} fonctionnalités`);
            }
            
        } catch (error) {
            this.ajouterTest('Fonctionnalités JARVIS', false, error.message);
        }
    }

    async testerMemoire() {
        console.log('🧠 Test mémoire thermique...');
        
        try {
            // Vérifier fichiers mémoire
            const fichiersMemoire = [
                './conversations_permanentes.json',
                '/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/thermal_memory_persistent.json'
            ];
            
            let memoireOK = false;
            fichiersMemoire.forEach(fichier => {
                if (fs.existsSync(fichier)) {
                    memoireOK = true;
                    console.log(`  ✅ Fichier mémoire trouvé: ${fichier}`);
                }
            });
            
            if (memoireOK) {
                this.ajouterTest('Mémoire Thermique', true, 'Fichiers mémoire présents');
            } else {
                this.ajouterTest('Mémoire Thermique', false, 'Aucun fichier mémoire trouvé');
            }
            
        } catch (error) {
            this.ajouterTest('Mémoire Thermique', false, error.message);
        }
    }

    faireRequete(port, path) {
        return new Promise((resolve, reject) => {
            const options = {
                hostname: '127.0.0.1',
                port: port,
                path: path,
                method: 'GET',
                timeout: 5000
            };

            const req = http.request(options, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => resolve(data));
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Timeout'));
            });

            req.end();
        });
    }

    ajouterTest(nom, succes, details) {
        this.tests.push({ nom, succes, details, timestamp: new Date() });
        this.resultats.total++;
        
        if (succes) {
            this.resultats.succes++;
            console.log(`  ✅ ${nom}: ${details}`);
        } else {
            this.resultats.echecs++;
            console.log(`  ❌ ${nom}: ${details}`);
        }
    }

    afficherResultats() {
        console.log('\n📊 RÉSULTATS VALIDATION');
        console.log('========================');
        console.log(`✅ Succès: ${this.resultats.succes}`);
        console.log(`❌ Échecs: ${this.resultats.echecs}`);
        console.log(`📊 Total: ${this.resultats.total}`);
        
        const pourcentage = (this.resultats.succes / this.resultats.total) * 100;
        console.log(`🎯 Taux de réussite: ${pourcentage.toFixed(1)}%`);
        
        if (pourcentage >= 90) {
            console.log('\n🎉 SYSTÈME JARVIS COMPLET OPÉRATIONNEL !');
            console.log('🌐 Interface: http://127.0.0.1:8083');
            console.log('🧠 Toutes les fonctionnalités sont disponibles');
        } else if (pourcentage >= 70) {
            console.log('\n⚠️ SYSTÈME JARVIS PARTIELLEMENT FONCTIONNEL');
            console.log('🔧 Quelques corrections nécessaires');
        } else {
            console.log('\n❌ SYSTÈME JARVIS NÉCESSITE DES CORRECTIONS');
            console.log('🛠️ Plusieurs problèmes détectés');
        }
        
        // Sauvegarder rapport
        this.sauvegarderRapport();
    }

    sauvegarderRapport() {
        const rapport = {
            timestamp: new Date().toISOString(),
            resultats: this.resultats,
            tests: this.tests,
            configuration: {
                portInterface: this.portInterface,
                portLlama: this.portLlama
            }
        };
        
        fs.writeFileSync('./rapport_validation_jarvis.json', JSON.stringify(rapport, null, 2));
        console.log('\n📋 Rapport sauvegardé: rapport_validation_jarvis.json');
    }
}

// 🚀 DÉMARRAGE VALIDATION
const validation = new ValidationJarvisComplet();
validation.demarrerValidation().catch(console.error);
