#!/usr/bin/env node

// 🔥 MÉMOIRE THERMIQUE CONVERSATIONS - CAPTURE ENTRÉE/SORTIE
// Jean-Luc - Connexion mémoire thermique aux conversations actuelles

const fs = require('fs');
const path = require('path');

class MemoireThermiqueConversations {
    constructor() {
        this.nom = "🔥 MÉMOIRE THERMIQUE CONVERSATIONS";
        this.memoryFile = './thermal_memory_persistent.json';
        this.conversationsFile = './conversations_permanentes.json';
        this.captureActive = true;
        this.conversationsCapturees = 0;
        this.derniereCapture = Date.now();
        
        console.log(`🚀 ${this.nom} - INITIALISATION`);
        console.log(`📡 Capture ENTRÉE/SORTIE de TOUTES les conversations`);
        console.log(`💾 Conversations effacées interface = GARDÉES en mémoire`);
        
        this.initialiserCaptureConversations();
    }
    
    initialiserCaptureConversations() {
        // 📡 Surveillance continue des conversations - toutes les 2 secondes
        setInterval(() => {
            this.capturerConversationsActuelles();
        }, 2000);
        
        // 🔄 Intégration mémoire thermique - toutes les 10 secondes
        setInterval(() => {
            this.integrerConversationsDansMemoire();
        }, 10000);
        
        // 💾 Sauvegarde conversations permanentes - toutes les 30 secondes
        setInterval(() => {
            this.sauvegarderConversationsPermanentes();
        }, 30000);
        
        // 🧠 Analyse sémantique conversations - toutes les 60 secondes
        setInterval(() => {
            this.analyserSemantique();
        }, 60000);
        
        console.log(`✅ Capture conversations activée - Surveillance continue`);
    }
    
    capturerConversationsActuelles() {
        try {
            // 📡 Capture depuis les processus actifs (agents)
            this.capturerDepuisAgents();
            
            // 📡 Capture depuis les logs système
            this.capturerDepuisLogs();
            
            // 📡 Capture depuis l'interface utilisateur
            this.capturerDepuisInterface();
            
        } catch (error) {
            console.error(`❌ Erreur capture conversations:`, error.message);
        }
    }
    
    capturerDepuisAgents() {
        // 🤖 Capture conversations Agent 1 et Agent 2
        const memory = this.lireMemoire();
        if (!memory) return;
        
        // 📥 Recherche nouvelles entrées
        const maintenant = Date.now();
        const nouvellesEntrees = this.extraireNouvellesEntrees(memory, maintenant);
        
        if (nouvellesEntrees.length > 0) {
            console.log(`📡 CAPTURE AGENTS: ${nouvellesEntrees.length} nouvelles entrées`);
            
            nouvellesEntrees.forEach(entree => {
                this.ajouterConversationPermanente({
                    type: 'agent_communication',
                    source: entree.source || 'agent_unknown',
                    destination: entree.destination || 'agent_unknown',
                    contenu_entree: entree.input || '',
                    contenu_sortie: entree.output || '',
                    qi_niveau: entree.qi_level || 0,
                    neurones_actifs: entree.active_neurons || 0,
                    temperature: entree.temperature || 0,
                    timestamp: entree.timestamp || maintenant,
                    statut: 'capture_automatique'
                });
            });
            
            this.conversationsCapturees += nouvellesEntrees.length;
        }
    }
    
    capturerDepuisLogs() {
        // 📋 Capture depuis les logs des processus
        try {
            // Simulation capture logs (à adapter selon tes logs réels)
            const logsRecents = this.lireLogsRecents();
            
            logsRecents.forEach(log => {
                if (this.estConversationImportante(log)) {
                    this.ajouterConversationPermanente({
                        type: 'log_conversation',
                        source: 'system_log',
                        contenu_entree: log.input || '',
                        contenu_sortie: log.output || '',
                        niveau_importance: this.evaluerImportance(log),
                        timestamp: log.timestamp || Date.now(),
                        statut: 'capture_log'
                    });
                }
            });
            
        } catch (error) {
            // Logs non disponibles - pas d'erreur critique
        }
    }
    
    capturerDepuisInterface() {
        // 🖥️ Capture conversations interface utilisateur
        // Cette fonction sera étendue pour capturer depuis ton interface
        
        const conversationsInterface = this.detecterConversationsInterface();
        
        conversationsInterface.forEach(conv => {
            this.ajouterConversationPermanente({
                type: 'interface_utilisateur',
                source: 'jean_luc',
                destination: 'agents',
                contenu_entree: conv.question || '',
                contenu_sortie: conv.reponse || '',
                timestamp: conv.timestamp || Date.now(),
                statut: 'interface_capture',
                preserve_meme_si_efface: true // 🔥 CLEF : Garder même si effacé interface
            });
        });
    }
    
    integrerConversationsDansMemoire() {
        try {
            console.log(`🧠 INTÉGRATION CONVERSATIONS → MÉMOIRE THERMIQUE`);
            
            const memory = this.lireMemoire();
            const conversations = this.lireConversationsPermanentes();
            
            if (!memory || !conversations) return;
            
            // 🔥 Intégration dans neural_system
            if (!memory.conversations_integrees) {
                memory.conversations_integrees = [];
            }
            
            // 📊 Analyse impact conversations sur QI
            const impactQI = this.calculerImpactConversationsSurQI(conversations);
            const impactNeurones = this.calculerImpactConversationsSurNeurones(conversations);
            
            if (impactQI > 0) {
                memory.neural_system.qi_level += impactQI;
                console.log(`🧠 Impact conversations sur QI: +${impactQI.toFixed(2)}`);
            }
            
            if (impactNeurones > 0) {
                memory.neural_system.active_neurons += impactNeurones;
                console.log(`🔗 Impact conversations sur neurones: +${impactNeurones}`);
            }
            
            // 💾 Intégration conversations récentes
            const conversationsRecentes = conversations.slice(-10); // 10 dernières
            memory.conversations_integrees = conversationsRecentes;
            
            // 🌡️ Ajustement température selon activité conversations
            if (!memory.neural_system.temperature) memory.neural_system.temperature = 37.0;
            const activiteConversations = this.calculerActiviteConversations(conversations);
            memory.neural_system.temperature = Math.min(
                (memory.neural_system.temperature || 37.0) + activiteConversations * 0.1,
                42.0 // Limite température
            );

            console.log(`🌡️ Température ajustée: ${(memory.neural_system.temperature || 37.0).toFixed(1)}°`);
            
            this.ecrireMemoire(memory);
            
        } catch (error) {
            console.error(`❌ Erreur intégration mémoire:`, error.message);
        }
    }
    
    calculerImpactConversationsSurQI(conversations) {
        let impact = 0;
        const maintenant = Date.now();
        
        conversations.forEach(conv => {
            // 📈 Impact basé sur complexité et récence
            const age = (maintenant - conv.timestamp) / (1000 * 60); // minutes
            const complexite = (conv.contenu_entree?.length || 0) + (conv.contenu_sortie?.length || 0);
            
            if (age < 60 && complexite > 50) { // Dernière heure, contenu substantiel
                impact += complexite * 0.001; // 0.001 QI par caractère
            }
        });
        
        return Math.min(impact, 1.0); // Maximum 1 point QI par cycle
    }
    
    calculerImpactConversationsSurNeurones(conversations) {
        let impact = 0;
        const maintenant = Date.now();
        
        conversations.forEach(conv => {
            const age = (maintenant - conv.timestamp) / (1000 * 60);
            
            if (age < 30) { // Dernières 30 minutes
                impact += 1; // 1 neurone par conversation récente
            }
        });
        
        return Math.min(impact, 10); // Maximum 10 neurones par cycle
    }
    
    calculerActiviteConversations(conversations) {
        const maintenant = Date.now();
        const conversationsRecentes = conversations.filter(conv => 
            (maintenant - conv.timestamp) < 300000 // 5 dernières minutes
        );
        
        return conversationsRecentes.length * 0.1; // 0.1° par conversation récente
    }
    
    ajouterConversationPermanente(conversation) {
        try {
            const conversations = this.lireConversationsPermanentes() || [];
            
            // 🔥 Ajout avec ID unique
            conversation.id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            conversation.preserve_permanent = true;
            
            conversations.push(conversation);
            
            // 🗂️ Limitation taille (garder 1000 dernières)
            if (conversations.length > 1000) {
                conversations.splice(0, conversations.length - 1000);
            }
            
            this.ecrireConversationsPermanentes(conversations);
            
        } catch (error) {
            console.error(`❌ Erreur ajout conversation:`, error.message);
        }
    }
    
    sauvegarderConversationsPermanentes() {
        const conversations = this.lireConversationsPermanentes();
        if (!conversations) return;
        
        console.log(`💾 SAUVEGARDE: ${conversations.length} conversations permanentes`);
        console.log(`📊 Total capturé: ${this.conversationsCapturees} conversations`);
        
        // 📈 Statistiques
        const typesConversations = {};
        conversations.forEach(conv => {
            typesConversations[conv.type] = (typesConversations[conv.type] || 0) + 1;
        });
        
        console.log(`📊 Types conversations:`, typesConversations);
    }
    
    analyserSemantique() {
        try {
            console.log(`🧠 ANALYSE SÉMANTIQUE DES CONVERSATIONS`);
            
            const conversations = this.lireConversationsPermanentes();
            if (!conversations || conversations.length === 0) return;
            
            // 🔍 Extraction mots-clés fréquents
            const motsCles = this.extraireMotsClesFrequents(conversations);
            
            // 🧠 Analyse patterns conversationnels
            const patterns = this.analyserPatternsConversationnels(conversations);
            
            console.log(`🔍 Mots-clés fréquents:`, motsCles.slice(0, 10));
            console.log(`🧠 Patterns détectés:`, patterns);
            
            // 💾 Sauvegarde analyse
            const analyse = {
                timestamp: Date.now(),
                mots_cles: motsCles,
                patterns: patterns,
                total_conversations: conversations.length
            };
            
            this.sauvegarderAnalyseSemantique(analyse);
            
        } catch (error) {
            console.error(`❌ Erreur analyse sémantique:`, error.message);
        }
    }
    
    // 🔧 FONCTIONS UTILITAIRES
    
    extraireNouvellesEntrees(memory, depuis) {
        // Extraction simplifiée - à adapter selon structure réelle
        const entrees = [];
        
        // Recherche dans différentes sections de la mémoire
        Object.keys(memory).forEach(key => {
            if (typeof memory[key] === 'object' && memory[key].timestamp > depuis) {
                entrees.push(memory[key]);
            }
        });
        
        return entrees;
    }
    
    lireLogsRecents() {
        // Simulation - à adapter selon tes logs réels
        return [];
    }
    
    detecterConversationsInterface() {
        // Simulation - à adapter selon ton interface
        return [];
    }
    
    estConversationImportante(log) {
        const motsClesImportants = ['agent', 'qi', 'evolution', 'neurone', 'intelligence'];
        const contenu = (log.message || '').toLowerCase();
        return motsClesImportants.some(mot => contenu.includes(mot));
    }
    
    evaluerImportance(log) {
        // Évaluation 1-10
        return Math.floor(Math.random() * 10) + 1;
    }
    
    extraireMotsClesFrequents(conversations) {
        const mots = {};
        
        conversations.forEach(conv => {
            const texte = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`;
            const motsTexte = texte.toLowerCase().match(/\b\w{4,}\b/g) || [];
            
            motsTexte.forEach(mot => {
                mots[mot] = (mots[mot] || 0) + 1;
            });
        });
        
        return Object.entries(mots)
            .sort(([,a], [,b]) => b - a)
            .map(([mot]) => mot);
    }
    
    analyserPatternsConversationnels(conversations) {
        return {
            conversations_par_heure: this.calculerConversationsParHeure(conversations),
            longueur_moyenne: this.calculerLongueurMoyenne(conversations),
            types_frequents: this.calculerTypesFrequents(conversations)
        };
    }
    
    calculerConversationsParHeure(conversations) {
        const maintenant = Date.now();
        const derniereHeure = conversations.filter(conv => 
            (maintenant - conv.timestamp) < 3600000
        );
        return derniereHeure.length;
    }
    
    calculerLongueurMoyenne(conversations) {
        if (conversations.length === 0) return 0;
        
        const longueurTotale = conversations.reduce((total, conv) => {
            return total + (conv.contenu_entree?.length || 0) + (conv.contenu_sortie?.length || 0);
        }, 0);
        
        return Math.floor(longueurTotale / conversations.length);
    }
    
    calculerTypesFrequents(conversations) {
        const types = {};
        conversations.forEach(conv => {
            types[conv.type] = (types[conv.type] || 0) + 1;
        });
        return types;
    }
    
    // 💾 GESTION FICHIERS
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }
    
    ecrireMemoire(memory) {
        try {
            fs.writeFileSync(this.memoryFile, JSON.stringify(memory, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur écriture mémoire:`, error.message);
            return false;
        }
    }
    
    lireConversationsPermanentes() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error(`❌ Erreur lecture conversations:`, error.message);
            return [];
        }
    }
    
    ecrireConversationsPermanentes(conversations) {
        try {
            fs.writeFileSync(this.conversationsFile, JSON.stringify(conversations, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur écriture conversations:`, error.message);
            return false;
        }
    }
    
    sauvegarderAnalyseSemantique(analyse) {
        try {
            const analyseFile = './analyse_semantique_conversations.json';
            fs.writeFileSync(analyseFile, JSON.stringify(analyse, null, 2));
        } catch (error) {
            console.error(`❌ Erreur sauvegarde analyse:`, error.message);
        }
    }
}

// 🚀 DÉMARRAGE CAPTURE CONVERSATIONS
const captureConversations = new MemoireThermiqueConversations();

console.log(`\n🔥 MÉMOIRE THERMIQUE CONVERSATIONS ACTIVE !`);
console.log(`📡 Capture ENTRÉE/SORTIE de toutes les conversations`);
console.log(`💾 Conversations effacées interface = GARDÉES en mémoire !`);
