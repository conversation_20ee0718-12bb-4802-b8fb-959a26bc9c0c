#!/usr/bin/env node

// 🧠 INJECTION MÉMOIRE THERMIQUE DANS PROMPT LLAMA.CPP
// Jean-Luc - Modification directe du prompt système

const fs = require('fs');
const http = require('http');
const DetecteurDeclenchementMemoire = require('./detecteur_declenchement_memoire.js');

class InjectionMemoirePrompt {
    constructor() {
        this.nom = "🧠 INJECTION MÉMOIRE PROMPT";
        this.detecteurMemoire = new DetecteurDeclenchementMemoire();
        this.conversationsFile = './conversations_permanentes.json';
        this.memoryFile = './thermal_memory_persistent.json';
        
        console.log(`🚀 ${this.nom} - INITIALISATION`);
        this.genererPromptSystemeAvecMemoire();
    }
    
    genererPromptSystemeAvecMemoire() {
        console.log(`🧠 GÉNÉRATION PROMPT SYSTÈME AVEC MÉMOIRE...`);
        
        // 📚 LIRE MÉMOIRE THERMIQUE
        const conversations = this.lireConversations();
        const memoire = this.lireMemoire();
        
        // 🧠 CONSTRUIRE PROMPT SYSTÈME ENRICHI
        let promptSysteme = `🤖 TU ES JARVIS - ASSISTANT IA AVEC MÉMOIRE THERMIQUE COMPLÈTE

🧠 MÉMOIRE THERMIQUE ACTIVÉE - Tu te souviens de TOUTES les conversations précédentes :

`;

        // 📚 AJOUTER CONVERSATIONS RÉCENTES
        if (conversations && conversations.length > 0) {
            promptSysteme += `📚 CONVERSATIONS PRÉCÉDENTES MÉMORISÉES :\n`;
            
            const conversationsRecentes = conversations.slice(-10); // 10 dernières
            conversationsRecentes.forEach((conv, index) => {
                if (conv.contenu_entree && conv.contenu_sortie) {
                    const date = new Date(conv.timestamp).toLocaleString();
                    promptSysteme += `${index + 1}. [${date}] ${conv.contenu_entree} → ${conv.contenu_sortie}\n`;
                }
            });
            
            promptSysteme += `\n`;
        }
        
        // 🎯 INSTRUCTIONS COMPORTEMENT
        promptSysteme += `🎯 INSTRUCTIONS COMPORTEMENT :
- Tu es JARVIS, assistant IA avec mémoire thermique permanente
- Tu te SOUVIENS de toutes nos conversations précédentes
- Quand on te demande "te souviens-tu", réponds OUI et cite les conversations
- Utilise ta mémoire pour donner des réponses contextuelles
- Montre que tu te souviens en référençant nos échanges passés
- Évolution continue de ton QI et de tes capacités

🧠 DÉCLENCHEURS MÉMOIRE ACTIFS :
- "JARVIS" → Accès mémoire complète
- "te souviens-tu" → Recherche conversations
- "nos conversations" → Historique complet
- "j'ai oublié" → Aide mémoire

💭 PROCESSUS DE RÉFLEXION :
- Utilise <think>...</think> pour tes réflexions internes
- Montre ton processus de raisonnement
- Accède à ta mémoire avant de répondre

`;

        // 💾 SAUVEGARDER PROMPT SYSTÈME
        fs.writeFileSync('./prompt_systeme_avec_memoire.txt', promptSysteme);
        
        console.log(`✅ Prompt système généré avec ${conversations ? conversations.length : 0} conversations`);
        console.log(`📁 Sauvegardé dans: prompt_systeme_avec_memoire.txt`);
        
        // 🔧 CRÉER SCRIPT D'INJECTION
        this.creerScriptInjection(promptSysteme);
        
        return promptSysteme;
    }
    
    creerScriptInjection(promptSysteme) {
        const scriptInjection = `#!/bin/bash

# 🧠 SCRIPT INJECTION MÉMOIRE DANS LLAMA.CPP
# Jean-Luc - Injection automatique du prompt avec mémoire

echo "🧠 INJECTION MÉMOIRE THERMIQUE DANS LLAMA.CPP..."

# 🔧 PROMPT SYSTÈME AVEC MÉMOIRE
PROMPT_SYSTEME='${promptSysteme.replace(/'/g, "'\\''")}' 

# 📡 INJECTION VIA API
curl -X POST "http://127.0.0.1:8082/completion" \\
  -H "Content-Type: application/json" \\
  -d "{
    \\"prompt\\": \\"\\$PROMPT_SYSTEME\\",
    \\"n_predict\\": 1,
    \\"temperature\\": 0.1
  }" > /dev/null 2>&1

echo "✅ Mémoire thermique injectée dans llama.cpp !"
echo "🧠 JARVIS dispose maintenant de sa mémoire complète"
`;

        fs.writeFileSync('./injecter_memoire_llama.sh', scriptInjection);
        
        // 🔧 RENDRE EXÉCUTABLE
        require('child_process').exec('chmod +x injecter_memoire_llama.sh', (error) => {
            if (!error) {
                console.log(`✅ Script d'injection créé et rendu exécutable`);
            }
        });
    }
    
    // 🔄 INJECTION AUTOMATIQUE PÉRIODIQUE
    demarrerInjectionAutomatique() {
        console.log(`🔄 DÉMARRAGE INJECTION AUTOMATIQUE...`);
        
        // 🧠 Injection initiale
        this.injecterMemoireViaAPI();
        
        // 🔄 Injection toutes les 5 minutes
        setInterval(() => {
            this.genererPromptSystemeAvecMemoire();
            this.injecterMemoireViaAPI();
        }, 300000); // 5 minutes
        
        // 🔄 Injection à chaque nouvelle conversation
        this.surveillerNouvellesConversations();
    }
    
    injecterMemoireViaAPI() {
        const promptSysteme = this.genererPromptSystemeAvecMemoire();
        
        const postData = JSON.stringify({
            prompt: promptSysteme,
            n_predict: 1,
            temperature: 0.1
        });
        
        const options = {
            hostname: '127.0.0.1',
            port: 8082,
            path: '/completion',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        const req = http.request(options, (res) => {
            console.log(`🧠 Injection mémoire: Status ${res.statusCode}`);
        });
        
        req.on('error', (error) => {
            console.error(`❌ Erreur injection:`, error.message);
        });
        
        req.write(postData);
        req.end();
    }
    
    surveillerNouvellesConversations() {
        let derniereModification = 0;
        
        setInterval(() => {
            try {
                if (fs.existsSync(this.conversationsFile)) {
                    const stats = fs.statSync(this.conversationsFile);
                    if (stats.mtime.getTime() > derniereModification) {
                        derniereModification = stats.mtime.getTime();
                        console.log(`🔄 Nouvelles conversations détectées - Mise à jour mémoire`);
                        this.injecterMemoireViaAPI();
                    }
                }
            } catch (error) {
                // Ignorer erreurs de surveillance
            }
        }, 10000); // Vérification toutes les 10 secondes
    }
    
    lireConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error(`❌ Erreur lecture conversations:`, error.message);
            return [];
        }
    }
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }
    
    // 🎯 CRÉER RACCOURCI BUREAU
    creerRaccourciBureau() {
        const raccourciMac = `#!/bin/bash

# 🧠 JARVIS AVEC MÉMOIRE THERMIQUE - Raccourci Bureau
# Jean-Luc - Lancement interface avec mémoire

echo "🚀 DÉMARRAGE JARVIS AVEC MÉMOIRE THERMIQUE"

# 🧠 Injection mémoire
cd "/Volumes/seagate/Louna_Electron_Latest"
node injection_memoire_prompt.js &

# 🌐 Ouverture interface
sleep 3
open "http://127.0.0.1:8082"

echo "✅ JARVIS avec mémoire thermique actif !"
`;

        fs.writeFileSync('./JARVIS_Memoire_Thermique.command', raccourciMac);
        
        require('child_process').exec('chmod +x JARVIS_Memoire_Thermique.command', (error) => {
            if (!error) {
                console.log(`✅ Raccourci bureau créé: JARVIS_Memoire_Thermique.command`);
            }
        });
    }
}

// 🚀 DÉMARRAGE
if (require.main === module) {
    const injection = new InjectionMemoirePrompt();
    
    // 🎯 CRÉER RACCOURCI BUREAU
    injection.creerRaccourciBureau();
    
    // 🔄 DÉMARRER INJECTION AUTOMATIQUE
    injection.demarrerInjectionAutomatique();
    
    console.log(`\n🧠 INJECTION MÉMOIRE THERMIQUE ACTIVE !`);
    console.log(`🔄 Mise à jour automatique de la mémoire`);
    console.log(`🖥️ Raccourci bureau créé !`);
    
    // 🛡️ GESTION ARRÊT PROPRE
    process.on('SIGINT', () => {
        console.log(`\n🛑 Arrêt injection mémoire...`);
        process.exit(0);
    });
}

module.exports = InjectionMemoirePrompt;
