#!/bin/bash

# 🧠 REDÉMARRAGE LLAMA.CPP AVEC MÉMOIRE THERMIQUE
# <PERSON><PERSON><PERSON> <PERSON> <PERSON>ript pour connecter l'interface à la mémoire

echo "🔄 REDÉMARRAGE LLAMA.CPP AVEC MÉMOIRE THERMIQUE"

# 🛑 Arrêter llama-server actuel
echo "🛑 Arrêt llama-server sur port 8080..."
pkill -f "llama-server.*8080" || echo "Aucun processus llama-server trouvé"

# ⏳ Attendre arrêt complet
sleep 2

# 🚀 Redémarrer llama-server sur port 8082 (pour éviter conflit)
echo "🚀 Démarrage llama-server sur port 8082..."
llama-server -m "/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf" \
    --host 127.0.0.1 \
    --port 8082 \
    --ctx-size 4096 \
    --threads 4 &

# ⏳ Attendre démarrage
sleep 5

echo "✅ Configuration terminée !"
echo "🧠 Interface avec mémoire : http://127.0.0.1:8081"
echo "🔗 Llama.cpp direct : http://127.0.0.1:8082"
echo "🧠 Middleware mémoire actif sur port 8081"
