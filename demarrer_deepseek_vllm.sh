#!/bin/bash

# 🤖 DÉMARRAGE DEEPSEEK R1 8B VIA VLLM
# Script pour lancer le modèle DeepSeek R1 avec VLLM

echo "🚀 DÉMARRAGE DEEPSEEK R1 8B VIA VLLM"
echo "===================================="

# Vérifier si VLLM est installé
if ! command -v vllm &> /dev/null; then
    echo "❌ VLLM n'est pas installé !"
    echo "💡 Installation requise:"
    echo "   pip install vllm"
    echo "   ou"
    echo "   pip install vllm[cuda] # pour GPU CUDA"
    exit 1
fi

echo "✅ VLLM trouvé: $(which vllm)"

# Vérifier si le port 8000 est libre
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ Port 8000 déjà utilisé"
    echo "🛑 Arrêt du processus existant..."
    pkill -f "vllm.*8000" || echo "Aucun processus VLLM trouvé"
    sleep 2
fi

# Activer l'environnement virtuel si disponible
if [ -d "venv_deepseek" ]; then
    echo "🔄 Activation environnement virtuel..."
    source venv_deepseek/bin/activate
    echo "✅ Environnement virtuel activé"
fi

echo "🧠 Démarrage du modèle DeepSeek R1 8B..."
echo "📡 Port: 8000"
echo "🔗 URL: http://localhost:8000"
echo ""
echo "⏳ Chargement du modèle (peut prendre quelques minutes)..."
echo ""

# Démarrer VLLM avec DeepSeek R1
vllm serve "deepseek-ai/DeepSeek-R1-0528" \
    --host 0.0.0.0 \
    --port 8000 \
    --max-model-len 4096 \
    --dtype auto \
    --api-key EMPTY \
    --served-model-name "deepseek-r1" \
    --trust-remote-code

echo ""
echo "🛑 VLLM arrêté"
