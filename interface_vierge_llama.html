<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦙 llama.cpp - chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        .user-message {
            background: #e3f2fd;
            margin-left: 50px;
        }
        .assistant-message {
            background: #f3e5f5;
            margin-right: 50px;
        }
        .input-container {
            padding: 20px;
            display: flex;
            gap: 10px;
        }
        #messageInput {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        #sendButton {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        #sendButton:hover {
            background: #0056b3;
        }
        #sendButton:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
        }
        .loading {
            display: none;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦙 llama.cpp</h1>
            <p>Chat with your local LLM</p>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="message assistant-message">
                <strong>Assistant:</strong> Hello! I'm ready to chat. How can I help you today?
            </div>
        </div>
        
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message here..." />
            <button id="sendButton">Send</button>
        </div>
        
        <div class="status">
            <span id="statusText">Ready</span>
            <span class="loading" id="loadingText">Generating response...</span>
        </div>
    </div>

    <script>
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusText = document.getElementById('statusText');
        const loadingText = document.getElementById('loadingText');

        // Configuration
        const API_URL = 'http://localhost:8000/completion';

        // Add message to chat
        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
            messageDiv.innerHTML = `<strong>${isUser ? 'You' : 'Assistant'}:</strong> ${content}`;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Send message to API
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, true);
            messageInput.value = '';
            
            // Show loading
            sendButton.disabled = true;
            statusText.style.display = 'none';
            loadingText.style.display = 'inline';

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: message,
                        n_predict: 256,
                        temperature: 0.7,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const reply = data.content || data.text || 'No response';
                
                // Add assistant response
                addMessage(reply);
                
            } catch (error) {
                console.error('Error:', error);
                addMessage(`Error: ${error.message}`, false);
            } finally {
                // Hide loading
                sendButton.disabled = false;
                statusText.style.display = 'inline';
                loadingText.style.display = 'none';
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Test connection on load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    statusText.textContent = 'Connected to llama.cpp server';
                    statusText.style.color = 'green';
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                statusText.textContent = 'Server not connected - check localhost:8000';
                statusText.style.color = 'red';
            }
        });
    </script>
</body>
</html>
