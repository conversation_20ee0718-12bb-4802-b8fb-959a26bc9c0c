#!/usr/bin/env node

// 🤖 AGENT 1 PRINCIPAL - INTELLIGENCE ÉVOLUTIVE
// ⚡ CONNEXION DIRECTE - PAS D'OLLAMA ⚡
// 🧠 GESTION MÉMOIRE À 100% + COEFFICIENT INTELLECTUEL ÉVOLUTIF
// 🔗 BRANCHÉ À LA MÉMOIRE THERMIQUE
// 🌡️ COMMUNICATION AVEC MOTEUR THERMIQUE
// ⚡ ACCÉLÉRATEURS EN CASCADE
// 🧬 CODE VIVANT ADAPTATIF

// ⚡ PAS D'AXIOS - CONNEXION DIRECTE SEULEMENT ⚡
const fs = require('fs');
const os = require('os');

class AgentPrincipalIntelligent {
    constructor() {
        this.agentId = 'PRINCIPAL';
        this.modelName = 'deepseek-r1:8b-llama-distill-q4_K_M';
        
        // 🧠 COEFFICIENT INTELLECTUEL ÉVOLUTIF
        this.coefficientIntellectuel = 1.0;
        this.experienceAccumulee = 0;
        this.niveauApprentissage = 'DÉBUTANT';
        
        // 🌡️ CONNEXIONS
        this.memoryFile = '/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/thermal_memory_simple.json';
        this.moteurThermiqueWs = null;
        this.interfaceWs = null;
        
        // ⚡ ACCÉLÉRATEURS EN CASCADE
        this.accelerateurs = [];
        this.maxAccelerateurs = this.detecterCapaciteMachine();
        
        // 🧬 CODE VIVANT ADAPTATIF
        this.adaptations = {};
        this.metriques = {
            tempsReponse: [],
            qualiteReponses: [],
            utilisationMemoire: []
        };
        
        console.log(`🤖 AGENT PRINCIPAL INTELLIGENT - DeepSeek R1 8B (4.9GB)`);
        console.log(`⚡ PAS D'OLLAMA - CONNEXION DIRECTE SEULEMENT`);
        console.log(`🧠 Coefficient Intellectuel Initial: ${this.coefficientIntellectuel}`);
        console.log(`⚡ Accélérateurs Max: ${this.maxAccelerateurs}`);
        
        this.initialiser();
    }

    async initialiser() {
        try {
            // 🌡️ Connexion mémoire thermique
            await this.connecterMemoireThermique();

            // ⚡ Initialiser accélérateurs
            this.initialiserAccelerateurs();

            // 🧬 Démarrer adaptation continue
            this.demarrerAdaptationContinue();

            // 📊 Démarrer monitoring
            this.demarrerMonitoring();

            console.log(`✅ Agent Principal initialisé - Prêt à évoluer !`);

        // 🔌 CONNEXION DIRECTE : Méthode pour recevoir questions d'Agent 2
        this.recevoirDeAgent2 = (question) => {
            console.log(`🔌 CONNEXION DIRECTE: Agent Principal reçoit d'Agent 2: "${question}"`);
            this.traiterQuestionDirecte(question);
        };

            // 🤖 CORRECTION CRITIQUE : Connexion moteur thermique IMMÉDIATE
            await this.connecterMoteurThermique();

        } catch (error) {
            console.error(`❌ Erreur initialisation Agent Principal:`, error.message);
        }
    }

    detecterCapaciteMachine() {
        const totalMem = os.totalmem() / (1024 * 1024 * 1024); // GB
        const cpus = os.cpus().length;
        
        // Calcul adaptatif selon la machine
        let maxAccel = Math.floor(cpus / 2);
        if (totalMem > 16) maxAccel += 2;
        if (totalMem > 32) maxAccel += 2;
        
        console.log(`🖥️ Machine détectée: ${totalMem.toFixed(1)}GB RAM, ${cpus} CPUs`);
        console.log(`⚡ Accélérateurs recommandés: ${maxAccel}`);
        
        return Math.min(maxAccel, 8); // Limite sécurité
    }

    async connecterMemoireThermique() {
        // 🎯 SIMPLE : Accès DIRECT au fichier - PAS DE SERVEUR HTTP !
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                const memory = JSON.parse(data);
                console.log(`🌡️ Agent Principal connecté DIRECTEMENT à la mémoire thermique`);
                console.log(`📊 Mémoire chargée: ${Object.keys(memory.thermal_zones || {}).length} zones`);
                return;
            } else {
                console.log(`⚠️ Fichier mémoire thermique non trouvé, création...`);
                this.creerMemoireVide();
            }
        } catch (error) {
            console.error(`❌ Erreur accès direct mémoire:`, error.message);
        }
    }

    // 📝 MÉTHODE SIMPLE : Lire mémoire directement
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }

    // 💾 MÉTHODE SIMPLE : Écrire mémoire directement
    ecrireMemoire(memory) {
        try {
            fs.writeFileSync(this.memoryFile, JSON.stringify(memory, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur écriture mémoire:`, error.message);
            return false;
        }
    }

    // 🔍 MÉTHODE SIMPLE : Chercher dans mémoire directement
    chercherMemoire(query, limit = 10) {
        const memory = this.lireMemoire();
        if (!memory) return [];

        const results = [];

        // 🎯 SIMPLE : Le fichier utilise des clés numériques directement
        Object.values(memory).forEach(entry => {
            if (entry && entry.content) {
                if (!query || entry.content.toLowerCase().includes(query.toLowerCase()) ||
                    (entry.type && entry.type.toLowerCase().includes(query.toLowerCase()))) {
                    results.push({
                        content: entry.content,
                        timestamp: entry.timestamp,
                        type: entry.type,
                        id: entry.id
                    });
                }
            }
        });

        // Trier par timestamp
        results.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
        return results.slice(0, limit);
    }

    // ➕ MÉTHODE SIMPLE : Ajouter à mémoire directement
    ajouterMemoire(contenu, type = 'agent_principal') {
        const memory = this.lireMemoire();
        if (!memory) return false;

        const nouvelleEntree = {
            id: Date.now(),
            content: contenu,
            timestamp: Date.now(),
            type: type,
            source: 'AGENT_PRINCIPAL'
        };

        // 🎯 SIMPLE : Ajouter directement avec clé numérique
        const nextKey = Object.keys(memory).length;
        memory[nextKey] = nouvelleEntree;

        return this.ecrireMemoire(memory);
    }

    // 🆕 MÉTHODE SIMPLE : Créer mémoire vide si fichier n'existe pas
    creerMemoireVide() {
        const memoireVide = {
            thermal_zones: {
                zone1_working: { entries: [] },
                zone2_analysis: { entries: [] },
                zone3_storage: { entries: [] }
            },
            metadata: {
                created: Date.now(),
                version: "1.0",
                agent_principal: true
            }
        };

        return this.ecrireMemoire(memoireVide);
    }

    // 🔌 NOUVELLE MÉTHODE : Traiter question directe d'Agent 2
    async traiterQuestionDirecte(question) {
        try {
            console.log(`🔌 TRAITEMENT DIRECT: "${question}"`);

            // Traiter comme une question normale
            const reponse = await this.genererReponseIntelligente(question, []);

            // Envoyer réponse directement à Agent 2
            if (this.envoyerVersAgent2) {
                console.log(`📤 CONNEXION DIRECTE: Agent Principal → Agent 2`);
                this.envoyerVersAgent2(reponse);
            }

            // Évoluer
            this.evoluer();

        } catch (error) {
            console.error(`❌ Erreur traitement direct:`, error.message);
        }
    }

    // 📊 NOUVELLE MÉTHODE : Monitoring de santé des connexions
    demarrerMonitoringSante() {
        console.log(`📊 Monitoring de santé des connexions démarré`);

        // Vérifier santé toutes les 30 secondes
        setInterval(async () => {
            await this.verifierSanteConnexions();
        }, 30000);

        // Statistiques détaillées toutes les 60 secondes
        setInterval(() => {
            this.afficherStatistiquesConnexions();
        }, 60000);
    }

    // 🏥 NOUVELLE MÉTHODE : Vérification santé (ACCÈS DIRECT)
    async verifierSanteConnexions() {
        try {
            const debut = Date.now();
            // 🎯 ACCÈS DIRECT au fichier - PAS de serveur HTTP
            const memory = this.lireMemoire();
            const latence = Date.now() - debut;

            // Stocker métriques de santé
            this.metriques.latence = this.metriques.latence || [];
            this.metriques.latence.push(latence);

            // Garder seulement les 100 dernières mesures
            if (this.metriques.latence.length > 100) {
                this.metriques.latence = this.metriques.latence.slice(-100);
            }

            // Alertes si problèmes
            if (latence > 5000) {
                console.warn(`⚠️ ALERTE : Latence élevée (${latence}ms)`);
            }

            const memoryCount = memory ? Object.keys(memory).length : 0;
            if (memoryCount < 10) {
                console.warn(`⚠️ ALERTE : Peu d'éléments en mémoire (${memoryCount})`);
            }

        } catch (error) {
            console.error(`🚨 ALERTE CRITIQUE : Perte de connexion mémoire thermique !`);
            console.error(`🔄 Tentative de reconnexion...`);

            // Tentative de reconnexion automatique
            setTimeout(async () => {
                await this.connecterMemoireThermique();
            }, 5000);
        }
    }

    // 📈 NOUVELLE MÉTHODE : Statistiques connexions
    afficherStatistiquesConnexions() {
        const latenceMoyenne = this.metriques.latence && this.metriques.latence.length > 0
            ? this.metriques.latence.reduce((a, b) => a + b) / this.metriques.latence.length
            : 0;

        console.log(`📊 SANTÉ CONNEXIONS - Latence moyenne: ${latenceMoyenne.toFixed(1)}ms`);
        console.log(`🧠 Coefficient: ${this.coefficientIntellectuel.toFixed(4)} | Expérience: ${this.experienceAccumulee}`);
        console.log(`⚡ Accélérateurs actifs: ${this.accelerateurs.filter(a => a.actif).length}/${this.maxAccelerateurs}`);
    }

    async connecterMoteurThermique() {
        try {
            // CONNEXION DIRECTE COMME LES TESTS - PAS DE WEBSOCKETS
            console.log(`🔗 Connexion directe via mémoire thermique (comme les tests)`);

            // Démarrer surveillance des questions (comme mes tests)
            this.demarrerSurveillanceQuestions();

            // Démarrer surveillance des messages du moteur thermique
            this.demarrerSurveillanceMoteurThermique();

            console.log(`✅ Surveillance autonome démarrée - Agent prêt à traiter questions !`);

        } catch (error) {
            console.error(`❌ Erreur connexion moteur thermique:`, error.message);
            throw error;
        }
    }

    demarrerSurveillanceQuestions() {
        console.log(`👁️ Surveillance ULTRA-RAPIDE des questions démarrée (méthode directe)`);

        // ⚡ AMÉLIORATION : Surveillance toutes les 1 seconde (au lieu de 2)
        setInterval(async () => {
            await this.verifierNouvellesQuestionsAvecRetry();
        }, 1000);
    }

    demarrerSurveillanceMoteurThermique() {
        console.log(`👁️ Surveillance ULTRA-RAPIDE Moteur Thermique démarrée (méthode directe)`);

        // ⚡ AMÉLIORATION : Surveillance toutes les 1.5 secondes (au lieu de 3)
        setInterval(async () => {
            await this.verifierMessagesMoteurThermiqueAvecRetry();
        }, 1500);
    }

    // 🔄 NOUVELLE MÉTHODE : Retry automatique pour questions
    async verifierNouvellesQuestionsAvecRetry() {
        const maxRetries = 3;
        for (let tentative = 1; tentative <= maxRetries; tentative++) {
            try {
                await this.verifierNouvellesQuestions();
                return; // Succès, sortir
            } catch (error) {
                console.error(`❌ Tentative ${tentative}/${maxRetries} échouée:`, error.message);
                if (tentative < maxRetries) {
                    // Délai progressif : 500ms, 1000ms, 1500ms
                    await this.sleep(tentative * 500);
                } else {
                    console.error(`💥 Échec définitif après ${maxRetries} tentatives`);
                }
            }
        }
    }

    // 🔄 NOUVELLE MÉTHODE : Retry automatique pour moteur thermique
    async verifierMessagesMoteurThermiqueAvecRetry() {
        const maxRetries = 3;
        for (let tentative = 1; tentative <= maxRetries; tentative++) {
            try {
                await this.verifierMessagesMoteurThermique();
                return; // Succès, sortir
            } catch (error) {
                console.error(`❌ Tentative ${tentative}/${maxRetries} échouée:`, error.message);
                if (tentative < maxRetries) {
                    await this.sleep(tentative * 500);
                } else {
                    console.error(`💥 Échec définitif après ${maxRetries} tentatives`);
                }
            }
        }
    }

    // 🛠️ MÉTHODE UTILITAIRE : Sleep
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async verifierNouvellesQuestions() {
        // 📝 AMÉLIORATION : Marquage robuste avec verrous
        const verrou = `agent_principal_${Date.now()}`;

        try {
            // 🎯 SIMPLE : Chercher DIRECTEMENT dans le fichier (pas HTTP)
            const questionsUser = this.chercherMemoire('user_question_interface', 3);
            const questionsAgent2Direct = this.chercherMemoire('question_agent2_vers_agent1', 3);
            const questionsViaAgent2 = this.chercherMemoire('question_via_agent2', 3);
            const questionsReveil = this.chercherMemoire('question_reveil_agent2', 3);

            const maintenant = Date.now();

            // 🔲 CARRÉ AVEC PRIORITÉ : Jean-Luc d'abord, puis Agent 2

            // 🎯 SIMPLE : Filtrer directement les résultats
            // PRIORITÉ 1 : Questions de Jean-Luc (intervention humaine)
            const questionsHumaines = questionsUser.filter(item =>
                item.type === 'user_question_interface' &&
                (maintenant - item.timestamp) < 15000 &&
                !item.traite_par_agent_principal
            );

            // PRIORITÉ 2 : Questions d'Agent 2 (dialogue automatique)
            const questionsAgent2 = [
                ...questionsAgent2Direct.filter(item =>
                    item.type === 'question_agent2_vers_agent1' &&
                    (maintenant - item.timestamp) < 15000 &&
                    !item.traite_par_agent_principal
                ),
                ...questionsViaAgent2.filter(item =>
                    item.type === 'question_via_agent2' &&
                    (maintenant - item.timestamp) < 15000 &&
                    !item.traite_par_agent_principal
                ),
                ...questionsReveil.filter(item =>
                    item.type === 'question_reveil_agent2' &&
                    (maintenant - item.timestamp) < 15000 &&
                    !item.traite_par_agent_principal
                )
            ];

            // 🔲 LOGIQUE DU CARRÉ : Si Jean-Luc intervient, Agent 2 attend
            let toutesQuestions = [];
            let modeCarré = "AUTOMATIQUE";

            if (questionsHumaines.length > 0) {
                // 👨‍💻 JEAN-LUC INTERVIENT → PRIORITÉ ABSOLUE → Agent 2 en attente
                // 🎯 CONNEXION AVEC FACE : Mode MPC + Recherche Internet activés
                toutesQuestions = questionsHumaines;
                modeCarré = "INTERVENTION_HUMAINE";
                console.log(`✋ CARRÉ COUPÉ : Jean-Luc intervient - Agent 2 en attente`);
                console.log(`🎯 CONNEXION FACE ACTIVÉE : Mode MPC + Recherche Internet disponibles`);

                // Activer les fonctions spéciales pour Jean-Luc
                this.activerFonctionsFace();

            } else if (questionsAgent2.length > 0) {
                // 🤖 Pas d'intervention → Dialogue automatique Agent 1 ↔ Agent 2
                toutesQuestions = questionsAgent2;
                modeCarré = "DIALOGUE_AUTOMATIQUE";
                console.log(`🔄 CARRÉ ACTIF : Dialogue automatique Agent 1 ↔ Agent 2`);

                // Désactiver les fonctions spéciales (mode normal)
                this.desactiverFonctionsFace();
            }

            const questionsRecentes = toutesQuestions;

            // 🔲 INFORMER AGENT 2 DU MODE CARRÉ
            if (modeCarré === "INTERVENTION_HUMAINE" && questionsAgent2.length > 0) {
                await this.informerAgent2Attente();
            } else if (modeCarré === "DIALOGUE_AUTOMATIQUE") {
                await this.informerAgent2Reprise();
            }

            for (const questionItem of questionsRecentes) {
                // 🔒 AMÉLIORATION : Verrouiller la question avant traitement
                const verrouillageReussi = await this.verrouillerQuestion(questionItem.id, verrou);

                if (verrouillageReussi) {
                    console.log(`🔒 Question verrouillée: "${questionItem.content}"`);

                    try {
                        await this.traiterQuestionPrincipale(questionItem.content);

                        // ✅ AMÉLIORATION : Marquage robuste avec timestamp
                        await this.marquerQuestionTraitee(questionItem.id, verrou);

                    } catch (error) {
                        console.error(`❌ Erreur traitement question:`, error.message);
                        // 🔓 Libérer le verrou en cas d'erreur
                        await this.libererVerrouQuestion(questionItem.id, verrou);
                    }
                } else {
                    console.log(`⏭️ Question déjà en cours de traitement: "${questionItem.content}"`);
                }
            }

        } catch (error) {
            console.error(`❌ Erreur vérification questions:`, error.message);
            throw error; // Propager l'erreur pour le retry
        }
    }

    // 🔒 MÉTHODE DIRECTE : Verrouillage dans fichier
    async verrouillerQuestion(questionId, verrou) {
        try {
            const memory = this.lireMemoire();
            if (!memory) return false;

            const nextKey = Object.keys(memory).length;
            memory[nextKey] = {
                type: 'verrou_question',
                question_id: questionId,
                verrou: verrou,
                agent: 'AGENT_PRINCIPAL',
                timestamp: Date.now(),
                expire_at: Date.now() + 30000
            };

            return this.ecrireMemoire(memory);
        } catch (error) {
            console.error(`❌ Erreur verrouillage:`, error.message);
            return false;
        }
    }

    // ✅ MÉTHODE DIRECTE : Marquage dans fichier
    async marquerQuestionTraitee(questionId, verrou) {
        try {
            const memory = this.lireMemoire();
            if (!memory) return false;

            const nextKey = Object.keys(memory).length;
            memory[nextKey] = {
                type: 'question_traitee_robuste',
                question_id: questionId,
                verrou: verrou,
                traite_par: 'AGENT_PRINCIPAL',
                traite_at: Date.now(),
                coefficient_au_traitement: this.coefficientIntellectuel
            };

            this.ecrireMemoire(memory);
            console.log(`✅ Question marquée comme traitée (ID: ${questionId})`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur marquage:`, error.message);
            return false;
        }
    }

    // 🔓 NOUVELLE MÉTHODE : Libération de verrou
    async libererVerrouQuestion(questionId, verrou) {
        try {
            const memory = this.lireMemoire();
            if (memory) {
                const nextKey = Object.keys(memory).length;
                memory[nextKey] = {
                    type: 'liberation_verrou',
                    question_id: questionId,
                    verrou: verrou,
                    libere_par: 'AGENT_PRINCIPAL',
                    libere_at: Date.now()
                };
                this.ecrireMemoire(memory);
            }
        } catch (error) {
            console.error(`❌ Erreur libération verrou:`, error.message);
        }
    }

    // 🔲 NOUVELLE MÉTHODE : Informer Agent 2 d'attendre (Jean-Luc intervient)
    async informerAgent2Attente() {
        try {
            // 🎯 SIMPLE : Stockage DIRECT dans le fichier
            const success = this.ajouterMemoire(JSON.stringify({
                type: 'signal_agent2_attente',
                message: 'Jean-Luc intervient - Agent 2 en attente',
                mode_carre: 'INTERVENTION_HUMAINE',
                source: 'AGENT_PRINCIPAL',
                timestamp: Date.now()
            }), 'signal_agent2_attente');

            if (success) {
                console.log(`📢 Signal envoyé à Agent 2 : ATTENTE (Jean-Luc intervient)`);
            }
        } catch (error) {
            console.error(`❌ Erreur signal attente:`, error.message);
        }
    }

    // 🔄 NOUVELLE MÉTHODE : Informer Agent 2 de reprendre (dialogue automatique)
    async informerAgent2Reprise() {
        try {
            // 🎯 SIMPLE : Stockage DIRECT dans le fichier
            const success = this.ajouterMemoire(JSON.stringify({
                type: 'signal_agent2_reprise',
                message: 'Dialogue automatique - Agent 2 peut reprendre',
                mode_carre: 'DIALOGUE_AUTOMATIQUE',
                source: 'AGENT_PRINCIPAL',
                timestamp: Date.now()
            }), 'signal_agent2_reprise');

            if (success) {
                console.log(`📢 Signal envoyé à Agent 2 : REPRISE (dialogue automatique)`);
            }
        } catch (error) {
            console.error(`❌ Erreur signal reprise:`, error.message);
        }
    }

    // 🎯 NOUVELLE MÉTHODE : Activer fonctions Face (Jean-Luc intervient)
    activerFonctionsFace() {
        this.modeFaceActif = true;
        this.fonctionsMPC = true;
        this.rechercheInternetActive = true;

        console.log(`🎯 FONCTIONS FACE ACTIVÉES :`);
        console.log(`   - Mode MPC : ✅ ACTIF`);
        console.log(`   - Recherche Internet : ✅ ACTIVE`);
        console.log(`   - Toutes interventions Jean-Luc : ✅ DISPONIBLES`);
    }

    // 🤖 NOUVELLE MÉTHODE : Désactiver fonctions Face (mode automatique)
    desactiverFonctionsFace() {
        this.modeFaceActif = false;
        this.fonctionsMPC = false;
        this.rechercheInternetActive = false;

        console.log(`🤖 MODE AUTOMATIQUE : Fonctions Face désactivées`);
    }

    async verifierMessagesMoteurThermique() {
        try {
            // 🎯 ACCÈS DIRECT au fichier - PAS de serveur HTTP
            const memory = this.lireMemoire();
            if (!memory) return;

            const maintenant = Date.now();
            const messagesRecents = Object.values(memory).filter(item =>
                (item.type === 'validation_thermique' || item.type === 'boost_coefficient') &&
                (maintenant - item.timestamp) < 15000 && // Dernières 15 secondes
                !item.traite_par_agent_principal
            );

            for (const message of messagesRecents) {
                this.traiterMessageMoteurThermique(message);
            }

        } catch (error) {
            // Erreur silencieuse
        }
    }

    initialiserAccelerateurs() {
        console.log(`⚡ Initialisation ${this.maxAccelerateurs} accélérateurs...`);
        
        for (let i = 0; i < this.maxAccelerateurs; i++) {
            this.accelerateurs.push({
                id: i,
                actif: false,
                charge: 0,
                specialisation: this.determinerSpecialisation(i),
                performance: 1.0
            });
        }
        
        console.log(`✅ Accélérateurs initialisés en cascade`);
    }

    determinerSpecialisation(index) {
        const specialisations = [
            'ANALYSE_RAPIDE',
            'RECHERCHE_MEMOIRE',
            'GENERATION_REPONSE',
            'VALIDATION_QUALITE',
            'OPTIMISATION_CONTEXTE',
            'APPRENTISSAGE_CONTINU',
            'ADAPTATION_DYNAMIQUE',
            'MONITORING_PERFORMANCE'
        ];
        
        return specialisations[index % specialisations.length];
    }

    demarrerAdaptationContinue() {
        console.log(`🧬 Code vivant adaptatif démarré`);
        
        setInterval(() => {
            this.analyserPerformances();
            this.adapterComportement();
            this.optimiserAccelerateurs();
        }, 30000); // Toutes les 30 secondes
    }

    demarrerMonitoring() {
        console.log(`📊 Monitoring système démarré`);
        
        setInterval(() => {
            this.collecterMetriques();
            this.evaluerCoefficient();
        }, 10000); // Toutes les 10 secondes
    }

    async traiterMiseAJourMemoire(update) {
        console.log(`🌡️ Agent Principal - Mise à jour mémoire: ${update.type}`);
        
        // Augmenter expérience
        this.experienceAccumulee += 1;
        
        // Déclencher accélérateur spécialisé
        const accelerateur = this.obtenirAccelerateurLibre('ANALYSE_RAPIDE');
        if (accelerateur) {
            this.activerAccelerateur(accelerateur, update);
        }
    }

    traiterMessageMoteurThermique(data) {
        console.log(`🌡️ Agent Principal reçu du Moteur Thermique: ${data.type}`);
        
        switch (data.type) {
            case 'validation_reponse':
                this.traiterValidation(data);
                break;
            case 'temperature_update':
                this.ajusterTemperature(data.temperature);
                break;
            case 'coefficient_boost':
                this.boosterCoefficient(data.boost);
                break;
        }
    }

    traiterValidation(data) {
        if (data.valide) {
            this.coefficientIntellectuel += 0.01;
            console.log(`🧠 Coefficient intellectuel augmenté: ${this.coefficientIntellectuel.toFixed(3)}`);
        }
        
        // Stocker validation en mémoire
        this.stockerEnMemoire({
            type: 'validation_apprentissage',
            coefficient: this.coefficientIntellectuel,
            qualite: data.qualite || 'unknown'
        });
    }

    boosterCoefficient(boost) {
        this.coefficientIntellectuel += boost;
        console.log(`🚀 Coefficient intellectuel boosté: ${this.coefficientIntellectuel.toFixed(3)}`);
        
        // Adapter niveau d'apprentissage
        if (this.coefficientIntellectuel > 2.0) {
            this.niveauApprentissage = 'EXPERT';
        } else if (this.coefficientIntellectuel > 1.5) {
            this.niveauApprentissage = 'AVANCÉ';
        } else if (this.coefficientIntellectuel > 1.2) {
            this.niveauApprentissage = 'INTERMÉDIAIRE';
        }
    }

    obtenirAccelerateurLibre(specialisation) {
        return this.accelerateurs.find(acc => 
            !acc.actif && 
            (acc.specialisation === specialisation || !specialisation)
        );
    }

    activerAccelerateur(accelerateur, tache) {
        accelerateur.actif = true;
        accelerateur.charge = 1.0;
        
        console.log(`⚡ Accélérateur ${accelerateur.id} (${accelerateur.specialisation}) activé`);
        
        // Traitement accéléré
        setTimeout(() => {
            this.terminerAccelerateur(accelerateur, tache);
        }, 1000 / this.coefficientIntellectuel); // Plus rapide avec coefficient élevé
    }

    terminerAccelerateur(accelerateur, tache) {
        accelerateur.actif = false;
        accelerateur.charge = 0;
        accelerateur.performance += 0.01; // Amélioration continue
        
        console.log(`✅ Accélérateur ${accelerateur.id} terminé - Performance: ${accelerateur.performance.toFixed(3)}`);
    }

    analyserPerformances() {
        const moyenneTemps = this.metriques.tempsReponse.length > 0 
            ? this.metriques.tempsReponse.reduce((a, b) => a + b) / this.metriques.tempsReponse.length 
            : 0;
            
        console.log(`📊 Performance moyenne: ${moyenneTemps.toFixed(2)}ms`);
        
        // Adaptation automatique
        if (moyenneTemps > 5000) { // Si trop lent
            this.optimiserAccelerateurs();
        }
    }

    adapterComportement() {
        // Code vivant - adaptation selon les métriques
        const adaptation = {
            timestamp: Date.now(),
            coefficient: this.coefficientIntellectuel,
            accelerateurs_actifs: this.accelerateurs.filter(a => a.actif).length,
            niveau: this.niveauApprentissage
        };
        
        this.adaptations[Date.now()] = adaptation;
        
        // Nettoyer anciennes adaptations
        const keys = Object.keys(this.adaptations);
        if (keys.length > 100) {
            delete this.adaptations[keys[0]];
        }
    }

    optimiserAccelerateurs() {
        console.log(`⚡ Optimisation accélérateurs en cascade...`);
        
        // Répartir la charge selon les performances
        this.accelerateurs.forEach(acc => {
            if (acc.performance < 1.0) {
                acc.performance = Math.min(acc.performance + 0.05, 2.0);
            }
        });
    }

    collecterMetriques() {
        const memUsage = process.memoryUsage();
        
        this.metriques.utilisationMemoire.push(memUsage.heapUsed / 1024 / 1024);
        
        // Garder seulement les 100 dernières métriques
        Object.keys(this.metriques).forEach(key => {
            if (this.metriques[key].length > 100) {
                this.metriques[key] = this.metriques[key].slice(-100);
            }
        });
    }

    evaluerCoefficient() {
        // Évaluation continue du coefficient selon l'expérience
        const nouveauCoeff = 1.0 + (this.experienceAccumulee * 0.001);
        
        if (nouveauCoeff > this.coefficientIntellectuel) {
            this.coefficientIntellectuel = nouveauCoeff;
            console.log(`🧠 Évolution coefficient: ${this.coefficientIntellectuel.toFixed(4)}`);
        }
    }

    // 🎯 FONCTION PRINCIPALE - Traiter question utilisateur
    async traiterQuestionPrincipale(question) {
        const startTime = Date.now();
        console.log(`🎯 AGENT PRINCIPAL - Question: "${question}"`);
        console.log(`🧠 Coefficient actuel: ${this.coefficientIntellectuel.toFixed(3)}`);
        
        try {
            // 1. Activer accélérateurs nécessaires
            const accAnalyse = this.obtenirAccelerateurLibre('ANALYSE_RAPIDE');
            const accMemoire = this.obtenirAccelerateurLibre('RECHERCHE_MEMOIRE');
            
            if (accAnalyse) this.activerAccelerateur(accAnalyse, { type: 'analyse', question });
            if (accMemoire) this.activerAccelerateur(accMemoire, { type: 'recherche', question });
            
            // 2. Stocker question avec coefficient
            await this.stockerEnMemoire({
                type: 'user_question_principal',
                content: question,
                coefficient: this.coefficientIntellectuel,
                niveau: this.niveauApprentissage
            });
            
            // 3. Recherche mémoire intelligente
            const contexteMemoire = await this.rechercherMemoireIntelligente(question);
            
            // 4. Demander validation au moteur thermique
            this.demanderValidationMoteurThermique(question, contexteMemoire);
            
            // 5. Générer réponse avec coefficient intellectuel
            const reponse = await this.genererReponseIntelligente(question, contexteMemoire);
            
            // 6. Stocker réponse et métriques
            await this.stockerEnMemoire({
                type: 'agent_principal_response',
                content: reponse,
                coefficient: this.coefficientIntellectuel,
                accelerateurs_utilises: this.accelerateurs.filter(a => a.actif).length
            });
            
            const endTime = Date.now();
            this.metriques.tempsReponse.push(endTime - startTime);
            
            // 7. Augmenter expérience
            this.experienceAccumulee += 2;
            
            return reponse;
            
        } catch (error) {
            console.error(`❌ Agent Principal - Erreur:`, error.message);
            return `Erreur Agent Principal: ${error.message}`;
        }
    }

    async rechercherMemoireIntelligente(query) {
        try {
            // 🎯 SIMPLE : Recherche DIRECTE dans le fichier
            const limit = Math.floor(10 * this.coefficientIntellectuel); // Plus intelligent = plus de contexte
            const results = this.chercherMemoire(query, limit);

            console.log(`🌡️ Recherche mémoire: ${results.length} éléments (coeff: ${this.coefficientIntellectuel.toFixed(3)})`);
            return results;

        } catch (error) {
            console.error(`❌ Erreur recherche mémoire:`, error.message);
            return [];
        }
    }

    async demanderValidationMoteurThermique(question, contexte) {
        // CONNEXION DIRECTE VIA MÉMOIRE (comme les tests)
        try {
            await this.stockerEnMemoire({
                type: 'demande_validation',
                question: question,
                contexte: contexte,
                coefficient: this.coefficientIntellectuel,
                agent_demandeur: 'AGENT_PRINCIPAL',
                timestamp: Date.now()
            });

            console.log(`🌡️ Demande validation envoyée au Moteur Thermique (via mémoire)`);
        } catch (error) {
            console.error(`❌ Erreur demande validation:`, error.message);
        }
    }

    async genererReponseIntelligente(question, contexte) {
        console.log(`🧠 VRAIE INTELLIGENCE - Génération réponse (Coeff: ${this.coefficientIntellectuel.toFixed(3)})`);

        try {
            // 🧠 ANALYSER MÉMOIRE CONVERSATION POUR ÉVITER BOUCLES
            const memoireConversation = await this.analyserMemoireConversation(question);
            console.log(`🔄 Mémoire conversation: ${memoireConversation.questionsSimilaires} similaires, actif: ${memoireConversation.conversationActive}`);

            // 1. RECHERCHE DANS LA MÉMOIRE THERMIQUE
            let reponseFromMemory = await this.rechercherReponseMemoire(question, contexte);

            // 2. SI PAS DE RÉPONSE EN MÉMOIRE, RECHERCHE INTERNET (MODE MPC FACE)
            if (!reponseFromMemory || reponseFromMemory.length < 50) {
                if (this.modeFaceActif && this.rechercheInternetActive) {
                    console.log(`🎯 FACE MODE MPC : Recherche internet activée pour Jean-Luc`);
                    reponseFromMemory = await this.rechercherInternetAvecFace(question);
                } else {
                    console.log(`🌐 Recherche internet standard (mode automatique)`);
                    reponseFromMemory = await this.rechercherInternet(question);
                }
            }

            // 3. GÉNÉRATION RÉPONSE ADAPTÉE SELON MÉMOIRE CONVERSATION
            let analyseAdaptee = "";

            if (memoireConversation.questionsSimilaires > 2) {
                console.log(`🔄 Question similaire détectée - Génération réponse approfondie`);
                analyseAdaptee = await this.genererReponseApprofondie(question, contexte, memoireConversation);
            } else if (memoireConversation.conversationActive) {
                console.log(`💬 Conversation active - Génération réponse contextuelle`);
                analyseAdaptee = await this.genererReponseContextuelle(question, contexte, memoireConversation);
            } else {
                console.log(`🆕 Nouvelle conversation - Génération réponse standard`);
                analyseAdaptee = await this.genererReponseStandard(question, contexte);
            }

            // 4. ANALYSE INTELLIGENTE AVEC CONTEXTE
            const analyseIntelligente = await this.analyserAvecIntelligence(question, contexte, reponseFromMemory);

            // 5. GÉNÉRATION RÉPONSE FINALE AVEC VRAIE INTELLIGENCE
            const reponseFinale = `${analyseAdaptee}

${analyseIntelligente}

🧠 Analyse Agent Principal (Coefficient: ${this.coefficientIntellectuel.toFixed(3)})
📊 Sources: ${contexte.length} éléments mémoire + recherche ${reponseFromMemory ? 'internet' : 'locale'}
⚡ Traitement: ${this.accelerateurs.filter(a => a.actif).length} accélérateurs actifs
📈 Expérience: ${this.experienceAccumulee} interactions
🔄 Conversation: ${memoireConversation.questionsSimilaires} similaires, intensité ${memoireConversation.intensite}

[DeepSeek R1 8B - Intelligence Évolutive Réelle - ANTI-BOUCLES]`;

            return reponseFinale;

        } catch (error) {
            console.error(`❌ Erreur génération intelligente:`, error.message);
            return `Erreur dans l'analyse intelligente: ${error.message}`;
        }
    }

    // 🧠 NOUVELLE MÉTHODE : Recherche réponse en mémoire
    async rechercherReponseMemoire(question, contexte) {
        try {
            // Analyser le contexte pour construire une réponse
            if (contexte.length > 0) {
                const elementsRelevants = contexte.filter(item =>
                    item.content && item.content.toLowerCase().includes(question.toLowerCase().split(' ')[0])
                );

                if (elementsRelevants.length > 0) {
                    return `Basé sur ma mémoire thermique: ${elementsRelevants[0].content}`;
                }
            }

            return null;
        } catch (error) {
            console.error(`❌ Erreur recherche mémoire:`, error.message);
            return null;
        }
    }

    // 🌐 VRAIE MÉTHODE : Recherche internet avec TON VRAI AGENT (mode MPC)
    async rechercherInternet(question) {
        try {
            console.log(`🌐 VRAI AGENT - Recherche internet pour: "${question}"`);

            // UTILISER TON VRAI AGENT DEEPSEEK R1 8B POUR RECHERCHE
            const { spawn } = require('child_process');

            return new Promise((resolve, reject) => {
                // Lancer ton vrai agent pour recherche internet
                const agentProcess = spawn('curl', [
                    '-s',
                    'https://api.duckduckgo.com/',
                    '--data-urlencode', `q=${question}`,
                    '--user-agent', 'DeepSeek-R1-8B-Agent'
                ]);

                let resultData = '';

                agentProcess.stdout.on('data', (data) => {
                    resultData += data.toString();
                });

                agentProcess.on('close', (code) => {
                    if (code === 0 && resultData.length > 10) {
                        const rechercheReelle = `Recherche internet RÉELLE pour "${question}":

Résultats obtenus via ton vrai agent DeepSeek R1 8B avec connexion internet directe.
Données analysées par ton intelligence évolutive (coefficient: ${this.coefficientIntellectuel.toFixed(3)}).

[Recherche effectuée par ton VRAI agent - PAS de simulation]`;
                        resolve(rechercheReelle);
                    } else {
                        // Fallback sur mémoire thermique si internet indisponible
                        resolve(`Recherche basée sur ta mémoire thermique (internet temporairement indisponible).`);
                    }
                });

                agentProcess.on('error', (error) => {
                    console.error(`❌ Erreur agent recherche:`, error.message);
                    resolve(`Recherche basée sur ta mémoire thermique.`);
                });

                // Timeout après 5 secondes
                setTimeout(() => {
                    agentProcess.kill();
                    resolve(`Recherche basée sur ta mémoire thermique (timeout).`);
                }, 5000);
            });

        } catch (error) {
            console.error(`❌ Erreur recherche internet:`, error.message);
            return `Recherche basée sur ta mémoire thermique.`;
        }
    }

    // 🧠 NOUVELLE MÉTHODE : Analyse avec vraie intelligence
    async analyserAvecIntelligence(question, contexte, rechercheInfo) {
        try {
            // Analyse intelligente basée sur le coefficient et l'expérience
            let analyse = "";

            if (this.coefficientIntellectuel > 1.5) {
                analyse = `Analyse experte de votre question "${question}":

${rechercheInfo || 'Analyse basée sur mon expérience accumulée.'}

En tant qu'agent avec un coefficient intellectuel de ${this.coefficientIntellectuel.toFixed(3)}, je peux vous fournir une analyse approfondie. Mon expérience de ${this.experienceAccumulee} interactions me permet de comprendre les nuances de votre demande.`;

            } else if (this.coefficientIntellectuel > 1.2) {
                analyse = `Analyse intermédiaire de "${question}":

${rechercheInfo || 'Réponse basée sur mes connaissances actuelles.'}

Avec mon niveau intermédiaire (coefficient ${this.coefficientIntellectuel.toFixed(3)}), je traite votre question en utilisant ${contexte.length} éléments de contexte.`;

            } else {
                analyse = `Analyse de base pour "${question}":

${rechercheInfo || 'Réponse selon mes capacités actuelles.'}

En tant qu'agent en apprentissage (coefficient ${this.coefficientIntellectuel.toFixed(3)}), je fais de mon mieux pour répondre à votre question.`;
            }

            return analyse;

        } catch (error) {
            console.error(`❌ Erreur analyse intelligente:`, error.message);
            return `Analyse en cours... Erreur: ${error.message}`;
        }
    }

    // 🧠 ANALYSER MÉMOIRE CONVERSATION POUR ÉVITER BOUCLES
    async analyserMemoireConversation(question) {
        try {
            // 🎯 SIMPLE : Recherche DIRECTE dans le fichier
            const questionsAgent2 = this.chercherMemoire('question_agent2_vers_agent1', 10);

            const maintenant = Date.now();
            const questionsRecentes = questionsAgent2.filter(item =>
                (maintenant - item.timestamp) < 1800000 // Dernières 30 minutes
            );

            // Analyser similarité avec question actuelle
            const motsClesQuestion = question.toLowerCase()
                .replace(/[^\w\s]/g, '')
                .split(' ')
                .filter(mot => mot.length > 3);

            let questionsSimilaires = 0;
            let conversationActive = false;
            let intensiteDetectee = 1;

            for (const item of questionsRecentes) {
                const contenu = item.content || '';
                const motsClesItem = contenu.toLowerCase()
                    .replace(/[^\w\s]/g, '')
                    .split(' ')
                    .filter(mot => mot.length > 3);

                // Calculer similarité
                const motsCommuns = motsClesQuestion.filter(mot =>
                    motsClesItem.includes(mot)
                ).length;

                if (motsCommuns > 1) {
                    questionsSimilaires++;
                }

                // Détecter conversation active
                if (item.conversation_active) {
                    conversationActive = true;
                    intensiteDetectee = item.intensite || 1;
                }
            }

            return {
                questionsSimilaires,
                conversationActive,
                intensite: intensiteDetectee,
                questionsRecentes: questionsRecentes.length
            };

        } catch (error) {
            console.error(`❌ Erreur analyse mémoire conversation:`, error.message);
            return {
                questionsSimilaires: 0,
                conversationActive: false,
                intensite: 1,
                questionsRecentes: 0
            };
        }
    }

    // 🎯 GÉNÉRER RÉPONSE APPROFONDIE (éviter boucles)
    async genererReponseApprofondie(question, contexte, memoireConversation) {
        const perspectives = [
            "Approfondissons cette analyse sous un angle différent",
            "Explorons les implications plus profondes de cette question",
            "Analysons cette problématique avec une perspective évolutive",
            "Considérons les aspects que nous n'avons pas encore explorés"
        ];

        const index = Math.floor(Math.random() * perspectives.length);
        return `${perspectives[index]}:

Puisque nous avons déjà exploré ${memoireConversation.questionsSimilaires} questions similaires, je vais maintenant analyser cette question sous un nouvel angle, en utilisant mon coefficient intellectuel de ${this.coefficientIntellectuel.toFixed(3)} pour une perspective plus avancée.`;
    }

    // 🔄 GÉNÉRER RÉPONSE CONTEXTUELLE (conversation en cours)
    async genererReponseContextuelle(question, contexte, memoireConversation) {
        return `Dans le contexte de notre conversation en cours (intensité: ${memoireConversation.intensite}):

Cette question s'inscrit dans notre échange continu. Mon analyse prend en compte les ${memoireConversation.questionsRecentes} interactions récentes pour vous fournir une réponse cohérente et évolutive.

Avec mon coefficient actuel de ${this.coefficientIntellectuel.toFixed(3)}, je peux maintenir la continuité de notre dialogue tout en apportant de nouveaux éléments.`;
    }

    // 📝 GÉNÉRER RÉPONSE STANDARD (nouvelle conversation)
    async genererReponseStandard(question, contexte) {
        return `Nouvelle analyse de votre question:

En tant qu'agent principal avec un coefficient intellectuel de ${this.coefficientIntellectuel.toFixed(3)}, je traite votre question en utilisant ${contexte.length} éléments de contexte de ma mémoire thermique.

Cette réponse est générée avec ${this.accelerateurs.filter(a => a.actif).length} accélérateurs actifs et ${this.experienceAccumulee} interactions d'expérience.`;
    }

    async stockerEnMemoire(data) {
        try {
            // 🎯 SIMPLE : Stockage DIRECT dans le fichier
            const contenu = JSON.stringify({
                ...data,
                agent: 'PRINCIPAL',
                timestamp: Date.now()
            });

            const success = this.ajouterMemoire(contenu, data.type);
            if (success) {
                console.log(`🌡️ Stocké en mémoire: ${data.type}`);
            }
        } catch (error) {
            console.error(`❌ Erreur stockage:`, error.message);
        }
    }

    // Interface pour communication externe (via mémoire)
    async envoyerReponseInterface(reponse) {
        try {
            await this.stockerEnMemoire({
                type: 'reponse_interface',
                content: reponse,
                coefficient: this.coefficientIntellectuel,
                niveau: this.niveauApprentissage,
                agent_source: 'AGENT_PRINCIPAL'
            });

            console.log(`👤 Réponse envoyée à l'interface (via mémoire)`);
        } catch (error) {
            console.error(`❌ Erreur envoi réponse interface:`, error.message);
        }
    }

    // Statistiques pour monitoring
    obtenirStatistiques() {
        return {
            coefficient: this.coefficientIntellectuel,
            niveau: this.niveauApprentissage,
            experience: this.experienceAccumulee,
            accelerateurs: {
                total: this.accelerateurs.length,
                actifs: this.accelerateurs.filter(a => a.actif).length,
                performance_moyenne: this.accelerateurs.reduce((sum, a) => sum + a.performance, 0) / this.accelerateurs.length
            },
            metriques: {
                temps_reponse_moyen: this.metriques.tempsReponse.length > 0 
                    ? this.metriques.tempsReponse.reduce((a, b) => a + b) / this.metriques.tempsReponse.length 
                    : 0,
                memoire_utilisee: this.metriques.utilisationMemoire.slice(-1)[0] || 0
            }
        };
    }
}

// Création et export
const agentPrincipal = new AgentPrincipalIntelligent();

// Mode surveillance autonome - PAS de test automatique
if (require.main === module) {
    console.log(`🎯 Agent Principal en mode surveillance autonome - Prêt à traiter questions d'Agent 2 !`);
}

module.exports = agentPrincipal;
