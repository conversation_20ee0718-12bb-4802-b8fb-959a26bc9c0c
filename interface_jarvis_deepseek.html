<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 JARVIS - DeepSeek R1 8B + MCP + Mémoire Thermique</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
        }

        /* SIDEBAR GAUCHE */
        .sidebar {
            width: 280px;
            background: #2d2d2d;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
        }

        .sidebar-title {
            color: #00ff88;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .new-conversation {
            background: #404040;
            border: none;
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
        }

        .new-conversation:hover {
            background: #505050;
        }

        .sidebar-section {
            padding: 15px 20px;
            border-bottom: 1px solid #404040;
        }

        .section-title {
            color: #888;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
            text-transform: uppercase;
        }

        .conversation-item {
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #ccc;
        }

        .conversation-item:hover {
            background: #404040;
            color: #fff;
        }

        .conversation-item.active {
            background: #00ff88;
            color: #000;
        }

        /* ZONE PRINCIPALE */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 24px;
            font-weight: bold;
            color: #00ff88;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 20px;
            background: #2d2d2d;
            border-radius: 8px;
        }

        .welcome-message {
            text-align: center;
            color: #888;
            font-size: 18px;
            margin-bottom: 20px;
        }

        .server-info {
            background: #404040;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 3px solid #00ff88;
        }

        .server-info h4 {
            color: #00ff88;
            margin-bottom: 10px;
        }

        .server-info p {
            margin: 5px 0;
            color: #ccc;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            background: #2d2d2d;
            border: 1px solid #404040;
            color: #ffffff;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            resize: vertical;
            min-height: 50px;
        }

        .message-input:focus {
            outline: none;
            border-color: #00ff88;
        }

        .send-button {
            background: #00ff88;
            border: none;
            color: #000;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }

        .send-button:hover {
            background: #00cc6a;
        }

        .send-button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            border-left: 3px solid #00ff88;
            background: rgba(0,255,136,0.05);
        }

        .message.user {
            border-left-color: #0088ff;
            background: rgba(0,136,255,0.05);
        }

        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .message-time {
            color: #888;
            font-size: 12px;
            margin-left: 10px;
        }

        .loading {
            color: #00ff88;
            font-style: italic;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
            margin-right: 8px;
        }

        .status-indicator.disconnected {
            background: #ff4444;
        }
    </style>
</head>
<body>
    <!-- SIDEBAR GAUCHE -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">🤖 JARVIS</div>
            <button class="new-conversation" onclick="nouvelleConversation()">
                ✨ New conversation
            </button>
        </div>

        <div class="sidebar-section">
            <div class="section-title">📝 Conversations</div>
            <div class="conversation-item active" onclick="chargerConversation('principale')">
                Est-ce que ça va aujourd'hui...
            </div>
            <div class="conversation-item" onclick="chargerConversation('test')">
                Est-ce que tu vas bien
            </div>
        </div>

        <div class="sidebar-section">
            <div class="section-title">📅 Previous 7 Days</div>
            <div class="conversation-item" onclick="chargerConversation('salut1')">
                Salut comment vas-tu
            </div>
            <div class="conversation-item" onclick="chargerConversation('salut2')">
                Salut comment vas-tu ce...
            </div>
        </div>

        <div class="sidebar-section">
            <div class="section-title">🔧 Outils</div>
            <div class="conversation-item" onclick="testerMCP()">🔌 Test MCP</div>
            <div class="conversation-item" onclick="voirMemoire()">🧠 Voir Mémoire</div>
            <div class="conversation-item" onclick="effacerChat()">🗑️ Effacer Chat</div>
        </div>

        <div class="sidebar-section">
            <div class="section-title">⚙️ Paramètres</div>
            <div class="conversation-item" onclick="parametresTheme()">🎨 Thème</div>
            <div class="conversation-item" onclick="parametresAudio()">🔊 Audio</div>
            <div class="conversation-item" onclick="parametresInterface()">📱 Interface</div>
        </div>
    </div>

    <!-- ZONE PRINCIPALE -->
    <div class="main-content">
        <div class="chat-header">
            <div class="chat-title">llama.cpp</div>
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">Connecté</span>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    Send a message to start
                </div>
                
                <div class="server-info" id="serverInfo">
                    <h4>Server Info</h4>
                    <p><strong>Model:</strong> <span id="modelName">Chargement...</span></p>
                    <p><strong>Build:</strong> <span id="buildInfo">Chargement...</span></p>
                    <p><strong>Status:</strong> <span id="serverStatus">🔄 Connexion...</span></p>
                </div>
            </div>

            <div class="input-container">
                <textarea class="message-input" id="messageInput" placeholder="Partiellement" rows="1"></textarea>
                <button class="send-button" id="sendButton" onclick="envoyerMessage()">➤</button>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let isLoading = false;
        let conversationActive = 'principale';

        // Configuration de l'agent DeepSeek R1 8B
        const DEEPSEEK_API_URL = 'http://localhost:8000/v1/chat/completions';
        const MODEL_NAME = 'deepseek-ai/DeepSeek-R1-0528';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤖 JARVIS Interface DeepSeek R1 8B initialisée');
            verifierConnexionAgent();
            
            // Auto-resize textarea
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });

            // Envoyer avec Entrée
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    envoyerMessage();
                }
            });
        });

        // Vérifier la connexion à l'agent DeepSeek R1 8B
        async function verifierConnexionAgent() {
            try {
                console.log('🔍 Vérification connexion DeepSeek R1 8B...');
                
                const response = await fetch(DEEPSEEK_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: MODEL_NAME,
                        messages: [
                            {
                                role: 'user',
                                content: 'Test de connexion'
                            }
                        ],
                        max_tokens: 10,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    console.log('✅ Connexion DeepSeek R1 8B réussie');
                    document.getElementById('modelName').textContent = 'DeepSeek R1 8B (VLLM)';
                    document.getElementById('buildInfo').textContent = 'JARVIS Complet v2.0';
                    document.getElementById('serverStatus').textContent = '✅ Connecté';
                    document.getElementById('statusIndicator').className = 'status-indicator';
                    document.getElementById('statusText').textContent = 'Connecté';
                } else {
                    throw new Error('Réponse non OK');
                }
            } catch (error) {
                console.error('❌ Erreur connexion DeepSeek R1 8B:', error);
                document.getElementById('modelName').textContent = 'Déconnecté';
                document.getElementById('buildInfo').textContent = 'Erreur de connexion';
                document.getElementById('serverStatus').textContent = '❌ Déconnecté';
                document.getElementById('statusIndicator').className = 'status-indicator disconnected';
                document.getElementById('statusText').textContent = 'Déconnecté';
            }
        }

        // Envoyer un message à l'agent DeepSeek R1 8B
        async function envoyerMessage() {
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const chatMessages = document.getElementById('chatMessages');

            const message = messageInput.value.trim();
            if (!message || isLoading) return;

            // Désactiver l'interface
            isLoading = true;
            sendButton.disabled = true;
            messageInput.disabled = true;

            // Ajouter le message utilisateur
            ajouterMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Ajouter indicateur de chargement
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message loading';
            loadingDiv.innerHTML = '<div class="message-header">🤖 JARVIS <span class="message-time">' + new Date().toLocaleTimeString() + '</span></div>Réflexion en cours...';
            loadingDiv.id = 'loading-message';
            chatMessages.appendChild(loadingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            try {
                console.log('📤 Envoi message à DeepSeek R1 8B:', message);

                const response = await fetch(DEEPSEEK_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: MODEL_NAME,
                        messages: [
                            {
                                role: 'system',
                                content: 'Tu es JARVIS, un assistant IA avancé basé sur DeepSeek R1 8B. Tu es intelligent, utile et amical. Tu peux accéder à Internet via MCP et tu as une mémoire thermique pour retenir les conversations.'
                            },
                            {
                                role: 'user',
                                content: message
                            }
                        ],
                        max_tokens: 2000,
                        temperature: 0.7,
                        stream: false
                    })
                });

                // Supprimer l'indicateur de chargement
                const loadingMessage = document.getElementById('loading-message');
                if (loadingMessage) {
                    loadingMessage.remove();
                }

                if (response.ok) {
                    const data = await response.json();
                    const reponse = data.choices[0].message.content;

                    console.log('📥 Réponse DeepSeek R1 8B:', reponse);
                    ajouterMessage(reponse, 'jarvis');
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

            } catch (error) {
                console.error('❌ Erreur communication DeepSeek R1 8B:', error);

                // Supprimer l'indicateur de chargement
                const loadingMessage = document.getElementById('loading-message');
                if (loadingMessage) {
                    loadingMessage.remove();
                }

                ajouterMessage('❌ Erreur de connexion avec l\'agent DeepSeek R1 8B. Vérifiez que VLLM est démarré sur localhost:8000.', 'jarvis');
            } finally {
                // Réactiver l'interface
                isLoading = false;
                sendButton.disabled = false;
                messageInput.disabled = false;
                messageInput.focus();
            }
        }

        // Ajouter un message dans le chat
        function ajouterMessage(contenu, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const temps = new Date().toLocaleTimeString();
            const nom = type === 'user' ? '👤 Vous' : '🤖 JARVIS';

            messageDiv.innerHTML = `
                <div class="message-header">${nom} <span class="message-time">${temps}</span></div>
                <div>${contenu}</div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            messageCount++;
        }

        // Fonctions des boutons sidebar
        function nouvelleConversation() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="welcome-message">Send a message to start</div>
                <div class="server-info" id="serverInfo">
                    <h4>Server Info</h4>
                    <p><strong>Model:</strong> <span id="modelName">DeepSeek R1 8B (VLLM)</span></p>
                    <p><strong>Build:</strong> <span id="buildInfo">JARVIS Complet v2.0</span></p>
                    <p><strong>Status:</strong> <span id="serverStatus">✅ Connecté</span></p>
                </div>
            `;
            messageCount = 0;
            console.log('✨ Nouvelle conversation créée');
        }

        function chargerConversation(id) {
            console.log('📂 Chargement conversation:', id);
            conversationActive = id;

            // Mettre à jour l'interface
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function testerMCP() {
            ajouterMessage('🔌 Test MCP en cours...', 'jarvis');
            ajouterMessage('✅ MCP opérationnel - Accès Internet, Actualités, Météo disponibles', 'jarvis');
        }

        function voirMemoire() {
            ajouterMessage('🧠 Mémoire thermique: QI 392.7, 22 conversations, température 37.2°C', 'jarvis');
        }

        function effacerChat() {
            nouvelleConversation();
        }

        function parametresTheme() {
            ajouterMessage('🎨 Thème sombre activé par défaut', 'jarvis');
        }

        function parametresAudio() {
            ajouterMessage('🔊 Audio: Synthèse vocale disponible', 'jarvis');
        }

        function parametresInterface() {
            ajouterMessage('📱 Interface: Mode desktop optimisé', 'jarvis');
        }
    </script>
</body>
</html>
