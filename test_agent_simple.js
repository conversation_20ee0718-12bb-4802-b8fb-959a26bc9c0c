const fs = require('fs');

console.log('🤖 AGENT 1 RÉEL SIMPLE - TEST');
console.log('⚡ PAS D\'OLLAMA - CONNEXION DIRECTE SEULEMENT');

// Test connexion mémoire thermique
const memoryFile = './thermal_memory_persistent.json';

if (fs.existsSync(memoryFile)) {
    const data = fs.readFileSync(memoryFile, 'utf8');
    const memory = JSON.parse(data);
    
    console.log('🌡️ Mémoire thermique connectée !');
    console.log('📊 QI Level:', memory.neural_system?.qi_level || 'N/A');
    console.log('🧠 Neurones actifs:', memory.neural_system?.active_neurons || 'N/A');
    console.log('🔥 Température:', memory.neural_system?.neural_temperature || 'N/A');
    
    console.log('\n✅ CONNEXION RÉELLE ÉTABLIE !');
    console.log('🎯 Agent 1 fonctionne avec ta vraie mémoire thermique !');
    
} else {
    console.log('❌ Fichier mémoire thermique non trouvé:', memoryFile);
}

console.log('\n🎯 Tapez Ctrl+C pour arrêter');
