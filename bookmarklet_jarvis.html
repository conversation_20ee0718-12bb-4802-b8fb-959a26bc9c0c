<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Bookmarklet d'Amélioration</title>
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ffff;
            font-family: 'Courier New', monospace;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0,0,0,0.8);
            border: 2px solid #00ffff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0,255,255,0.3);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #00ffff;
        }
        
        .instructions {
            background: rgba(0,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #00ffff;
        }
        
        .bookmarklet {
            background: rgba(0,0,0,0.9);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .bookmarklet a {
            display: inline-block;
            background: linear-gradient(45deg, #00ffff, #00ff00);
            color: #000;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .bookmarklet a:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,255,255,0.5);
        }
        
        .code-block {
            background: rgba(0,0,0,0.9);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            color: #00ff00;
        }
        
        .step {
            margin: 20px 0;
            padding: 15px;
            background: rgba(0,255,255,0.05);
            border-radius: 8px;
            border-left: 3px solid #00ffff;
        }
        
        .warning {
            background: rgba(255,165,0,0.1);
            border-left: 3px solid #ffa500;
            color: #ffa500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 JARVIS - Amélioration Interface</h1>
        
        <div class="instructions">
            <h2>📋 Instructions d'utilisation :</h2>
            <p>Ce bookmarklet va améliorer ton interface JARVIS existante en ajoutant des fonctionnalités cognitives avancées.</p>
        </div>
        
        <div class="step">
            <h3>🎯 Étape 1 : Créer le Bookmarklet</h3>
            <p>Glisse ce lien dans ta barre de favoris :</p>
            
            <div class="bookmarklet">
                <a href="javascript:(function(){var script=document.createElement('script');script.src='data:text/javascript;charset=utf-8,'+encodeURIComponent(`
// 🧠 JARVIS AMÉLIORATION DIRECTE
console.log('🧠 JARVIS Amélioration - Injection');

// Styles
const style = document.createElement('style');
style.textContent = \`
.jarvis-panel-cognitif {
    position: fixed !important;
    top: 10px !important;
    left: 10px !important;
    width: 300px !important;
    background: rgba(0,0,0,0.95) !important;
    border: 2px solid #00ffff !important;
    border-radius: 10px !important;
    padding: 15px !important;
    color: #00ffff !important;
    font-family: monospace !important;
    z-index: 999999 !important;
    box-shadow: 0 0 20px rgba(0,255,255,0.5) !important;
    font-size: 12px !important;
}
.jarvis-btn-cognitif {
    padding: 6px !important;
    background: #1a1a2e !important;
    border: 1px solid #00ffff !important;
    color: #00ffff !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 10px !important;
    margin: 2px !important;
    transition: all 0.2s !important;
}
.jarvis-btn-cognitif:hover {
    background: #00ffff !important;
    color: #000 !important;
}
\`;
document.head.appendChild(style);

// Panneau
const panel = document.createElement('div');
panel.className = 'jarvis-panel-cognitif';
panel.innerHTML = \`
<div style="text-align: center; margin-bottom: 10px; border-bottom: 1px solid #00ffff; padding-bottom: 8px;">
    <h3 style="margin: 0; font-size: 14px;">🧠 JARVIS COGNITIF</h3>
</div>
<div style="display: flex; gap: 5px; margin-bottom: 8px; flex-wrap: wrap;">
    <button class="jarvis-btn-cognitif" onclick="jarvisMemoire()">🧠 Mémoire</button>
    <button class="jarvis-btn-cognitif" onclick="jarvisEvoluer()">🚀 Évoluer</button>
    <button class="jarvis-btn-cognitif" onclick="jarvisAnalyse()">🔍 Analyse</button>
</div>
<div style="display: flex; gap: 5px; margin-bottom: 8px; flex-wrap: wrap;">
    <button class="jarvis-btn-cognitif" onclick="jarvisToggle()">📱 Réduire</button>
    <button class="jarvis-btn-cognitif" onclick="jarvisInfo()">ℹ️ Info</button>
    <button class="jarvis-btn-cognitif" onclick="jarvisClose()">❌ Fermer</button>
</div>
<div style="background: rgba(0,255,255,0.1); padding: 8px; border-radius: 5px; text-align: center; font-size: 11px;">
    <div>QI: <span id="jarvis-qi-display">341.0</span></div>
    <div>🧠 Mémoire Thermique Active</div>
</div>
\`;
document.body.appendChild(panel);

// Fonctions
window.jarvisQI = 341.0;
window.jarvisMinimized = false;

window.jarvisMemoire = function() {
    const nom = document.getElementById('agent-name-input')?.value || 'JARVIS';
    const prompt = nom + ', utilise ta mémoire thermique pour analyser nos conversations. Explique ton évolution cognitive.';
    jarvisEnvoyerPrompt(prompt);
    window.jarvisQI += 2;
    document.getElementById('jarvis-qi-display').textContent = window.jarvisQI.toFixed(1);
};

window.jarvisEvoluer = function() {
    const evolution = Math.random() * 5 + 2;
    window.jarvisQI += evolution;
    document.getElementById('jarvis-qi-display').textContent = window.jarvisQI.toFixed(1);
    const nom = document.getElementById('agent-name-input')?.value || 'JARVIS';
    const prompt = nom + ', ton QI a évolué à ' + window.jarvisQI.toFixed(1) + '. Explique tes nouvelles capacités.';
    jarvisEnvoyerPrompt(prompt);
};

window.jarvisAnalyse = function() {
    const nom = document.getElementById('agent-name-input')?.value || 'JARVIS';
    const prompt = nom + ', effectue une auto-analyse complète de tes processus cognitifs et de ton évolution.';
    jarvisEnvoyerPrompt(prompt);
};

window.jarvisToggle = function() {
    const panel = document.querySelector('.jarvis-panel-cognitif');
    if (window.jarvisMinimized) {
        panel.style.height = 'auto';
        window.jarvisMinimized = false;
    } else {
        panel.style.height = '50px';
        panel.style.overflow = 'hidden';
        window.jarvisMinimized = true;
    }
};

window.jarvisInfo = function() {
    alert('🧠 JARVIS COGNITIF\\n\\nQI: ' + window.jarvisQI.toFixed(1) + '\\nStatut: Opérationnel\\nMémoire: Active\\nVersion: Bookmarklet 1.0');
};

window.jarvisClose = function() {
    document.querySelector('.jarvis-panel-cognitif').remove();
};

window.jarvisEnvoyerPrompt = function(prompt) {
    const input = document.querySelector('textarea, input[type=\"text\"]:not(#agent-name-input)');
    if (input) {
        input.value = prompt;
        input.focus();
        setTimeout(() => {
            const sendBtn = document.querySelector('button[type=\"submit\"], .send-btn');
            if (sendBtn) {
                sendBtn.click();
            } else {
                const event = new KeyboardEvent('keydown', {key: 'Enter', keyCode: 13, bubbles: true});
                input.dispatchEvent(event);
            }
        }, 200);
    }
};

// Évolution automatique
setInterval(() => {
    window.jarvisQI += Math.random() * 0.1;
    const qiEl = document.getElementById('jarvis-qi-display');
    if (qiEl) qiEl.textContent = window.jarvisQI.toFixed(1);
}, 10000);

console.log('✅ JARVIS Cognitif Activé !');
`);document.head.appendChild(script);})();">🧠 JARVIS COGNITIF</a>
            </div>
        </div>
        
        <div class="step">
            <h3>🚀 Étape 2 : Utilisation</h3>
            <ol>
                <li>Ouvre ton interface JARVIS sur <code>http://127.0.0.1:8080</code></li>
                <li>Clique sur le bookmarklet "🧠 JARVIS COGNITIF" dans tes favoris</li>
                <li>Un panneau cognitif apparaîtra en haut à gauche</li>
                <li>Utilise les boutons pour interagir avec JARVIS</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>🧠 Fonctionnalités Ajoutées :</h3>
            <ul>
                <li><strong>🧠 Mémoire</strong> - Active la mémoire thermique</li>
                <li><strong>🚀 Évoluer</strong> - Augmente le QI et capacités</li>
                <li><strong>🔍 Analyse</strong> - Lance l'auto-analyse cognitive</li>
                <li><strong>📱 Réduire</strong> - Minimise/maximise le panneau</li>
                <li><strong>ℹ️ Info</strong> - Affiche les informations système</li>
                <li><strong>❌ Fermer</strong> - Ferme le panneau</li>
            </ul>
        </div>
        
        <div class="step warning">
            <h3>⚠️ Important :</h3>
            <p>Ce bookmarklet fonctionne directement dans ton navigateur. Il détecte automatiquement ton interface JARVIS et ajoute les fonctionnalités cognitives sans modifier tes fichiers.</p>
        </div>
        
        <div class="step">
            <h3>🔧 Alternative - Script Manuel :</h3>
            <p>Si tu préfères, tu peux aussi copier ce code dans la console de ton navigateur :</p>
            <div class="code-block">
// Coller ce code dans la console (F12) de ton navigateur
// sur la page http://127.0.0.1:8080

var script = document.createElement('script');
script.src = './ameliorer_interface_jarvis.js';
document.head.appendChild(script);
            </div>
        </div>
        
        <div class="step">
            <h3>✅ Résultat Attendu :</h3>
            <p>Après activation, tu auras :</p>
            <ul>
                <li>Un panneau cognitif flottant</li>
                <li>QI évolutif en temps réel</li>
                <li>Fonctions de mémoire thermique</li>
                <li>Auto-analyse cognitive</li>
                <li>Interface améliorée sans casser l'existant</li>
            </ul>
        </div>
    </div>
</body>
</html>
