#!/bin/bash

# 🤖 LANCEMENT JARVIS - Agent DeepSeek R1 8B (Linux)
# <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>ci Linux avec adaptation automatique

echo "🚀 DÉMARRAGE JARVIS - Agent DeepSeek R1 8B (Linux)"
echo "📍 Répertoire: $(pwd)"

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Erreur: Node.js non installé"
    exit 1
fi

# Vérifier fichiers
if [ ! -f "agent1_reel_simple.js" ]; then
    echo "❌ Erreur: agent1_reel_simple.js non trouvé"
    exit 1
fi

if [ ! -f "mpc_bureau_adaptatif.js" ]; then
    echo "❌ Erreur: mpc_bureau_adaptatif.js non trouvé"
    exit 1
fi

echo "✅ Fichiers trouvés"
echo "🐧 Adaptation Linux en cours..."
echo "🤖 Lancement de JARVIS avec MPC Adaptatif..."

# Lancer l'agent avec adaptation
node agent1_reel_simple.js
