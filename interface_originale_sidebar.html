<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦙 llama.cpp - chat + 🔌 MCP + 🧠 Mémoire Thermique</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
        }

        /* SIDEBAR GAUCHE */
        .sidebar {
            width: 280px;
            background: #2d2d2d;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
        }

        .sidebar-title {
            color: #00ff88;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .sidebar-section {
            padding: 15px 20px;
            border-bottom: 1px solid #404040;
        }

        .section-title {
            color: #888;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .conversation-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #ccc;
            background: rgba(255,255,255,0.05);
        }

        .conversation-item:hover {
            background: rgba(255,255,255,0.1);
        }

        .conversation-item.active {
            background: #00ff88;
            color: #000;
        }

        .new-conversation {
            background: #00ff88;
            color: #000;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .new-conversation:hover {
            background: #00dd77;
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .status-item {
            background: rgba(0,255,136,0.1);
            border: 1px solid #00ff88;
            border-radius: 6px;
            padding: 8px;
            text-align: center;
            font-size: 12px;
        }

        .status-value {
            color: #00ff88;
            font-weight: bold;
            font-size: 14px;
        }

        /* ZONE PRINCIPALE */
        .main-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
        }

        .main-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main-title {
            font-size: 24px;
            color: #fff;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            background: rgba(255,255,255,0.1);
            border: none;
            color: #fff;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .header-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        /* ZONE CHAT */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(255,255,255,0.02);
            border-radius: 10px;
            border: 1px solid #404040;
        }

        .welcome-message {
            text-align: center;
            color: #888;
            font-size: 16px;
            margin-top: 50px;
        }

        .server-info {
            background: rgba(0,255,136,0.1);
            border: 1px solid #00ff88;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
        }

        .server-info h4 {
            color: #00ff88;
            margin-bottom: 10px;
        }

        .server-info p {
            color: #ccc;
            font-size: 14px;
            margin: 5px 0;
        }

        /* INPUT ZONE */
        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: rgba(255,255,255,0.05);
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 12px 16px;
            color: #fff;
            font-size: 16px;
            resize: none;
            min-height: 50px;
        }

        .message-input:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 0 2px rgba(0,255,136,0.2);
        }

        .send-button {
            background: #00ff88;
            color: #000;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 600;
            cursor: pointer;
            font-size: 16px;
        }

        .send-button:hover {
            background: #00dd77;
        }

        .send-button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            border-left: 3px solid #00ff88;
            background: rgba(0,255,136,0.05);
        }

        .message.user {
            border-left-color: #0088ff;
            background: rgba(0,136,255,0.05);
        }

        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .message-time {
            color: #888;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <!-- SIDEBAR GAUCHE -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">🤖 JARVIS</div>
            <div class="new-conversation" onclick="nouvelleConversation()">
                ✨ New conversation
            </div>
        </div>

        <div class="sidebar-section">
            <div class="section-title">Conversations</div>
            <div class="conversation-item active">Est-ce que ça va aujourd'hui...</div>
            <div class="conversation-item">Est-ce que tu vas bien</div>
            <div class="conversation-item">Previous 7 Days</div>
            <div class="conversation-item">Salut comment vas-tu</div>
            <div class="conversation-item">Salut comment vas-tu ce...</div>
        </div>

        <div class="sidebar-section">
            <div class="section-title">🧠 Mémoire Thermique</div>
            <div class="status-grid">
                <div class="status-item">
                    <div>QI</div>
                    <div class="status-value">392.7</div>
                </div>
                <div class="status-item">
                    <div>Conversations</div>
                    <div class="status-value">22</div>
                </div>
                <div class="status-item">
                    <div>Souvenirs</div>
                    <div class="status-value">0</div>
                </div>
                <div class="status-item">
                    <div>Température</div>
                    <div class="status-value">37.2°C</div>
                </div>
            </div>
        </div>

        <div class="sidebar-section">
            <div class="section-title">🔧 Outils</div>
            <div class="conversation-item" onclick="testerMCP()">🔌 Test MCP</div>
            <div class="conversation-item" onclick="voirMemoire()">🧠 Voir Mémoire</div>
            <div class="conversation-item" onclick="effacerChat()">🗑️ Effacer Chat</div>
        </div>

        <div class="sidebar-section">
            <div class="section-title">⚙️ Paramètres</div>
            <div class="conversation-item">🎨 Thème</div>
            <div class="conversation-item">🔊 Audio</div>
            <div class="conversation-item">📱 Interface</div>
        </div>
    </div>

    <!-- ZONE PRINCIPALE -->
    <div class="main-area">
        <div class="main-header">
            <div class="main-title">llama.cpp</div>
            <div class="header-controls">
                <button class="header-btn">⚙️</button>
                <button class="header-btn">🌙</button>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    DeepSeek R1 8B Agent
                </div>
                
                <div class="server-info">
                    <h4>Server Info</h4>
                    <p><strong>Model:</strong> DeepSeek R1 8B</p>
                    <p><strong>Build:</strong> JARVIS Complet</p>
                    <p><strong>Status:</strong> ✅ Connecté</p>
                </div>
            </div>

            <div class="input-container">
                <textarea class="message-input" id="messageInput" placeholder="Partiellement" rows="1"></textarea>
                <button class="send-button" id="sendButton" onclick="envoyerMessage()">➤</button>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let isLoading = false;

        // Fonction pour envoyer un message
        async function envoyerMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isLoading) return;
            
            isLoading = true;
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            sendButton.textContent = '⏳';
            
            // Afficher le message utilisateur
            ajouterMessage(message, 'user');
            input.value = '';
            
            try {
                // Connexion directe à votre agent DeepSeek R1 8B
                const response = await fetch('http://127.0.0.1:8000/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        messages: [
                            {
                                role: 'user',
                                content: `Tu es JARVIS, assistant IA de Jean-Luc. Réponds en français de manière concise et utile. Message: ${message}`
                            }
                        ],
                        max_tokens: 500,
                        temperature: 0.7
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const reponse = data.choices[0].message.content;
                    ajouterMessage(reponse, 'jarvis');
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
                
            } catch (error) {
                console.error('Erreur:', error);
                ajouterMessage('❌ Erreur de connexion à JARVIS: ' + error.message, 'jarvis');
            } finally {
                isLoading = false;
                sendButton.disabled = false;
                sendButton.textContent = '➤';
            }
        }

        function ajouterMessage(contenu, type) {
            const chatMessages = document.getElementById('chatMessages');
            
            // Enlever le message de bienvenue
            const welcomeMsg = chatMessages.querySelector('.welcome-message');
            if (welcomeMsg) welcomeMsg.remove();
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const time = new Date().toLocaleTimeString();
            const nom = type === 'user' ? 'Vous' : 'JARVIS';
            
            messageDiv.innerHTML = `
                <div class="message-header">
                    ${nom}
                    <span class="message-time">${time}</span>
                </div>
                <div>${contenu}</div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            messageCount++;
        }

        // Fonctions des boutons sidebar
        function nouvelleConversation() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="welcome-message">DeepSeek R1 8B Agent</div>
                <div class="server-info">
                    <h4>Server Info</h4>
                    <p><strong>Model:</strong> DeepSeek R1 8B</p>
                    <p><strong>Build:</strong> JARVIS Complet</p>
                    <p><strong>Status:</strong> ✅ Connecté</p>
                </div>
            `;
            messageCount = 0;
        }

        function testerMCP() {
            ajouterMessage('🔌 Test MCP en cours...', 'jarvis');
            // Ici vous pouvez ajouter le test MCP réel
        }

        function voirMemoire() {
            ajouterMessage('🧠 Mémoire thermique: QI 392.7, 22 conversations, température 37.2°C', 'jarvis');
        }

        function effacerChat() {
            nouvelleConversation();
        }

        // Gestion de la touche Entrée
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                envoyerMessage();
            }
        });

        // Auto-resize du textarea
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    </script>
</body>
</html>
