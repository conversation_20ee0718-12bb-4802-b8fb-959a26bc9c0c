<!DOCTYPE html>
<html>
<head>
    <title>🧪 TEST INTERFACE JARVIS COMPLET</title>
    <style>
        body { 
            font-family: monospace; 
            background: #000; 
            color: #00ffff; 
            padding: 20px; 
        }
        .test { 
            border: 1px solid #00ffff; 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 5px; 
        }
        .success { border-color: #00ff00; color: #00ff00; }
        .error { border-color: #ff0000; color: #ff0000; }
        .loading { border-color: #ffff00; color: #ffff00; }
        button {
            background: rgba(0,255,255,0.1);
            border: 1px solid #00ffff;
            color: #00ffff;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background: rgba(0,255,255,0.2); }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 TEST COMPLET INTERFACE JARVIS</h1>
    
    <div class="test">
        <h3>🔧 TESTS DISPONIBLES</h3>
        <button onclick="testServeurs()">🖥️ Test Serveurs</button>
        <button onclick="testMCP()">🔌 Test MCP</button>
        <button onclick="testMemoire()">🧠 Test Mémoire</button>
        <button onclick="testInterface()">🌐 Test Interface</button>
        <button onclick="testComplet()">🚀 Test Complet</button>
    </div>
    
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testServeurs() {
            log('🔧 DÉBUT TEST SERVEURS', 'loading');
            
            try {
                // Test llama.cpp
                const llamaResponse = await fetch('http://127.0.0.1:8080/health');
                if (llamaResponse.ok) {
                    log('✅ llama.cpp (8080): ACTIF', 'success');
                } else {
                    log('❌ llama.cpp (8080): ERREUR', 'error');
                }
            } catch (error) {
                log('❌ llama.cpp (8080): INACCESSIBLE', 'error');
            }

            try {
                // Test Interface
                const interfaceResponse = await fetch('http://127.0.0.1:8085/');
                if (interfaceResponse.ok) {
                    log('✅ Interface JARVIS (8085): ACTIF', 'success');
                } else {
                    log('❌ Interface JARVIS (8085): ERREUR', 'error');
                }
            } catch (error) {
                log('❌ Interface JARVIS (8085): INACCESSIBLE', 'error');
            }

            try {
                // Test MCP
                const mcpResponse = await fetch('http://127.0.0.1:8086/mcp/status');
                if (mcpResponse.ok) {
                    const mcpData = await mcpResponse.json();
                    log(`✅ Serveur MCP (8086): ACTIF - ${mcpData.apis_disponibles.length} APIs`, 'success');
                } else {
                    log('❌ Serveur MCP (8086): ERREUR', 'error');
                }
            } catch (error) {
                log('❌ Serveur MCP (8086): INACCESSIBLE', 'error');
            }
        }

        async function testMCP() {
            log('🔌 DÉBUT TEST MCP', 'loading');
            
            try {
                // Test Actualités
                const newsResponse = await fetch('http://127.0.0.1:8086/mcp/news');
                const newsData = await newsResponse.json();
                log(`✅ MCP News: ${newsData.actualites.length} actualités récupérées`, 'success');
                
                // Test Recherche
                const searchResponse = await fetch('http://127.0.0.1:8086/mcp/search?q=test');
                const searchData = await searchResponse.json();
                log(`✅ MCP Search: ${searchData.resultats.length} résultats pour "test"`, 'success');
                
                // Test Météo
                const weatherResponse = await fetch('http://127.0.0.1:8086/mcp/weather?city=Paris');
                const weatherData = await weatherResponse.json();
                log(`✅ MCP Weather: ${weatherData.donnees.temperature}°C à ${weatherData.ville}`, 'success');
                
            } catch (error) {
                log(`❌ Erreur MCP: ${error.message}`, 'error');
            }
        }

        async function testMemoire() {
            log('🧠 DÉBUT TEST MÉMOIRE THERMIQUE', 'loading');
            
            // Test avec prompt mémoire
            const promptMemoire = `🧠 MÉMOIRE THERMIQUE ACTIVÉE:
📚 SOUVENIRS: Test de la mémoire thermique JARVIS
🎯 CONTEXTE: Jean-Luc teste le système
MESSAGE: Test mémoire thermique`;

            try {
                const response = await fetch('http://127.0.0.1:8080/v1/chat/completions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        messages: [{ role: 'user', content: promptMemoire }],
                        max_tokens: 100
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ Mémoire Thermique: Réponse générée avec contexte', 'success');
                    log(`📝 Réponse: ${data.choices[0].message.content.substring(0, 100)}...`, 'info');
                } else {
                    log('❌ Mémoire Thermique: Erreur de réponse', 'error');
                }
            } catch (error) {
                log(`❌ Erreur Mémoire: ${error.message}`, 'error');
            }
        }

        async function testInterface() {
            log('🌐 DÉBUT TEST INTERFACE', 'loading');
            
            try {
                const response = await fetch('http://127.0.0.1:8085/');
                const html = await response.text();
                
                // Vérifier les éléments clés
                const checks = [
                    { name: 'Sidebar JARVIS', pattern: 'jarvis-sidebar' },
                    { name: 'MCP Tools', pattern: 'MCP TOOLS' },
                    { name: 'Mémoire Thermique', pattern: 'MÉMOIRE THERMIQUE' },
                    { name: 'Conversations', pattern: 'CONVERSATIONS' },
                    { name: 'Settings', pattern: 'SETTINGS' },
                    { name: 'JavaScript MCP', pattern: 'mcpRechercheLlama' },
                    { name: 'Détection mots-clés', pattern: 'detecterMotsClesMemoireEtAugmenter' }
                ];
                
                checks.forEach(check => {
                    if (html.includes(check.pattern)) {
                        log(`✅ Interface: ${check.name} présent`, 'success');
                    } else {
                        log(`❌ Interface: ${check.name} manquant`, 'error');
                    }
                });
                
            } catch (error) {
                log(`❌ Erreur Interface: ${error.message}`, 'error');
            }
        }

        async function testComplet() {
            log('🚀 DÉBUT TEST COMPLET', 'loading');
            
            await testServeurs();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testMCP();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testMemoire();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testInterface();
            
            log('🎯 TEST COMPLET TERMINÉ', 'success');
            log('📊 Vérifiez les résultats ci-dessus pour le statut détaillé', 'info');
        }

        // Auto-test au chargement
        window.onload = function() {
            log('🧪 Interface de test JARVIS chargée', 'success');
            log('👆 Cliquez sur les boutons pour lancer les tests', 'info');
        };
    </script>
</body>
</html>
