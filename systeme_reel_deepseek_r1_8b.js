// 🤖 SYSTÈME RÉEL : DEEPSEEK R1 8B (4.5GB) + MÉMOIRE THERMIQUE
// ⚡ VRAI MODÈLE R1 8B + VRAIE MÉMOIRE - PAS DE SIMULATION ⚡

const fs = require('fs');
const { spawn } = require('child_process');

class SystemeReelDeepSeekR1_8B {
    constructor() {
        this.memoryFile = './thermal_memory_persistent.json';
        this.modele = 'deepseek-r1:8b';
        
        console.log('🤖 SYSTÈME RÉEL DEEPSEEK R1 8B (4.5GB) + MÉMOIRE THERMIQUE');
        console.log('⚡ VRAI MODÈLE R1 8B + VRAIE MÉMOIRE - PAS DE SIMULATION');
        console.log(`🧠 Modèle: ${this.modele}`);
        console.log(`🌡️ Mémoire: ${this.memoryFile}`);
        
        this.verifierConnexions();
    }
    
    // 🔍 VÉRIFIER CONNEXIONS RÉELLES
    async verifierConnexions() {
        console.log('\n🔍 Vérification connexions réelles...');
        
        // Vérifier mémoire thermique
        const memoireOK = this.verifierMemoireThermique();
        
        // Vérifier Ollama
        const ollamaOK = await this.verifierOllama();
        
        if (memoireOK && ollamaOK) {
            console.log('✅ TOUTES LES CONNEXIONS RÉELLES SONT OK !');
            this.demarrerSystemeReel();
        } else {
            console.log('❌ Problème de connexions - Vérifiez vos services');
            this.afficherInstructions();
        }
    }
    
    // 🌡️ VÉRIFIER VRAIE MÉMOIRE THERMIQUE
    verifierMemoireThermique() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                const memory = JSON.parse(data);
                
                const qi = memory.neural_system?.qi_level || 0;
                const neurones = memory.neural_system?.active_neurons || 0;
                const temperature = memory.neural_system?.neural_temperature || 0;
                
                console.log('🌡️ VRAIE MÉMOIRE THERMIQUE CONNECTÉE :');
                console.log(`   QI Level: ${qi}`);
                console.log(`   Neurones actifs: ${neurones}`);
                console.log(`   Température: ${temperature}`);
                
                return true;
            } else {
                console.log('❌ Fichier mémoire thermique non trouvé');
                return false;
            }
        } catch (error) {
            console.error('❌ Erreur mémoire thermique:', error.message);
            return false;
        }
    }
    
    // 🤖 VÉRIFIER OLLAMA ET DEEPSEEK R1 8B
    async verifierOllama() {
        return new Promise((resolve) => {
            // Vérifier si Ollama est installé
            const ollamaCheck = spawn('ollama', ['list'], { stdio: 'pipe' });
            
            let output = '';
            ollamaCheck.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            ollamaCheck.on('close', (code) => {
                if (code === 0) {
                    console.log('🤖 OLLAMA INSTALLÉ ✅');
                    
                    // Vérifier si DeepSeek R1 8B est téléchargé
                    if (output.includes('deepseek-r1:8b')) {
                        console.log('🧠 DEEPSEEK R1 8B DISPONIBLE ✅');
                        resolve(true);
                    } else {
                        console.log('❌ DeepSeek R1 8B non téléchargé');
                        console.log('   Téléchargez avec: ollama pull deepseek-r1:8b');
                        resolve(false);
                    }
                } else {
                    console.log('❌ Ollama non installé ou non accessible');
                    resolve(false);
                }
            });
            
            ollamaCheck.on('error', () => {
                console.log('❌ Ollama non installé');
                resolve(false);
            });
        });
    }
    
    // 📋 AFFICHER INSTRUCTIONS
    afficherInstructions() {
        console.log('\n📋 INSTRUCTIONS POUR INSTALLER:');
        console.log('');
        console.log('1. INSTALLER OLLAMA:');
        console.log('   curl -fsSL https://ollama.com/install.sh | sh');
        console.log('');
        console.log('2. TÉLÉCHARGER DEEPSEEK R1 8B (4.5GB):');
        console.log('   ollama pull deepseek-r1:8b');
        console.log('');
        console.log('3. RELANCER CE SCRIPT');
        console.log('');
    }
    
    // 🚀 DÉMARRER SYSTÈME RÉEL
    demarrerSystemeReel() {
        console.log('\n🚀 DÉMARRAGE SYSTÈME RÉEL...');
        
        // Créer agents réels
        this.agent1 = new VraiAgentDeepSeekR1('Agent 1', this);
        this.agent2 = new VraiAgentDeepSeekR1('Agent 2', this);
        
        // Connexion magnétique réelle
        this.connecterAgentsMagnetiquement();
        
        console.log('\n🧲 AGENTS RÉELS CONNECTÉS MAGNÉTIQUEMENT !');
        console.log('🎯 Tapez une question pour tester le système réel:');
        
        this.demarrerInterface();
    }
    
    // 🧲 CONNEXION MAGNÉTIQUE RÉELLE
    connecterAgentsMagnetiquement() {
        // Agent 2 SORTIE → Agent 1 ENTRÉE
        this.agent2.connecterSortie((message) => {
            console.log('🧲 CONNEXION MAGNÉTIQUE: Agent 2 → Agent 1');
            this.agent1.entree(message);
        });
        
        // Agent 1 SORTIE → Agent 2 ENTRÉE
        this.agent1.connecterSortie((reponse) => {
            console.log('🧲 CONNEXION MAGNÉTIQUE: Agent 1 → Agent 2');
            console.log('✅ CYCLE MAGNÉTIQUE RÉEL COMPLET !');
            console.log(`🎯 Réponse finale: "${reponse.substring(0, 100)}..."`);
        });
        
        console.log('🧲 Connexion magnétique établie entre agents réels');
    }
    
    // 📱 INTERFACE UTILISATEUR
    demarrerInterface() {
        process.stdin.on('data', async (data) => {
            const input = data.toString().trim();
            
            if (input === 'exit') {
                console.log('👋 Arrêt système réel');
                process.exit(0);
            } else if (input === 'memoire') {
                this.afficherMemoireThermique();
            } else if (input === 'stats') {
                this.afficherStatistiques();
            } else if (input.length > 0) {
                console.log(`\n👤 UTILISATEUR: "${input}"`);
                console.log('📤 Envoi vers système réel...');
                
                // Enrichir avec mémoire thermique
                const messageEnrichi = await this.enrichirAvecMemoireThermique(input);
                
                // Envoyer à Agent 2
                this.agent2.entree(messageEnrichi);
            }
        });
        
        console.log('\n📱 COMMANDES:');
        console.log('   - Tapez votre question');
        console.log('   - "memoire" pour voir la mémoire thermique');
        console.log('   - "stats" pour les statistiques');
        console.log('   - "exit" pour quitter');
    }
    
    // 🌡️ ENRICHIR AVEC MÉMOIRE THERMIQUE
    async enrichirAvecMemoireThermique(question) {
        try {
            const memory = JSON.parse(fs.readFileSync(this.memoryFile, 'utf8'));
            
            const qi = memory.neural_system?.qi_level || 0;
            const neurones = memory.neural_system?.active_neurons || 0;
            
            const contexte = `[CONTEXTE MÉMOIRE THERMIQUE: QI=${qi}, Neurones=${neurones}] ${question}`;
            
            console.log('🌡️ Question enrichie avec mémoire thermique');
            return contexte;
            
        } catch (error) {
            console.log('⚠️ Erreur enrichissement mémoire thermique');
            return question;
        }
    }
    
    // 📊 AFFICHER MÉMOIRE THERMIQUE
    afficherMemoireThermique() {
        try {
            const memory = JSON.parse(fs.readFileSync(this.memoryFile, 'utf8'));
            
            console.log('\n🌡️ MÉMOIRE THERMIQUE ACTUELLE:');
            console.log(`   QI Level: ${memory.neural_system?.qi_level || 0}`);
            console.log(`   Neurones actifs: ${memory.neural_system?.active_neurons || 0}`);
            console.log(`   Température: ${memory.neural_system?.neural_temperature || 0}`);
            console.log(`   Zones thermiques: ${Object.keys(memory.thermal_zones || {}).length}`);
            
        } catch (error) {
            console.log('❌ Erreur lecture mémoire thermique');
        }
    }
    
    // 📈 AFFICHER STATISTIQUES
    afficherStatistiques() {
        console.log('\n📈 STATISTIQUES SYSTÈME RÉEL:');
        console.log(`   Agent 1 actif: ${this.agent1 ? '✅' : '❌'}`);
        console.log(`   Agent 2 actif: ${this.agent2 ? '✅' : '❌'}`);
        console.log(`   Mémoire thermique: ${fs.existsSync(this.memoryFile) ? '✅' : '❌'}`);
        console.log(`   Modèle: ${this.modele} (4.5GB)`);
    }
}

// 🤖 VRAI AGENT DEEPSEEK R1 8B
class VraiAgentDeepSeekR1 {
    constructor(nom, systeme) {
        this.nom = nom;
        this.systeme = systeme;
        this.sortie = null;
        
        console.log(`🤖 ${this.nom} - VRAI DEEPSEEK R1 8B initialisé`);
    }
    
    // 📥 ENTRÉE MAGNÉTIQUE
    async entree(message) {
        console.log(`📥 ${this.nom} ENTRÉE: "${message.substring(0, 50)}..."`);
        
        try {
            // Appeler VRAI DeepSeek R1 8B via Ollama
            const reponse = await this.appellerVraiDeepSeekR1(message);
            
            // SORTIE MAGNÉTIQUE
            if (this.sortie) {
                console.log(`📤 ${this.nom} SORTIE: Envoi réponse réelle`);
                this.sortie(reponse);
            }
            
        } catch (error) {
            console.error(`❌ ${this.nom}: Erreur:`, error.message);
        }
    }
    
    // 🧠 APPELER VRAI DEEPSEEK R1 8B
    async appellerVraiDeepSeekR1(prompt) {
        return new Promise((resolve, reject) => {
            console.log(`🧠 ${this.nom}: Appel VRAI DeepSeek R1 8B via Ollama...`);
            
            const ollama = spawn('ollama', ['run', this.systeme.modele], {
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            let reponse = '';
            let erreur = '';
            
            // Envoyer le prompt
            ollama.stdin.write(prompt + '\n');
            ollama.stdin.end();
            
            // Collecter la réponse
            ollama.stdout.on('data', (data) => {
                reponse += data.toString();
            });
            
            ollama.stderr.on('data', (data) => {
                erreur += data.toString();
            });
            
            ollama.on('close', (code) => {
                if (code === 0 && reponse.trim()) {
                    console.log(`✅ ${this.nom}: VRAIE réponse DeepSeek R1 8B reçue`);
                    resolve(reponse.trim());
                } else {
                    reject(new Error(`Erreur Ollama: ${erreur || 'Pas de réponse'}`));
                }
            });
            
            ollama.on('error', (error) => {
                reject(error);
            });
        });
    }
    
    // 🔌 CONNEXION MAGNÉTIQUE
    connecterSortie(fonctionSortie) {
        this.sortie = fonctionSortie;
        console.log(`🧲 ${this.nom}: SORTIE magnétique connectée`);
    }
}

// 🚀 DÉMARRAGE
console.log('🚀 Initialisation système réel DeepSeek R1 8B + Mémoire Thermique...');
console.log('⚠️  PRÉREQUIS:');
console.log('   1. Ollama installé');
console.log('   2. DeepSeek R1 8B téléchargé (4.5GB)');
console.log('   3. Fichier thermal_memory_persistent.json');
console.log('');

const systemeReel = new SystemeReelDeepSeekR1_8B();

module.exports = SystemeReelDeepSeekR1_8B;
