#!/usr/bin/env python3
import requests
import gzip
from io import BytesIO

def recuperer_interface():
    try:
        # Faire la requête avec les bons headers
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'en-US,en;q=0.5',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
        
        response = requests.get('http://localhost:8000/', headers=headers, timeout=10)
        
        if response.status_code == 200:
            # Récupérer le contenu directement
            content = response.text
            
            # Sauvegarder
            with open('interface_exacte_llama.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"Interface récupérée ! Taille: {len(content)} caractères")
            return True
            
        else:
            print(f"Erreur HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Erreur: {e}")
        return False

if __name__ == "__main__":
    recuperer_interface()
