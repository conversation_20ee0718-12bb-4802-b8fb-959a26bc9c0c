<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Simulation Mémoire Thermique - JARVIS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 1200px; margin: 0 auto;
            display: grid; grid-template-columns: 1fr 1fr; gap: 20px;
        }
        .panel {
            background: rgba(255,255,255,0.1);
            padding: 20px; border-radius: 15px;
            border-left: 4px solid #4CAF50;
        }
        .conversation {
            background: #000; color: #0f0; padding: 15px;
            border-radius: 8px; font-family: monospace;
            font-size: 14px; margin: 10px 0;
            border-left: 3px solid #0f0;
        }
        .memory-entry {
            background: rgba(255, 152, 0, 0.2); padding: 10px;
            border-radius: 5px; margin: 5px 0;
            border-left: 3px solid #FF9800;
            font-size: 12px;
        }
        .prompt-comparison {
            display: grid; grid-template-columns: 1fr 1fr; gap: 10px;
            margin: 15px 0;
        }
        .prompt-box {
            background: rgba(0,0,0,0.5); padding: 10px;
            border-radius: 5px; font-family: monospace;
            font-size: 12px;
        }
        .without-memory { border-left: 3px solid #f44336; }
        .with-memory { border-left: 3px solid #4CAF50; }
        .result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .success { background: rgba(76, 175, 80, 0.3); border-left: 4px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border-left: 4px solid #f44336; }
        .info { background: rgba(33, 150, 243, 0.3); border-left: 4px solid #2196F3; }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 12px 24px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 16px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        .verdict {
            grid-column: 1 / -1; text-align: center; font-size: 1.5em;
            padding: 20px; margin: 20px 0; border-radius: 10px; font-weight: bold;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center;">🧪 Simulation Complète - Mémoire Thermique JARVIS</h1>
    <p style="text-align: center;">Démonstration exacte du fonctionnement de la mémoire thermique avec DeepSeek R1 8B</p>

    <div class="container">
        <div class="panel">
            <h2>🧠 Fonctionnement Mémoire Thermique</h2>
            <button onclick="simulateMemoryFlow()">🚀 Simuler Flux Mémoire</button>
            <button onclick="clearSimulation()">🗑️ Effacer</button>
            <div id="memoryFlow"></div>
        </div>

        <div class="panel">
            <h2>📊 Comparaison Prompts</h2>
            <button onclick="comparePrompts()">🔍 Comparer Sans/Avec Mémoire</button>
            <div id="promptComparison"></div>
        </div>

        <div class="panel">
            <h2>🎭 Simulation Réponses Agent</h2>
            <button onclick="simulateAgentResponses()">🤖 Simuler Réponses</button>
            <div id="agentSimulation"></div>
        </div>

        <div class="panel">
            <h2>🔍 Analyse Fonctionnement</h2>
            <button onclick="analyzeMemoryFunction()">📋 Analyser Fonctions</button>
            <div id="functionAnalysis"></div>
        </div>

        <div id="verdict"></div>
    </div>

    <script>
        let thermalMemory = [];
        let simulationStep = 0;

        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            container.appendChild(div);
        }

        function addConversation(containerId, user, agent, hasMemory = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = 'conversation';
            div.innerHTML = `
                <div style="color: #4CAF50;">👤 User: ${user}</div>
                <div style="color: #2196F3;">🤖 Agent: ${agent}</div>
                ${hasMemory ? '<div style="color: #FF9800;">🧠 Mémoire utilisée</div>' : ''}
            `;
            container.appendChild(div);
        }

        function saveThermalMemory(userMessage, agentResponse) {
            const entry = {
                id: Date.now() + Math.random(),
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse
            };
            
            thermalMemory.push(entry);
            return entry;
        }

        function searchMemory(query) {
            if (thermalMemory.length === 0) return '';
            
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom', 'comment je', 'qui suis'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                const results = thermalMemory.filter(entry => 
                    entry.user?.toLowerCase().includes('appelle') || 
                    entry.user?.toLowerCase().includes('nom') ||
                    entry.user?.toLowerCase().includes('suis')
                );
                
                if (results.length > 0) {
                    return '\n\nCONTEXTE MÉMOIRE THERMIQUE:\n' + 
                           results.map(r => `- Précédemment: "${r.user}" → "${r.agent}"`).join('\n') + 
                           '\nUtilise ce contexte pour répondre de manière cohérente.\n';
                }
            }
            
            return '';
        }

        async function simulateMemoryFlow() {
            const container = document.getElementById('memoryFlow');
            container.innerHTML = '';
            thermalMemory = [];
            
            log('memoryFlow', '🚀 Début simulation flux mémoire thermique', 'info');
            
            // Étape 1: Première interaction
            await new Promise(resolve => setTimeout(resolve, 500));
            log('memoryFlow', '📝 Étape 1: Première interaction', 'info');
            
            const user1 = 'Bonjour, je m\'appelle Jean-Luc';
            const agent1 = 'Bonjour Jean-Luc ! Ravi de faire votre connaissance.';
            
            const entry1 = saveThermalMemory(user1, agent1);
            addConversation('memoryFlow', user1, agent1);
            
            const memoryDiv1 = document.createElement('div');
            memoryDiv1.className = 'memory-entry';
            memoryDiv1.innerHTML = `💾 Sauvegardé en mémoire: "${user1}" → "${agent1}"`;
            container.appendChild(memoryDiv1);
            
            // Étape 2: Question avec mémoire
            await new Promise(resolve => setTimeout(resolve, 1000));
            log('memoryFlow', '📝 Étape 2: Question avec recherche mémoire', 'info');
            
            const user2 = 'Tu te souviens de mon nom ?';
            const memoryContext = searchMemory(user2);
            
            if (memoryContext) {
                log('memoryFlow', `🧠 Contexte trouvé: ${memoryContext.length} caractères`, 'success');
                
                const memoryDiv2 = document.createElement('div');
                memoryDiv2.className = 'memory-entry';
                memoryDiv2.innerHTML = `🔍 Recherche activée: "${user2}"<br>📋 Contexte trouvé: "${entry1.user}" → "${entry1.agent}"`;
                container.appendChild(memoryDiv2);
                
                const agent2 = 'Oui, vous vous appelez Jean-Luc ! Nous venons de nous présenter.';
                addConversation('memoryFlow', user2, agent2, true);
                saveThermalMemory(user2, agent2);
                
                log('memoryFlow', '✅ Agent utilise la mémoire thermique avec succès', 'success');
            } else {
                log('memoryFlow', '❌ Aucun contexte trouvé', 'error');
            }
        }

        function comparePrompts() {
            const container = document.getElementById('promptComparison');
            container.innerHTML = '<h3>📋 Comparaison des Prompts</h3>';
            
            const user1 = 'Bonjour, je m\'appelle Jean-Luc';
            const agent1 = 'Bonjour Jean-Luc ! Ravi de faire votre connaissance.';
            saveThermalMemory(user1, agent1);
            
            const user2 = 'Tu te souviens de mon nom ?';
            const memoryContext = searchMemory(user2);
            
            const comparisonDiv = document.createElement('div');
            comparisonDiv.className = 'prompt-comparison';
            comparisonDiv.innerHTML = `
                <div class="prompt-box without-memory">
                    <strong>❌ SANS Mémoire Thermique:</strong><br>
                    "${user2}"
                    <br><br>
                    <em>→ Agent ne peut pas se souvenir</em>
                </div>
                <div class="prompt-box with-memory">
                    <strong>✅ AVEC Mémoire Thermique:</strong><br>
                    "${user2}${memoryContext}"
                    <br><br>
                    <em>→ Agent a le contexte nécessaire</em>
                </div>
            `;
            container.appendChild(comparisonDiv);
            
            log('promptComparison', `🔍 Différence: ${memoryContext.length} caractères de contexte ajoutés`, 'info');
        }

        function simulateAgentResponses() {
            const container = document.getElementById('agentSimulation');
            container.innerHTML = '<h3>🤖 Simulation Réponses Agent</h3>';
            
            // Simulation sans mémoire
            log('agentSimulation', '❌ Simulation SANS mémoire thermique:', 'error');
            addConversation('agentSimulation', 
                'Tu te souviens de mon nom ?', 
                'Je suis désolé, je n\'ai pas d\'information sur votre nom dans notre conversation actuelle.'
            );
            
            // Simulation avec mémoire
            log('agentSimulation', '✅ Simulation AVEC mémoire thermique:', 'success');
            addConversation('agentSimulation', 
                'Tu te souviens de mon nom ?', 
                'Oui, vous vous appelez Jean-Luc ! Vous vous êtes présenté au début de notre conversation.', 
                true
            );
            
            log('agentSimulation', '🎯 Résultat: La mémoire thermique permet la continuité conversationnelle', 'success');
        }

        function analyzeMemoryFunction() {
            const container = document.getElementById('functionAnalysis');
            container.innerHTML = '<h3>🔍 Analyse des Fonctions</h3>';
            
            // Test des fonctions
            thermalMemory = [];
            
            // Test sauvegarde
            const testEntry = saveThermalMemory('Test user', 'Test agent');
            if (thermalMemory.length === 1) {
                log('functionAnalysis', '✅ Fonction saveThermalMemory: OK', 'success');
            } else {
                log('functionAnalysis', '❌ Fonction saveThermalMemory: ÉCHEC', 'error');
            }
            
            // Test recherche
            const searchResult = searchMemory('tu te souviens de test');
            if (searchResult.includes('Test user')) {
                log('functionAnalysis', '✅ Fonction searchMemory: OK', 'success');
            } else {
                log('functionAnalysis', '❌ Fonction searchMemory: ÉCHEC', 'error');
            }
            
            // Test triggers
            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom'];
            let triggerCount = 0;
            triggers.forEach(trigger => {
                const result = searchMemory(trigger + ' de quelque chose');
                if (result.length > 0) triggerCount++;
            });
            
            log('functionAnalysis', `🎯 Triggers détectés: ${triggerCount}/${triggers.length}`, 
                triggerCount === triggers.length ? 'success' : 'error');
            
            // Analyse structure mémoire
            if (testEntry.id && testEntry.timestamp && testEntry.user && testEntry.agent) {
                log('functionAnalysis', '✅ Structure mémoire: Complète', 'success');
            } else {
                log('functionAnalysis', '❌ Structure mémoire: Incomplète', 'error');
            }
            
            // Verdict final
            const allWorking = thermalMemory.length === 1 && 
                              searchResult.includes('Test user') && 
                              triggerCount === triggers.length;
            
            if (allWorking) {
                showVerdict('✅ TOUTES LES FONCTIONS MÉMOIRE FONCTIONNENT CORRECTEMENT', 'success');
            } else {
                showVerdict('❌ PROBLÈMES DÉTECTÉS DANS LES FONCTIONS MÉMOIRE', 'error');
            }
        }

        function showVerdict(message, type) {
            const verdictDiv = document.getElementById('verdict');
            verdictDiv.innerHTML = `<div class="verdict ${type}">${message}</div>`;
        }

        function clearSimulation() {
            document.getElementById('memoryFlow').innerHTML = '';
            document.getElementById('promptComparison').innerHTML = '';
            document.getElementById('agentSimulation').innerHTML = '';
            document.getElementById('functionAnalysis').innerHTML = '';
            document.getElementById('verdict').innerHTML = '';
            thermalMemory = [];
        }

        // Initialisation
        console.log('🧪 Simulation mémoire thermique prête');
    </script>
</body>
</html>
