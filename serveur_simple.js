// 🚀 SERVEUR SIMPLE POUR INTERFACE JARVIS
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🚀 SERVEUR SIMPLE JARVIS - DÉMARRAGE');

const server = http.createServer((req, res) => {
    console.log(`📡 Requête: ${req.method} ${req.url}`);
    
    // CORS pour toutes les requêtes
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // Servir l'interface simple
    if (req.method === 'GET' && req.url === '/') {
        try {
            const html = fs.readFileSync('./interface_simple_qui_marche.html', 'utf8');
            
            res.writeHead(200, {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache'
            });
            res.end(html);
            
            console.log('✅ Interface simple servie');
            
        } catch (error) {
            console.error('❌ Erreur lecture interface:', error.message);
            res.writeHead(500);
            res.end('Erreur serveur');
        }
    }
    
    // 404 pour le reste
    else {
        res.writeHead(404);
        res.end('Page non trouvée');
    }
});

// Démarrage serveur
const PORT = 8095;
server.listen(PORT, () => {
    console.log(`✅ SERVEUR SIMPLE ACTIF sur http://127.0.0.1:${PORT}`);
    console.log('🌐 Interface simple: http://127.0.0.1:8095');
    console.log('🤖 Connexion directe à llama.cpp: http://127.0.0.1:8080');
    console.log('🔌 MCP disponible: http://127.0.0.1:8086');
    console.log('');
    console.log('🎯 INSTRUCTIONS:');
    console.log('1. Ouvrir http://127.0.0.1:8095 dans le navigateur');
    console.log('2. Taper un message');
    console.log('3. Cliquer "🚀 Envoyer"');
    console.log('4. Voir la réponse de JARVIS');
});

// Gestion erreurs
server.on('error', (error) => {
    console.error('❌ Erreur serveur:', error.message);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt SERVEUR SIMPLE');
    server.close();
    process.exit(0);
});
