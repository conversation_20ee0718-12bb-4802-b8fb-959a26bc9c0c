<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 TEST INTERFACE ORIGINALE - Mémoire Thermique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px; border-radius: 15px;
        }
        .test-log {
            background: #000; color: #0f0; padding: 20px;
            border-radius: 10px; font-family: monospace;
            font-size: 14px; height: 400px; overflow-y: auto;
            margin: 20px 0; border: 2px solid #0f0;
        }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 15px 30px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 16px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .status {
            padding: 10px; border-radius: 5px; margin: 10px 0;
            font-weight: bold; text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .warning { background: rgba(255, 152, 0, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 TEST INTERFACE ORIGINALE</h1>
        <p>Test de la mémoire thermique dans votre interface sur http://localhost:8080</p>

        <button onclick="testInterfaceOriginale()" id="testBtn">🚀 TESTER INTERFACE ORIGINALE</button>
        <button onclick="testServeur()">🔧 Test Serveur Seul</button>
        <button onclick="clearAll()">🗑️ Effacer</button>

        <div id="status"></div>
        <div class="test-log" id="testLog">[PRÊT] Cliquez sur "TESTER INTERFACE ORIGINALE" pour commencer</div>
        <div id="results"></div>
    </div>

    <script>
        let testLog = document.getElementById('testLog');
        let results = document.getElementById('results');
        let status = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#0f0', error: '#f44', success: '#4f4', warning: '#fa0' };
            testLog.innerHTML += `<div style="color: ${colors[type]}">[${timestamp}] ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function showStatus(message, type) {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Test de l'interface originale
        async function testInterfaceOriginale() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            
            try {
                log('🧪 DÉBUT TEST INTERFACE ORIGINALE', 'success');
                log('=' .repeat(50), 'info');
                
                showStatus('🔧 Test interface sur localhost:8080...', 'warning');
                
                // Test 1: Vérifier que l'interface est accessible
                log('📝 ÉTAPE 1: Vérification interface accessible', 'info');
                
                try {
                    const interfaceResponse = await fetch('http://localhost:8080/interface_8080_reelle.html');
                    if (interfaceResponse.ok) {
                        log('✅ Interface accessible sur localhost:8080', 'success');
                        showStatus('✅ Interface accessible', 'success');
                    } else {
                        throw new Error(`Interface inaccessible: ${interfaceResponse.status}`);
                    }
                } catch (error) {
                    log(`❌ Interface inaccessible: ${error.message}`, 'error');
                    showStatus('❌ Interface inaccessible', 'error');
                    return;
                }
                
                // Test 2: Vérifier le serveur DeepSeek
                log('📝 ÉTAPE 2: Vérification serveur DeepSeek', 'info');
                
                try {
                    const healthResponse = await fetch('http://localhost:8000/health');
                    if (healthResponse.ok) {
                        log('✅ Serveur DeepSeek accessible', 'success');
                    } else {
                        throw new Error(`Serveur inaccessible: ${healthResponse.status}`);
                    }
                } catch (error) {
                    log(`❌ Serveur DeepSeek inaccessible: ${error.message}`, 'error');
                    showStatus('❌ Serveur DeepSeek inaccessible', 'error');
                    return;
                }
                
                // Test 3: Test simple avec le serveur
                log('📝 ÉTAPE 3: Test communication serveur', 'info');
                showStatus('📝 Test communication...', 'warning');
                
                const testMessage = 'Bonjour, je m\'appelle Jean-Luc';
                log(`📤 Envoi: "${testMessage}"`, 'info');
                
                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: testMessage,
                        n_predict: 50,
                        temperature: 0.3,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`Erreur serveur: ${response.status}`);
                }

                const data = await response.json();
                const reply = (data.content || '').trim();
                
                log(`📥 Réponse: "${reply.substring(0, 100)}..."`, 'success');
                
                // Test 4: Vérifier si la réponse contient le nom
                log('📝 ÉTAPE 4: Vérification mémoire', 'info');
                
                if (reply.toLowerCase().includes('jean-luc')) {
                    log('🎉 SUCCÈS ! Le serveur utilise le nom "Jean-Luc"', 'success');
                    showStatus('🎉 SUCCÈS ! Mémoire fonctionne', 'success');
                } else {
                    log('⚠️ Le serveur ne semble pas utiliser le nom directement', 'warning');
                    showStatus('⚠️ Test mémoire à vérifier manuellement', 'warning');
                }
                
                // Instructions finales
                log('=' .repeat(50), 'info');
                log('📋 INSTRUCTIONS POUR TEST MANUEL:', 'info');
                log('1. Ouvrez http://localhost:8080/interface_8080_reelle.html', 'info');
                log('2. Tapez: "Bonjour, je m\'appelle Jean-Luc"', 'info');
                log('3. Attendez la réponse', 'info');
                log('4. Tapez: "Tu te souviens de mon nom ?"', 'info');
                log('5. Vérifiez si l\'agent répond avec "Jean-Luc"', 'info');
                log('=' .repeat(50), 'info');
                
                showStatus('✅ Test terminé - Vérifiez manuellement', 'success');
                
            } catch (error) {
                log(`❌ TEST ÉCHOUÉ: ${error.message}`, 'error');
                showStatus(`❌ Test échoué: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
            }
        }

        // Test serveur seul
        async function testServeur() {
            log('🔧 TEST SERVEUR SEUL', 'success');
            
            try {
                showStatus('🔧 Test serveur...', 'warning');
                
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Serveur OK: ${JSON.stringify(data)}`, 'success');
                    showStatus('✅ Serveur accessible', 'success');
                } else {
                    throw new Error(`Erreur ${response.status}`);
                }
            } catch (error) {
                log(`❌ Serveur inaccessible: ${error.message}`, 'error');
                showStatus('❌ Serveur inaccessible', 'error');
            }
        }

        function clearAll() {
            testLog.innerHTML = '[EFFACÉ] Prêt pour un nouveau test';
            results.innerHTML = '';
            status.innerHTML = '';
        }

        // Test initial
        log('🧪 Test interface originale prêt', 'success');
        log('📍 Interface cible: http://localhost:8080/interface_8080_reelle.html', 'info');
        log('🎯 Serveur cible: http://localhost:8000', 'info');
    </script>
</body>
</html>
