<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>llama.cpp - JARVIS avec Mémoire Thermique</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .new-conversation {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .conversations {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }
        
        .conversation-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
        }
        
        .conversation-item:hover {
            background: #f0f0f0;
        }
        
        .conversation-item.active {
            background: #e3f2fd;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .model-info {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
        }
        
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: white;
        }
        
        .message {
            margin-bottom: 20px;
            max-width: 80%;
        }
        
        .message.user {
            margin-left: auto;
            text-align: right;
        }
        
        .message.assistant {
            margin-right: auto;
        }
        
        .message-content {
            padding: 12px 16px;
            border-radius: 18px;
            display: inline-block;
            max-width: 100%;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #007bff;
            color: white;
        }
        
        .message.assistant .message-content {
            background: #f1f3f4;
            color: #333;
        }
        
        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }
        
        .input-wrapper {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
        }
        
        .input-wrapper:focus-within {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        #messageInput {
            flex: 1;
            border: none;
            background: none;
            outline: none;
            font-size: 16px;
            padding: 5px;
        }
        
        #sendButton {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
        }
        
        #sendButton:hover {
            background: #0056b3;
        }
        
        #sendButton:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            font-size: 12px;
            color: #666;
        }
        
        .loading {
            display: none;
        }
        
        .memory-indicator {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 10px;
        }
        
        .empty-state {
            text-align: center;
            color: #666;
            margin-top: 100px;
        }
        
        .empty-state h2 {
            color: #333;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <button class="new-conversation" onclick="startNewConversation()">
                ✨ New conversation
            </button>
        </div>
        <div class="conversations" id="conversationsList">
            <!-- Les conversations seront ajoutées ici -->
        </div>
    </div>
    
    <div class="main-content">
        <div class="header">
            <h1>llama.cpp</h1>
            <div>
                <span class="model-info" id="modelInfo">DeepSeek-R1-8B-Q4_K_L.gguf</span>
                <span class="memory-indicator">🧠 Mémoire Thermique</span>
            </div>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="empty-state">
                <h2>Send a message to start</h2>
                <p>Your conversation will be saved in thermal memory</p>
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-wrapper">
                <input type="text" id="messageInput" placeholder="Type a message (Shift+Enter to add a new line)" />
                <button id="sendButton">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </div>
        
        <div class="status">
            <span id="statusText">Ready</span>
            <span class="loading" id="loadingText">Generating response...</span>
        </div>
    </div>

    <script>
        // Configuration
        const API_URL = 'http://localhost:8000/completion';
        const MEMORY_FILE = 'thermal_memory_persistent.json';
        
        let currentConversation = null;
        let conversations = [];
        let thermalMemory = [];
        
        // Éléments DOM
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusText = document.getElementById('statusText');
        const loadingText = document.getElementById('loadingText');
        const conversationsList = document.getElementById('conversationsList');
        
        // Initialisation
        window.addEventListener('load', async () => {
            await loadThermalMemory();
            await loadConversations();
            await testConnection();
        });
        
        // Charger la mémoire thermique
        async function loadThermalMemory() {
            try {
                const response = await fetch(MEMORY_FILE);
                if (response.ok) {
                    const data = await response.json();
                    thermalMemory = data.conversations || [];
                    console.log(`Mémoire thermique chargée: ${thermalMemory.length} conversations`);
                }
            } catch (error) {
                console.log('Pas de mémoire thermique existante, création d\'une nouvelle');
                thermalMemory = [];
            }
        }
        
        // Sauvegarder la mémoire thermique
        async function saveThermalMemory() {
            const memoryData = {
                conversations: thermalMemory,
                lastUpdate: new Date().toISOString()
            };
            
            // Simulation de sauvegarde (dans un vrai système, utiliser une API)
            localStorage.setItem('thermalMemory', JSON.stringify(memoryData));
            console.log('Mémoire thermique sauvegardée');
        }
        
        // Charger les conversations
        async function loadConversations() {
            conversations = thermalMemory.slice(-10); // 10 dernières conversations
            updateConversationsList();
        }
        
        // Mettre à jour la liste des conversations
        function updateConversationsList() {
            conversationsList.innerHTML = '';
            conversations.forEach((conv, index) => {
                const item = document.createElement('div');
                item.className = 'conversation-item';
                item.textContent = conv.title || `Conversation ${index + 1}`;
                item.onclick = () => loadConversation(conv);
                conversationsList.appendChild(item);
            });
        }
        
        // Nouvelle conversation
        function startNewConversation() {
            currentConversation = {
                id: Date.now(),
                title: '',
                messages: [],
                timestamp: new Date().toISOString()
            };
            
            chatContainer.innerHTML = `
                <div class="empty-state">
                    <h2>Send a message to start</h2>
                    <p>Your conversation will be saved in thermal memory</p>
                </div>
            `;
        }
        
        // Charger une conversation
        function loadConversation(conversation) {
            currentConversation = conversation;
            chatContainer.innerHTML = '';
            
            conversation.messages.forEach(msg => {
                addMessageToChat(msg.content, msg.role === 'user');
            });
            
            // Marquer comme active
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }
        
        // Ajouter un message au chat
        function addMessageToChat(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            // Supprimer l'état vide
            const emptyState = chatContainer.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }
        }
        
        // Envoyer un message
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Créer une nouvelle conversation si nécessaire
            if (!currentConversation) {
                startNewConversation();
                currentConversation.title = message.substring(0, 50) + (message.length > 50 ? '...' : '');
            }
            
            // Ajouter le message utilisateur
            addMessageToChat(message, true);
            currentConversation.messages.push({
                role: 'user',
                content: message,
                timestamp: new Date().toISOString()
            });
            
            messageInput.value = '';
            
            // Afficher le loading
            sendButton.disabled = true;
            statusText.style.display = 'none';
            loadingText.style.display = 'inline';
            
            try {
                // Recherche dans la mémoire thermique
                const memoryContext = searchThermalMemory(message);
                let prompt = message;
                
                if (memoryContext.length > 0) {
                    prompt = `Contexte de mémoire thermique:\n${memoryContext.join('\n')}\n\nQuestion actuelle: ${message}`;
                }
                
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        n_predict: 512,
                        temperature: 0.7,
                        stream: false
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const reply = data.content || data.text || 'Pas de réponse';
                
                // Ajouter la réponse
                addMessageToChat(reply);
                currentConversation.messages.push({
                    role: 'assistant',
                    content: reply,
                    timestamp: new Date().toISOString()
                });
                
                // Sauvegarder dans la mémoire thermique
                await saveConversationToMemory();
                
            } catch (error) {
                console.error('Erreur:', error);
                addMessageToChat(`Erreur: ${error.message}`);
            } finally {
                // Cacher le loading
                sendButton.disabled = false;
                statusText.style.display = 'inline';
                loadingText.style.display = 'none';
            }
        }
        
        // Rechercher dans la mémoire thermique
        function searchThermalMemory(query) {
            const keywords = query.toLowerCase().split(' ');
            const results = [];
            
            thermalMemory.forEach(conv => {
                conv.messages.forEach(msg => {
                    const content = msg.content.toLowerCase();
                    if (keywords.some(keyword => content.includes(keyword))) {
                        results.push(`${msg.role}: ${msg.content.substring(0, 200)}`);
                    }
                });
            });
            
            return results.slice(0, 3); // Top 3 résultats
        }
        
        // Sauvegarder la conversation dans la mémoire thermique
        async function saveConversationToMemory() {
            if (!currentConversation) return;
            
            // Ajouter ou mettre à jour la conversation
            const existingIndex = thermalMemory.findIndex(conv => conv.id === currentConversation.id);
            if (existingIndex >= 0) {
                thermalMemory[existingIndex] = currentConversation;
            } else {
                thermalMemory.push(currentConversation);
                conversations.push(currentConversation);
                updateConversationsList();
            }
            
            await saveThermalMemory();
        }
        
        // Tester la connexion
        async function testConnection() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    statusText.textContent = 'Connected to DeepSeek R1 8B';
                    statusText.style.color = 'green';
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                statusText.textContent = 'Server not connected - check localhost:8000';
                statusText.style.color = 'red';
            }
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Démarrer avec une nouvelle conversation
        startNewConversation();
    </script>
</body>
</html>
