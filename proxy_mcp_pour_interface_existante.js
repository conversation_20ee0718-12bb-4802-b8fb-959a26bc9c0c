// 🔌 PROXY MCP POUR TON INTERFACE LLAMA.CPP EXISTANTE
const http = require('http');
const zlib = require('zlib');

class ProxyMCPInterface {
    constructor() {
        this.portProxy = 8091;  // Nouveau port pour interface avec MCP
        this.portLlama = 8080;  // Ton interface originale
        this.nom = "🔌 PROXY MCP INTERFACE";
    }

    demarrer() {
        console.log(`🚀 ${this.nom} - DÉMARRAGE`);
        
        const server = http.createServer((req, res) => {
            try {
                if (req.method === 'GET' && req.url === '/') {
                    this.servirInterfaceAvecMCP(req, res);
                } else if (req.url.startsWith('/mcp/')) {
                    this.gererAPIMCP(req, res);
                } else {
                    this.redirigerVersLlama(req, res);
                }
            } catch (error) {
                this.gererErreur(error, res);
            }
        });

        server.listen(this.portProxy, () => {
            console.log(`✅ ${this.nom} ACTIF sur http://127.0.0.1:${this.portProxy}`);
            console.log(`🔗 Interface originale: http://127.0.0.1:${this.portLlama}`);
            console.log(`🔌 MCP ajouté à ton interface existante !`);
        });
    }

    async servirInterfaceAvecMCP(req, res) {
        try {
            const htmlOriginal = await this.recupererInterfaceOriginale();
            const htmlAvecMCP = this.ajouterMCPAInterface(htmlOriginal);
            
            res.writeHead(200, {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache'
            });
            res.end(htmlAvecMCP);
            
            console.log('✅ Interface avec MCP servie');
            
        } catch (error) {
            this.gererErreur(error, res);
        }
    }

    recupererInterfaceOriginale() {
        return new Promise((resolve, reject) => {
            const req = http.request({
                hostname: '127.0.0.1',
                port: this.portLlama,
                path: '/',
                method: 'GET',
                headers: {
                    'Accept-Encoding': 'identity',  // Pas de compression
                    'User-Agent': 'Mozilla/5.0 (compatible; MCP-Proxy/1.0)'
                }
            }, (res) => {
                let chunks = [];
                res.on('data', chunk => chunks.push(chunk));
                res.on('end', () => {
                    try {
                        let buffer = Buffer.concat(chunks);
                        let html = buffer.toString();

                        // Vérifier si c'est une erreur de compression
                        if (html.includes('gzip is not supported')) {
                            console.log('⚠️ Erreur compression détectée, utilisation interface de base');
                            html = this.creerInterfaceDeBase();
                        }

                        resolve(html);
                    } catch (error) {
                        console.log('⚠️ Erreur récupération, utilisation interface de base');
                        resolve(this.creerInterfaceDeBase());
                    }
                });
            });
            req.on('error', (error) => {
                console.log('⚠️ Erreur connexion, utilisation interface de base');
                resolve(this.creerInterfaceDeBase());
            });
            req.end();
        });
    }

    creerInterfaceDeBase() {
        return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>🔌 JARVIS + MCP - Interface Cognitive</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a2e;
            color: #00ffff;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
        }
        .chat-area {
            background: rgba(0,0,0,0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .message {
            margin: 10px 0;
            padding: 8px;
            border-radius: 5px;
        }
        .user {
            background: rgba(0,255,255,0.1);
            border-left: 4px solid #00ffff;
        }
        .assistant {
            background: rgba(0,255,0,0.1);
            border-left: 4px solid #00ff00;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            background: rgba(0,255,255,0.1);
            border: 1px solid #00ffff;
            color: #00ffff;
            border-radius: 5px;
        }
        button {
            padding: 12px 20px;
            background: linear-gradient(45deg, #00ffff, #00ff00);
            color: #000;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 JARVIS + MCP</h1>
        <p style="text-align: center;">Interface Cognitive avec Model Context Protocol</p>

        <div class="chat-area">
            <div class="messages" id="messages">
                <div class="message assistant">
                    <strong>JARVIS:</strong> Système cognitif initialisé avec support MCP. Accès Internet et outils externes disponibles.
                </div>
            </div>

            <div class="input-area">
                <input type="text" id="messageInput" placeholder="Tapez votre message à JARVIS..." onkeypress="if(event.key==='Enter') sendMessage()">
                <button onclick="sendMessage()">📤 Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;

            addMessage('user', message);
            input.value = '';

            try {
                const response = await fetch('/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: message,
                        n_predict: 200
                    })
                });

                const data = await response.json();
                if (data.content) {
                    addMessage('assistant', data.content);
                }
            } catch (error) {
                addMessage('assistant', 'Erreur de communication avec JARVIS');
            }
        }

        function addMessage(type, content) {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = 'message ' + type;
            div.innerHTML = '<strong>' + (type === 'user' ? 'VOUS' : 'JARVIS') + ':</strong> ' + content;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
    </script>
</body>
</html>`;
    }

    ajouterMCPAInterface(html) {
        console.log('🔌 Ajout MCP à l\'interface existante...');
        
        // 🏷️ MODIFIER TITRE
        html = html.replace(/<title>.*?<\/title>/i, '<title>🔌 JARVIS + MCP - Interface Cognitive</title>');
        
        // 🎨 AJOUTER STYLES MCP
        const stylesMCP = `
        <style>
        /* 🔌 STYLES MCP POUR INTERFACE EXISTANTE */
        .mcp-panel {
            position: fixed !important;
            top: 10px !important;
            right: 10px !important;
            width: 320px !important;
            background: rgba(0,0,0,0.95) !important;
            border: 2px solid #00ff00 !important;
            border-radius: 10px !important;
            padding: 15px !important;
            color: #00ff00 !important;
            font-family: monospace !important;
            z-index: 999999 !important;
            box-shadow: 0 0 20px rgba(0,255,0,0.5) !important;
            font-size: 12px !important;
        }
        
        .mcp-header {
            text-align: center !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #00ff00 !important;
            padding-bottom: 8px !important;
        }
        
        .mcp-tools {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 6px !important;
            margin-bottom: 8px !important;
        }
        
        .mcp-btn {
            padding: 6px !important;
            background: #1a2e1a !important;
            border: 1px solid #00ff00 !important;
            color: #00ff00 !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 10px !important;
            transition: all 0.2s !important;
        }
        
        .mcp-btn:hover {
            background: #00ff00 !important;
            color: #000 !important;
            transform: scale(1.05) !important;
        }
        
        .mcp-status {
            background: rgba(0,255,0,0.1) !important;
            padding: 8px !important;
            border-radius: 5px !important;
            text-align: center !important;
            font-size: 11px !important;
        }
        
        .mcp-minimized {
            height: 40px !important;
            overflow: hidden !important;
        }
        
        /* 🎯 AMÉLIORATION INTERFACE EXISTANTE */
        body {
            position: relative !important;
        }
        </style>`;
        
        // 🔌 PANNEAU MCP
        const panneauMCP = `
        <div class="mcp-panel" id="mcp-panel">
            <div class="mcp-header">
                <h3 style="margin: 0; font-size: 14px;">🔌 MCP TOOLS</h3>
                <div style="font-size: 10px;">Model Context Protocol</div>
            </div>
            
            <div class="mcp-tools">
                <button class="mcp-btn" onclick="mcpRecherche()">🌐 Web Search</button>
                <button class="mcp-btn" onclick="mcpActualites()">📰 News 2025</button>
                <button class="mcp-btn" onclick="mcpMeteo()">🌤️ Weather</button>
                <button class="mcp-btn" onclick="mcpCalcul()">🧮 Calculator</button>
                <button class="mcp-btn" onclick="mcpTraduction()">🌍 Translate</button>
                <button class="mcp-btn" onclick="mcpCode()">💻 Code Gen</button>
            </div>
            
            <div class="mcp-tools">
                <button class="mcp-btn" onclick="mcpToggle()">📱 Réduire</button>
                <button class="mcp-btn" onclick="mcpStatus()">ℹ️ Status</button>
            </div>
            
            <div class="mcp-status">
                <div>🔌 MCP: <span style="color: #00ff00;">ACTIF</span></div>
                <div>🌐 Internet: <span style="color: #00ff00;">CONNECTÉ</span></div>
                <div>🛠️ Outils: 6 disponibles</div>
            </div>
        </div>`;
        
        // 📜 SCRIPT MCP
        const scriptMCP = `
        <script>
        // 🔌 SYSTÈME MCP POUR INTERFACE EXISTANTE
        let mcpMinimized = false;
        
        console.log('🔌 MCP System ajouté à l\'interface existante');
        
        // 🌐 FONCTIONS MCP
        function mcpRecherche() {
            const query = prompt('🌐 Recherche Web - Entrez votre requête:');
            if (query) {
                mcpEnvoyerCommande('search', query);
            }
        }
        
        function mcpActualites() {
            mcpEnvoyerCommande('news', 'Actualités 2025');
        }
        
        function mcpMeteo() {
            const ville = prompt('🌤️ Météo - Entrez la ville:') || 'Paris';
            mcpEnvoyerCommande('weather', ville);
        }
        
        function mcpCalcul() {
            const expression = prompt('🧮 Calculatrice - Entrez l\'expression:');
            if (expression) {
                mcpEnvoyerCommande('calculate', expression);
            }
        }
        
        function mcpTraduction() {
            const texte = prompt('🌍 Traduction - Entrez le texte:');
            if (texte) {
                mcpEnvoyerCommande('translate', texte);
            }
        }
        
        function mcpCode() {
            const langage = prompt('💻 Code - Quel langage?') || 'Python';
            mcpEnvoyerCommande('code', langage);
        }
        
        function mcpToggle() {
            const panel = document.getElementById('mcp-panel');
            if (mcpMinimized) {
                panel.classList.remove('mcp-minimized');
                mcpMinimized = false;
            } else {
                panel.classList.add('mcp-minimized');
                mcpMinimized = true;
            }
        }
        
        function mcpStatus() {
            alert('🔌 MCP STATUS:\\n\\n• Connexion: ACTIVE\\n• Internet: DISPONIBLE\\n• Outils: 6 fonctionnels\\n• Interface: Intégrée\\n\\nVersion: MCP 1.0');
        }
        
        // 🚀 ENVOYER COMMANDE MCP
        async function mcpEnvoyerCommande(type, data) {
            try {
                console.log(\`🔌 MCP: \${type} - \${data}\`);
                
                // Construire prompt MCP pour JARVIS
                const promptMCP = \`[MCP \${type.toUpperCase()}] \${data} - Utilise tes capacités étendues pour traiter cette demande avec accès Internet et outils externes.\`;
                
                // Injecter dans l'interface existante
                const input = document.querySelector('input[type="text"], textarea, #messageInput');
                if (input) {
                    input.value = promptMCP;
                    input.focus();
                    
                    // Déclencher envoi automatique
                    setTimeout(() => {
                        const sendBtn = document.querySelector('button[type="submit"], .send-btn, #sendBtn');
                        if (sendBtn) {
                            sendBtn.click();
                        } else {
                            // Essayer Enter
                            const event = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                keyCode: 13,
                                bubbles: true
                            });
                            input.dispatchEvent(event);
                        }
                    }, 200);
                }
                
            } catch (error) {
                console.error('❌ Erreur MCP:', error);
                alert('❌ Erreur MCP: ' + error.message);
            }
        }
        
        // 🎯 INITIALISATION MCP
        setTimeout(() => {
            console.log('✅ MCP intégré à l\'interface existante !');
        }, 1000);
        </script>`;
        
        // 🔧 INJECTION DANS HTML
        html = html.replace('</head>', stylesMCP + '</head>');
        html = html.replace('</body>', panneauMCP + scriptMCP + '</body>');
        
        console.log('✅ MCP ajouté avec succès à l\'interface');
        return html;
    }

    async gererAPIMCP(req, res) {
        // APIs MCP (pour futures extensions)
        res.writeHead(200, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        });
        res.end(JSON.stringify({ status: 'MCP API active' }));
    }

    redirigerVersLlama(req, res) {
        const options = {
            hostname: '127.0.0.1',
            port: this.portLlama,
            path: req.url,
            method: req.method,
            headers: { ...req.headers }
        };

        delete options.headers.host;

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (error) => {
            this.gererErreur(error, res);
        });

        req.pipe(proxyReq);
    }

    gererErreur(error, res) {
        console.error(`❌ ERREUR:`, error.message);
        
        if (!res.headersSent) {
            res.writeHead(500, { 'Content-Type': 'text/html' });
            res.end(`
                <h1>❌ ERREUR MCP</h1>
                <p>Erreur: ${error.message}</p>
                <button onclick="location.reload()">🔄 Réessayer</button>
            `);
        }
    }
}

// 🚀 DÉMARRAGE
const proxy = new ProxyMCPInterface();
proxy.demarrer();
