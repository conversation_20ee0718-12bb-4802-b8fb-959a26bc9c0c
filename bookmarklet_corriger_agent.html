<!DOCTYPE html>
<html>
<head>
    <title>🎯 Corriger Nom Agent</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #1a1a1a; color: white; }
        .bookmarklet { background: #333; padding: 15px; border-radius: 5px; margin: 10px 0; }
        a { color: #00ff88; text-decoration: none; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🎯 Corriger le Nom de l'Agent</h1>
    
    <p>Glissez ce lien dans vos favoris, puis cliquez dessus quand vous êtes sur l'interface llama.cpp :</p>
    
    <div class="bookmarklet">
        <a href="javascript:(function(){function corrigerNomAgent(){let correctionEffectuee=false;const walker=document.createTreeWalker(document.body,NodeFilter.SHOW_TEXT,null,false);let node;while(node=walker.nextNode()){if(node.textContent&&node.textContent.includes('Send a message to start')){node.textContent=node.textContent.replace('Send a message to start','DeepSeek R1 8B Agent');correctionEffectuee=true;}}const allElements=document.querySelectorAll('*');allElements.forEach(element=>{if(element.textContent&&element.textContent.includes('Send a message to start')&&element.children.length===0){element.textContent=element.textContent.replace('Send a message to start','DeepSeek R1 8B Agent');correctionEffectuee=true;}});return correctionEffectuee;}const observer=new MutationObserver(()=>{corrigerNomAgent();});observer.observe(document.body,{childList:true,subtree:true,characterData:true});let tentatives=0;const maxTentatives=5;function correctionRecursive(){const correctionEffectuee=corrigerNomAgent();tentatives++;if(!correctionEffectuee&&tentatives<maxTentatives){setTimeout(correctionRecursive,1000);}else if(correctionEffectuee){alert('✅ Nom agent corrigé en DeepSeek R1 8B Agent après '+tentatives+' tentative(s)');}else{alert('⚠️ Texte non trouvé après '+maxTentatives+' tentatives');}}setTimeout(correctionRecursive,500);})();">🎯 Corriger Nom Agent</a>
    </div>
    
    <h3>Instructions :</h3>
    <ol>
        <li>Glissez le lien ci-dessus dans votre barre de favoris</li>
        <li>Ouvrez votre interface llama.cpp sur http://localhost:8080/interface_8080_reelle.html</li>
        <li>Cliquez sur le favori "🎯 Corriger Nom Agent"</li>
        <li>Le texte "Send a message to start" sera remplacé par "DeepSeek R1 8B Agent"</li>
    </ol>
</body>
</html>
