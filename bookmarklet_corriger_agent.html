<!DOCTYPE html>
<html>
<head>
    <title>🎯 Corriger Nom Agent</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #1a1a1a; color: white; }
        .bookmarklet { background: #333; padding: 15px; border-radius: 5px; margin: 10px 0; }
        a { color: #00ff88; text-decoration: none; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🎯 Corriger le Nom de l'Agent</h1>
    
    <p>Glissez ce lien dans vos favoris, puis cliquez dessus quand vous êtes sur l'interface llama.cpp :</p>
    
    <div class="bookmarklet">
        <a href="javascript:(function(){const walker=document.createTreeWalker(document.body,NodeFilter.SHOW_TEXT,null,false);const nodesToReplace=[];let node;while(node=walker.nextNode()){if(node.textContent.includes('Send a message to start')){nodesToReplace.push(node);}}nodesToReplace.forEach(node=>{node.textContent=node.textContent.replace('Send a message to start','DeepSeek R1 8B Agent');});const observer=new MutationObserver(()=>{const walker=document.createTreeWalker(document.body,NodeFilter.SHOW_TEXT,null,false);const nodesToReplace=[];let node;while(node=walker.nextNode()){if(node.textContent.includes('Send a message to start')){nodesToReplace.push(node);}}nodesToReplace.forEach(node=>{node.textContent=node.textContent.replace('Send a message to start','DeepSeek R1 8B Agent');});});observer.observe(document.body,{childList:true,subtree:true,characterData:true});alert('✅ Nom agent corrigé en DeepSeek R1 8B Agent');})();">🎯 Corriger Nom Agent</a>
    </div>
    
    <h3>Instructions :</h3>
    <ol>
        <li>Glissez le lien ci-dessus dans votre barre de favoris</li>
        <li>Ouvrez votre interface llama.cpp sur http://localhost:8080/interface_8080_reelle.html</li>
        <li>Cliquez sur le favori "🎯 Corriger Nom Agent"</li>
        <li>Le texte "Send a message to start" sera remplacé par "DeepSeek R1 8B Agent"</li>
    </ol>
</body>
</html>
