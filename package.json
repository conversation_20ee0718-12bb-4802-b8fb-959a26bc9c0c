{"name": "jarvis-r1-8b-complete", "version": "2.0.0", "description": "JARVIS R1 8B COMPLET - Interface Claude + MCP + Mémoire Thermique - Créé avec amour par <PERSON> pour Jean-Luc", "main": "jarvis_r1_complete.js", "scripts": {"start": "electron jarvis_r1_complete.js", "dev": "NODE_ENV=development electron jarvis_r1_complete.js", "build": "electron-builder", "test": "echo \"JARVIS R1 8B Test Suite\" && exit 0"}, "keywords": ["jarvis", "r1", "8b", "deepseek", "ai", "assistant", "electron", "claude", "thermal-memory", "mcp", "model-context-protocol", "internet-access", "jean-luc"], "author": {"name": "Claude & Jean-Luc <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.21.2", "http-proxy": "^1.18.1", "http-proxy-middleware": "^3.0.5", "node-fetch": "^2.7.0"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "build": {"appId": "com.jeanluc.jarvis-r1-8b", "productName": "JARVIS R1 8B", "directories": {"output": "dist"}, "files": ["jarvis_r1_complete.js", "interface_8080_reelle.html", "serveur_mcp_reel.js", "serveur_interface_mcp_complete.js", "thermal_memory_persistent.json", "conversations_permanentes.json", "models/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns"}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "repository": {"type": "git", "url": "https://github.com/jean-luc-passave/jarvis-r1-8b.git"}, "bugs": {"url": "https://github.com/jean-luc-passave/jarvis-r1-8b/issues"}, "homepage": "https://github.com/jean-luc-passave/jarvis-r1-8b#readme"}