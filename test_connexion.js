const fs = require('fs');

console.log('🔌 TEST CONNEXION DIRECTE AGENTS');

// Canal simple
const canal = './canal.json';

// Agent 2
class Agent2 {
    constructor() {
        this.nom = "Agent 2";
        console.log('🤖 Agent 2 initialisé');
    }
    
    recevoirMot(mot) {
        console.log(`📥 Agent 2: Reçoit "${mot}"`);
        
        // Envoyer à Agent 1
        const message = `Agent 1, analyse: "${mot}"`;
        
        const data = {
            de: 'AGENT_2',
            vers: 'AGENT_1', 
            message: message,
            timestamp: Date.now()
        };
        
        fs.writeFileSync(canal, JSON.stringify(data));
        console.log(`📤 Agent 2 → Agent 1: "${message}"`);
    }
}

// Agent 1  
class Agent1 {
    constructor() {
        this.nom = "Agent 1";
        this.coefficient = 1.0;
        console.log('🤖 Agent 1 initialisé');
        this.surveiller();
    }
    
    surveiller() {
        setInterval(() => {
            if (fs.existsSync(canal)) {
                const data = JSON.parse(fs.readFileSync(canal, 'utf8'));
                
                if (data.vers === 'AGENT_1' && !data.traite) {
                    this.traiter(data.message);
                    data.traite = true;
                    fs.writeFileSync(canal, JSON.stringify(data));
                }
            }
        }, 1000);
    }
    
    traiter(message) {
        console.log(`📥 Agent 1: Reçoit "${message}"`);
        this.coefficient += 0.01;
        
        const reponse = `Agent 1 répond: Analysé. Coefficient: ${this.coefficient.toFixed(3)}`;
        console.log(`🧠 Agent 1: ${reponse}`);
    }
}

// Créer agents
const agent2 = new Agent2();
const agent1 = new Agent1();

console.log('\n✅ AGENTS CONNECTÉS !');
console.log('🎯 Tapez un mot pour tester:');

process.stdin.on('data', (data) => {
    const mot = data.toString().trim();
    if (mot === 'exit') process.exit(0);
    if (mot.length > 0) {
        console.log(`\n👤 UTILISATEUR: "${mot}"`);
        agent2.recevoirMot(mot);
    }
});

