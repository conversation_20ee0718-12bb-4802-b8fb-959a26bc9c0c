#!/bin/bash

# 🧠 JARVIS - DÉMARRAGE SYSTÈME COMPLET
# Script de démarrage automatique avec validation

echo "🚀 DÉMARRAGE JARVIS SYSTÈME COMPLET"
echo "=================================="

# 🔍 VÉRIFICATIONS PRÉALABLES
echo "🔍 Vérification des prérequis..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js non installé !"
    echo "💡 Installez Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js: $(node --version)"

# Vérifier llama.cpp
if ! pgrep -f "llama" > /dev/null; then
    echo "⚠️ llama.cpp ne semble pas actif"
    echo "💡 Démarrez llama.cpp avant ce script"
    echo "💡 Exemple: ./llama-server --port 8082"
fi

# 🧹 NETTOYAGE
echo "🧹 Nettoyage des processus précédents..."
pkill -f "proxy_jarvis_professionnel.js" 2>/dev/null || true
sleep 2

# 🚀 DÉMARRAGE PROXY JARVIS
echo "🚀 Démarrage JARVIS Proxy Professionnel..."
echo "📍 Port Interface: 8080"
echo "📍 Port llama.cpp: 8082"
echo "🌐 URL: http://127.0.0.1:8080"

# Démarrage en arrière-plan avec logs
nohup node proxy_jarvis_professionnel.js > jarvis_logs.txt 2>&1 &
JARVIS_PID=$!

echo "✅ JARVIS démarré (PID: $JARVIS_PID)"

# 🔍 VALIDATION DÉMARRAGE
echo "🔍 Validation du démarrage..."
sleep 3

if ps -p $JARVIS_PID > /dev/null; then
    echo "✅ JARVIS fonctionne correctement"
    
    # Test connexion
    if curl -s http://127.0.0.1:8080 > /dev/null; then
        echo "✅ Interface accessible"
        echo ""
        echo "🎉 JARVIS SYSTÈME COMPLET OPÉRATIONNEL !"
        echo "🌐 Ouvrez: http://127.0.0.1:8080"
        echo "📋 Logs: tail -f jarvis_logs.txt"
        echo "🛑 Arrêt: kill $JARVIS_PID"
        
        # Ouvrir automatiquement le navigateur (optionnel)
        if command -v open &> /dev/null; then
            echo "🌐 Ouverture automatique du navigateur..."
            sleep 2
            open http://127.0.0.1:8080
        fi
        
    else
        echo "❌ Interface non accessible"
        echo "📋 Vérifiez les logs: cat jarvis_logs.txt"
    fi
else
    echo "❌ JARVIS n'a pas démarré correctement"
    echo "📋 Vérifiez les logs: cat jarvis_logs.txt"
    exit 1
fi

echo ""
echo "📊 STATUT SYSTÈME:"
echo "=================="
echo "🧠 JARVIS PID: $JARVIS_PID"
echo "📍 Interface: http://127.0.0.1:8080"
echo "📋 Logs: jarvis_logs.txt"
echo "🛑 Arrêt: kill $JARVIS_PID"
echo ""
echo "🎯 FONCTIONNALITÉS DISPONIBLES:"
echo "- 🎤 Reconnaissance vocale"
echo "- 🔊 Synthèse vocale"
echo "- 📹 Caméra cognitive"
echo "- 🧠 Mémoire thermique"
echo "- 🚀 Évolution automatique"
echo "- ⚙️ Settings préservés"
echo "- 🎨 Thèmes préservés"
echo ""
echo "✨ Interface JARVIS prête à l'utilisation !"
