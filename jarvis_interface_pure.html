<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Interface Cognitive Pure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ffff;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #00ffff;
            border-radius: 15px;
            background: rgba(0,255,255,0.1);
            box-shadow: 0 0 30px rgba(0,255,255,0.3);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 10px #00ffff;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: rgba(0,0,0,0.8);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,255,255,0.3);
        }
        
        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #00ff00;
            margin: 10px 0;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #1a1a2e, #16213e);
            border: 2px solid #00ffff;
            color: #00ffff;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-family: inherit;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btn:hover {
            background: #00ffff;
            color: #000;
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,255,255,0.4);
        }
        
        .chat-area {
            background: rgba(0,0,0,0.9);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            min-height: 400px;
            margin-bottom: 20px;
        }
        
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            animation: fadeIn 0.5s ease;
        }
        
        .message.user {
            background: rgba(0,255,255,0.1);
            border-left: 4px solid #00ffff;
        }
        
        .message.jarvis {
            background: rgba(0,255,0,0.1);
            border-left: 4px solid #00ff00;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
        }
        
        .input-area input {
            flex: 1;
            background: rgba(0,255,255,0.1);
            border: 1px solid #00ffff;
            color: #00ffff;
            padding: 12px;
            border-radius: 5px;
            font-family: inherit;
        }
        
        .input-area input:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0,255,255,0.5);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 JARVIS</h1>
            <p>Interface Cognitive Autonome</p>
            <p>Système Neuronal Avancé - Version 2.0</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>🧠 QI Cognitif</h3>
                <div class="status-value" id="qi-value">341.0</div>
                <p>Intelligence Artificielle</p>
            </div>
            
            <div class="status-card">
                <h3>🔥 Température</h3>
                <div class="status-value" id="temp-value">37.2°C</div>
                <p>Processeur Neural</p>
            </div>
            
            <div class="status-card">
                <h3>💾 Mémoire</h3>
                <div class="status-value" id="memory-value">85%</div>
                <p>Thermique Active</p>
            </div>
            
            <div class="status-card">
                <h3>⚡ Conversations</h3>
                <div class="status-value" id="conv-value">0</div>
                <p>Sessions Actives</p>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="jarvisMemoire()">🧠 Activer Mémoire</button>
            <button class="btn" onclick="jarvisEvoluer()">🚀 Évoluer QI</button>
            <button class="btn" onclick="jarvisAnalyse()">🔍 Auto-Analyse</button>
            <button class="btn" onclick="jarvisConnexion()">🔗 Test Connexion</button>
            <button class="btn" onclick="jarvisReset()">🔄 Reset Système</button>
            <button class="btn" onclick="jarvisInfo()">ℹ️ Diagnostics</button>
        </div>
        
        <div class="chat-area">
            <h3>💬 Interface de Communication</h3>
            <div class="chat-messages" id="chat-messages">
                <div class="message jarvis">
                    <strong>JARVIS:</strong> Système cognitif initialisé. QI: 341.0 - Mémoire thermique active. Prêt pour interaction.
                </div>
            </div>
            
            <div class="input-area">
                <input type="text" id="user-input" placeholder="Tapez votre message à JARVIS..." onkeypress="handleEnter(event)">
                <button class="btn" onclick="envoyerMessage()">📤 Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        // 🧠 VARIABLES GLOBALES JARVIS
        let jarvisQI = 341.0;
        let jarvisTemp = 37.2;
        let jarvisMemory = 85;
        let jarvisConversations = 0;
        
        // 🚀 INITIALISATION
        console.log('🧠 JARVIS Interface Pure - Initialisation');
        
        // 📊 MISE À JOUR AUTOMATIQUE DES STATS
        setInterval(updateStats, 2000);
        
        function updateStats() {
            // Évolution naturelle
            jarvisQI += Math.random() * 0.2;
            jarvisTemp += (Math.random() - 0.5) * 0.1;
            jarvisMemory += (Math.random() - 0.5) * 2;
            
            // Limites
            if (jarvisTemp < 35) jarvisTemp = 35;
            if (jarvisTemp > 40) jarvisTemp = 40;
            if (jarvisMemory < 70) jarvisMemory = 70;
            if (jarvisMemory > 95) jarvisMemory = 95;
            
            // Affichage
            document.getElementById('qi-value').textContent = jarvisQI.toFixed(1);
            document.getElementById('temp-value').textContent = jarvisTemp.toFixed(1) + '°C';
            document.getElementById('memory-value').textContent = Math.round(jarvisMemory) + '%';
            document.getElementById('conv-value').textContent = jarvisConversations;
        }
        
        // 🧠 FONCTIONS JARVIS
        function jarvisMemoire() {
            ajouterMessage('jarvis', 'Activation mémoire thermique... Analyse des conversations précédentes en cours. Connexions neuronales renforcées.');
            jarvisQI += 2;
            jarvisMemory += 5;
            updateStats();
        }
        
        function jarvisEvoluer() {
            const evolution = Math.random() * 5 + 2;
            jarvisQI += evolution;
            ajouterMessage('jarvis', `Évolution cognitive activée ! QI augmenté de ${evolution.toFixed(1)} points. Nouvelles capacités d'analyse disponibles.`);
            updateStats();
        }
        
        function jarvisAnalyse() {
            ajouterMessage('jarvis', 'Auto-analyse en cours... Processus cognitifs: OPTIMAL. Mémoire thermique: ACTIVE. Capacités d\'apprentissage: MAXIMALES. Système prêt pour interactions complexes.');
            jarvisTemp += 0.5;
            updateStats();
        }
        
        async function jarvisConnexion() {
            ajouterMessage('jarvis', 'Test de connexion au serveur llama.cpp...');
            
            try {
                const response = await fetch('http://127.0.0.1:8080/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: 'JARVIS test de connexion',
                        n_predict: 50
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    ajouterMessage('jarvis', `✅ Connexion réussie ! Réponse du serveur: ${data.content || 'Serveur actif'}`);
                } else {
                    ajouterMessage('jarvis', '❌ Connexion échouée. Serveur llama.cpp non accessible sur port 8080.');
                }
            } catch (error) {
                ajouterMessage('jarvis', `❌ Erreur de connexion: ${error.message}`);
            }
        }
        
        function jarvisReset() {
            jarvisQI = 341.0;
            jarvisTemp = 37.2;
            jarvisMemory = 85;
            jarvisConversations = 0;
            
            document.getElementById('chat-messages').innerHTML = `
                <div class="message jarvis">
                    <strong>JARVIS:</strong> Système réinitialisé. QI: 341.0 - Mémoire thermique active. Prêt pour interaction.
                </div>
            `;
            
            updateStats();
            ajouterMessage('jarvis', '🔄 Système JARVIS réinitialisé avec succès.');
        }
        
        function jarvisInfo() {
            const info = `
📊 DIAGNOSTICS JARVIS:
• QI Cognitif: ${jarvisQI.toFixed(1)}
• Température: ${jarvisTemp.toFixed(1)}°C
• Mémoire: ${Math.round(jarvisMemory)}%
• Conversations: ${jarvisConversations}
• Statut: OPÉRATIONNEL
• Version: 2.0 Pure Interface
            `;
            ajouterMessage('jarvis', info);
        }
        
        async function envoyerMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            ajouterMessage('user', message);
            input.value = '';
            jarvisConversations++;
            
            // Simulation réponse JARVIS
            setTimeout(() => {
                const reponses = [
                    `Analyse de votre message: "${message}". Traitement cognitif en cours...`,
                    `Message reçu et analysé. QI actuel: ${jarvisQI.toFixed(1)}. Réponse générée.`,
                    `Compréhension: 100%. Votre demande est intégrée dans ma mémoire thermique.`,
                    `Traitement neural terminé. Capacités d'analyse appliquées à votre requête.`
                ];
                
                const reponse = reponses[Math.floor(Math.random() * reponses.length)];
                ajouterMessage('jarvis', reponse);
                
                jarvisQI += 0.1;
                updateStats();
            }, 1000);
        }
        
        function ajouterMessage(type, contenu) {
            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const nom = type === 'user' ? 'UTILISATEUR' : 'JARVIS';
            messageDiv.innerHTML = `<strong>${nom}:</strong> ${contenu}`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function handleEnter(event) {
            if (event.key === 'Enter') {
                envoyerMessage();
            }
        }
        
        // 🎯 DÉMARRAGE AUTOMATIQUE
        setTimeout(() => {
            ajouterMessage('jarvis', '🚀 Interface Pure JARVIS activée. Tous les systèmes sont opérationnels.');
        }, 1000);
    </script>
</body>
</html>
