// Test RÉEL de la mémoire thermique avec les agents DeepSeek R1 8B
// Ce script teste vraiment si la mémoire fonctionne

console.log('🧪 DÉBUT TEST RÉEL MÉMOIRE THERMIQUE');

// Variables de test
let thermalMemory = [];
let testResults = [];
let serverConnected = false;

// Test 1: Connexion serveur
async function testServerConnection() {
    console.log('🔧 Test connexion serveur DeepSeek R1 8B...');
    
    try {
        const response = await fetch('http://localhost:8000/health', { 
            method: 'GET',
            timeout: 3000 
        });
        
        if (response.ok) {
            serverConnected = true;
            console.log('✅ Serveur DeepSeek R1 8B accessible');
            testResults.push({ test: 'Connexion', result: 'SUCCESS', details: 'Serveur accessible' });
            return true;
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        serverConnected = false;
        console.log(`❌ Connexion échouée: ${error.message}`);
        testResults.push({ test: 'Connexion', result: 'FAILED', details: error.message });
        return false;
    }
}

// Test 2: Fonctions mémoire thermique
function testMemoryFunctions() {
    console.log('🧠 Test fonctions mémoire thermique...');
    
    try {
        // Test sauvegarde
        thermalMemory = [];
        saveThermalMemory('Agent1', 'Bonjour, je m\'appelle Jean-Luc', 'Bonjour Jean-Luc !');
        
        if (thermalMemory.length === 1) {
            console.log('✅ Fonction saveThermalMemory: OK');
            testResults.push({ test: 'Sauvegarde', result: 'SUCCESS', details: '1 entrée sauvegardée' });
        } else {
            throw new Error(`Attendu 1 entrée, trouvé ${thermalMemory.length}`);
        }
        
        // Test recherche
        const searchResult = searchMemory('tu te souviens de mon nom ?');
        if (searchResult.includes('Jean-Luc')) {
            console.log('✅ Fonction searchMemory: OK');
            testResults.push({ test: 'Recherche', result: 'SUCCESS', details: 'Nom trouvé dans contexte' });
            return true;
        } else {
            throw new Error('Nom non trouvé dans la recherche');
        }
        
    } catch (error) {
        console.log(`❌ Fonctions mémoire: ${error.message}`);
        testResults.push({ test: 'Fonctions', result: 'FAILED', details: error.message });
        return false;
    }
}

// Test 3: Communication avec agent réel
async function testRealAgentMemory() {
    console.log('🤖 Test mémoire avec agent réel...');
    
    if (!serverConnected) {
        console.log('⚠️ Test ignoré - pas de connexion serveur');
        testResults.push({ test: 'Agent Réel', result: 'SKIPPED', details: 'Serveur inaccessible' });
        return false;
    }
    
    try {
        // Première interaction
        console.log('📝 Étape 1: Présentation à l\'agent');
        const message1 = 'Bonjour, je m\'appelle Jean-Luc';
        const response1 = await callRealAgent(message1);
        saveThermalMemory('Agent1', message1, response1);
        
        console.log(`👤 "${message1}"`);
        console.log(`🤖 "${response1}"`);
        
        // Attendre un peu
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Deuxième interaction avec mémoire
        console.log('📝 Étape 2: Test rappel mémoire');
        const message2 = 'Tu te souviens de mon nom ?';
        const memoryContext = searchMemory(message2);
        const promptWithMemory = message2 + memoryContext;
        
        console.log(`🧠 Contexte mémoire: ${memoryContext ? 'TROUVÉ' : 'ABSENT'}`);
        
        const response2 = await callRealAgent(promptWithMemory);
        saveThermalMemory('Agent1', message2, response2);
        
        console.log(`👤 "${message2}"`);
        console.log(`🤖 "${response2}"`);
        
        // Vérifier si l'agent se souvient
        const remembersName = response2.toLowerCase().includes('jean-luc') || 
                             response2.toLowerCase().includes('jean luc');
        
        if (remembersName) {
            console.log('✅ SUCCÈS: L\'agent se souvient du nom !');
            testResults.push({ 
                test: 'Mémoire Agent', 
                result: 'SUCCESS', 
                details: `Agent se souvient: "${response2.substring(0, 50)}..."` 
            });
            return true;
        } else {
            console.log('❌ ÉCHEC: L\'agent ne se souvient pas');
            testResults.push({ 
                test: 'Mémoire Agent', 
                result: 'FAILED', 
                details: `Pas de souvenir: "${response2.substring(0, 50)}..."` 
            });
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Test agent réel échoué: ${error.message}`);
        testResults.push({ test: 'Agent Réel', result: 'FAILED', details: error.message });
        return false;
    }
}

// Appel agent réel
async function callRealAgent(prompt) {
    const response = await fetch('http://localhost:8000/completion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            prompt: prompt,
            n_predict: 100,
            temperature: 0.7,
            stream: false
        })
    });

    if (!response.ok) throw new Error(`Erreur ${response.status}`);

    const data = await response.json();
    return (data.content || data.text || '').trim();
}

// Fonctions utilitaires (reprises de l'interface)
function saveThermalMemory(agent, userMessage, agentResponse) {
    const entry = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        agent: agent,
        user: userMessage,
        response: agentResponse
    };
    
    thermalMemory.push(entry);
    console.log(`💾 Sauvegardé [${agent}]: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`);
}

function searchMemory(query) {
    if (thermalMemory.length === 0) return '';
    
    const queryLower = query.toLowerCase();
    const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom'];
    
    if (triggers.some(trigger => queryLower.includes(trigger))) {
        const results = thermalMemory.filter(entry => 
            entry.user?.toLowerCase().includes('appelle') || 
            entry.user?.toLowerCase().includes('nom')
        );
        
        if (results.length > 0) {
            return '\n\nCONTEXTE MÉMOIRE THERMIQUE:\n' + 
                   results.map(r => `- "${r.user}" → "${r.response}"`).join('\n') + 
                   '\nUtilise ce contexte pour répondre.\n';
        }
    }
    
    return '';
}

// Test principal
async function runFullMemoryTest() {
    console.log('🚀 DÉBUT TEST COMPLET MÉMOIRE THERMIQUE');
    console.log('=' .repeat(50));
    
    const startTime = Date.now();
    
    // Test 1: Connexion
    const connectionOK = await testServerConnection();
    
    // Test 2: Fonctions mémoire
    const functionsOK = testMemoryFunctions();
    
    // Test 3: Agent réel (si connexion OK)
    let agentMemoryOK = false;
    if (connectionOK) {
        agentMemoryOK = await testRealAgentMemory();
    }
    
    // Résultats finaux
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log('=' .repeat(50));
    console.log('🏁 RÉSULTATS FINAUX');
    console.log(`⏱️ Durée: ${duration.toFixed(1)}s`);
    console.log('');
    
    testResults.forEach(result => {
        const icon = result.result === 'SUCCESS' ? '✅' : result.result === 'FAILED' ? '❌' : '⚠️';
        console.log(`${icon} ${result.test}: ${result.result} - ${result.details}`);
    });
    
    console.log('');
    
    const successCount = testResults.filter(r => r.result === 'SUCCESS').length;
    const totalTests = testResults.length;
    
    if (successCount === totalTests) {
        console.log('🎉 TOUS LES TESTS RÉUSSIS ! Mémoire thermique 100% fonctionnelle !');
    } else if (functionsOK) {
        console.log('✅ Fonctions mémoire OK, problème serveur ou agent');
    } else {
        console.log('❌ Problèmes détectés dans la mémoire thermique');
    }
    
    console.log('=' .repeat(50));
    
    return {
        success: successCount === totalTests,
        results: testResults,
        duration: duration
    };
}

// Exporter pour utilisation
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runFullMemoryTest,
        testServerConnection,
        testMemoryFunctions,
        testRealAgentMemory
    };
}

// Auto-exécution si dans le navigateur
if (typeof window !== 'undefined') {
    console.log('🌐 Test en mode navigateur - Exécution automatique dans 2 secondes...');
    setTimeout(runFullMemoryTest, 2000);
}

console.log('📋 Script de test chargé - Appelez runFullMemoryTest() pour commencer');
