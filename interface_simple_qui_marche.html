<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 JARVIS - Interface Simple</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
            color: #00ffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0,0,0,0.8);
            border: 2px solid #00ffff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 0 30px rgba(0,255,255,0.3);
        }
        
        h1 {
            text-align: center;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
            margin-bottom: 30px;
        }
        
        .chat-area {
            background: rgba(0,255,255,0.05);
            border: 1px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 300px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            border-left: 3px solid #00ffff;
            background: rgba(0,255,255,0.1);
        }
        
        .message-time {
            font-size: 10px;
            opacity: 0.7;
            margin-bottom: 5px;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        #messageInput {
            flex: 1;
            padding: 15px;
            background: rgba(0,255,255,0.1);
            border: 1px solid #00ffff;
            border-radius: 8px;
            color: #00ffff;
            font-family: inherit;
            font-size: 14px;
        }
        
        #messageInput:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0,255,255,0.5);
        }
        
        .btn {
            padding: 15px 25px;
            background: rgba(0,255,0,0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: rgba(0,255,0,0.3);
            box-shadow: 0 0 15px rgba(0,255,0,0.5);
        }
        
        .btn-mcp {
            background: rgba(255,165,0,0.2);
            border-color: #ffa500;
            color: #ffa500;
        }
        
        .btn-mcp:hover {
            background: rgba(255,165,0,0.3);
            box-shadow: 0 0 15px rgba(255,165,0,0.5);
        }
        
        .status {
            text-align: center;
            padding: 10px;
            background: rgba(0,255,0,0.1);
            border: 1px solid #00ff00;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .loading {
            color: #ffff00;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 JARVIS - Interface Simple</h1>
        
        <div class="status" id="status">
            ✅ Prêt à communiquer avec JARVIS
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message">
                <div class="message-time">Système - Démarrage</div>
                <div>🧠 JARVIS initialisé avec succès !<br>
                💬 Tapez votre message ci-dessous<br>
                🔌 MCP et mémoire thermique disponibles</div>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Tapez votre message à JARVIS..." />
            <button class="btn" onclick="envoyerMessage()">🚀 Envoyer</button>
            <button class="btn btn-mcp" onclick="testerMCP()">🔌 Test MCP</button>
            <button class="btn" onclick="simulerAgent2()" style="background: rgba(255,165,0,0.2); border-color: #ffa500; color: #ffa500;">🤖 Agent 2</button>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn" onclick="effacerChat()">🗑️ Effacer</button>
            <button class="btn btn-mcp" onclick="statusJarvis()">📊 Status</button>
        </div>
    </div>

    <script>
        // Variables globales
        let messageCount = 0;
        
        // Fonction pour ajouter un message au chat
        function ajouterMessage(contenu, type = 'user') {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            
            const time = new Date().toLocaleTimeString();
            const icon = type === 'user' ? '👤' : '🤖';
            const name = type === 'user' ? 'Vous' : 'JARVIS';
            
            messageDiv.innerHTML = `
                <div class="message-time">${icon} ${name} - ${time}</div>
                <div>${contenu}</div>
            `;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
            messageCount++;
        }
        
        // Fonction pour envoyer un message
        async function envoyerMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                alert('❌ Veuillez saisir un message');
                return;
            }
            
            // Afficher le message utilisateur
            ajouterMessage(message, 'user');
            input.value = '';
            
            // Afficher le statut d'envoi
            const status = document.getElementById('status');
            status.innerHTML = '<span class="loading">📡 Envoi vers JARVIS...</span>';
            
            try {
                // Envoyer directement à votre agent DeepSeek R1 8B
                const response = await fetch('http://127.0.0.1:8000/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        messages: [
                            {
                                role: 'user',
                                content: `Tu es JARVIS, assistant IA de Jean-Luc. Réponds en français de manière concise et utile. Message: ${message}`
                            }
                        ],
                        max_tokens: 300,
                        temperature: 0.7
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const reponse = data.choices[0].message.content;
                    
                    // Afficher la réponse
                    ajouterMessage(reponse, 'jarvis');
                    status.innerHTML = '✅ Message envoyé et réponse reçue';
                    
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
                
            } catch (error) {
                console.error('Erreur:', error);
                ajouterMessage(`❌ Erreur de connexion: ${error.message}`, 'jarvis');
                status.innerHTML = '❌ Erreur de connexion à JARVIS';
            }
        }
        
        // Fonction pour tester MCP
        async function testerMCP() {
            const status = document.getElementById('status');
            status.innerHTML = '<span class="loading">🔌 Test MCP...</span>';
            
            try {
                const response = await fetch('http://127.0.0.1:8086/mcp/status');
                const data = await response.json();
                
                ajouterMessage(`🔌 MCP Status: ${data.mcp_server}<br>📡 APIs: ${data.apis_disponibles.join(', ')}<br>🌐 Internet: ${data.internet_access ? 'Connecté' : 'Déconnecté'}`, 'jarvis');
                status.innerHTML = '✅ MCP testé avec succès';
                
            } catch (error) {
                ajouterMessage(`❌ Erreur MCP: ${error.message}`, 'jarvis');
                status.innerHTML = '❌ Erreur test MCP';
            }
        }
        
        // Fonction pour effacer le chat
        function effacerChat() {
            const chatArea = document.getElementById('chatArea');
            chatArea.innerHTML = `
                <div class="message">
                    <div class="message-time">Système - ${new Date().toLocaleTimeString()}</div>
                    <div>🧹 Chat effacé - Prêt pour une nouvelle conversation</div>
                </div>
            `;
            messageCount = 0;
        }
        
        // Fonction pour afficher le status
        function statusJarvis() {
            ajouterMessage(`📊 STATUS JARVIS:<br>
            💬 Messages échangés: ${messageCount}<br>
            🔌 MCP: Disponible sur port 8086<br>
            🤖 Agent: llama.cpp sur port 8080<br>
            🧠 Interface: Simple et fonctionnelle<br>
            ⏰ Heure: ${new Date().toLocaleString()}`, 'jarvis');
        }
        
        // Gestion de la touche Entrée
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                envoyerMessage();
            }
        });
        
        // Test de connexion au démarrage
        window.onload = function() {
            setTimeout(async () => {
                try {
                    const response = await fetch('http://127.0.0.1:8000/health');
                    if (response.ok) {
                        document.getElementById('status').innerHTML = '✅ JARVIS connecté et prêt';
                    } else {
                        document.getElementById('status').innerHTML = '⚠️ JARVIS partiellement connecté';
                    }
                } catch (error) {
                    document.getElementById('status').innerHTML = '❌ JARVIS non connecté';
                }
            }, 1000);
        };

        // 🤖 SYSTÈME BULLES AGENT 2
        let bubbleCounter = 0;
        let activeBubbles = [];

        function simulerAgent2() {
            const messages = [
                "Test manuel Agent 2",
                "Vérification systèmes thermiques",
                "Analyse patterns conversation",
                "Optimisation connexions neuronales"
            ];
            const randomMsg = messages[Math.floor(Math.random() * messages.length)];
            creerBulleAgent2(randomMsg);
        }

        function creerBulleAgent2(messageDeClencheur) {
            bubbleCounter++;
            const bubbleId = `agent2-bubble-${bubbleCounter}`;

            const bubble = document.createElement('div');
            bubble.id = bubbleId;
            bubble.style.cssText = `
                position: fixed;
                top: ${100 + (bubbleCounter * 60)}px;
                right: ${50 + (bubbleCounter * 30)}px;
                background: rgba(0,0,0,0.95);
                border: 2px solid #ffa500;
                border-radius: 15px;
                padding: 20px;
                min-width: 320px;
                max-width: 500px;
                box-shadow: 0 0 25px rgba(255,165,0,0.6);
                z-index: 10000;
                color: white;
                font-family: 'Courier New', monospace;
                animation: bulleApparition 0.5s ease-out;
                resize: both;
                overflow: auto;
            `;

            const messagesAgent2 = [
                `🤖 Agent 2 analyse: "${messageDeClencheur.substring(0, 30)}..."`,
                `🧠 Réflexion thermique activée`,
                `🔥 Température cognitive: ${(37 + Math.random() * 2).toFixed(1)}°C`,
                `💭 Connexions neuronales renforcées`,
                `⚡ Stimulation cognitive détectée`,
                `🌡️ Mémoire thermique mise à jour`
            ];

            const messageAgent2 = messagesAgent2[Math.floor(Math.random() * messagesAgent2.length)];

            bubble.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; border-bottom: 1px solid #ffa500; padding-bottom: 10px;">
                    <div style="color: #ffa500; font-weight: bold;">🤖 Agent 2 - Contrôleur Thermique</div>
                    <div>
                        <button onclick="minimiserBulle('${bubbleId}')" style="background: rgba(255,165,0,0.2); border: 1px solid #ffa500; color: #ffa500; padding: 3px 6px; margin: 0 2px; border-radius: 3px; cursor: pointer; font-size: 11px;">➖</button>
                        <button onclick="maximiserBulle('${bubbleId}')" style="background: rgba(255,165,0,0.2); border: 1px solid #ffa500; color: #ffa500; padding: 3px 6px; margin: 0 2px; border-radius: 3px; cursor: pointer; font-size: 11px;">⬜</button>
                        <button onclick="fermerBulle('${bubbleId}')" style="background: rgba(255,165,0,0.2); border: 1px solid #ffa500; color: #ffa500; padding: 3px 6px; margin: 0 2px; border-radius: 3px; cursor: pointer; font-size: 11px;">❌</button>
                    </div>
                </div>
                <div>
                    <p><strong>💬 Message:</strong> ${messageAgent2}</p>
                    <p><strong>🌡️ Température:</strong> ${(37 + Math.random() * 2).toFixed(1)}°C</p>
                    <p><strong>⏰ Timestamp:</strong> ${new Date().toLocaleTimeString()}</p>
                    <p><strong>🎯 Déclencheur:</strong> "${messageDeClencheur.substring(0, 40)}..."</p>
                    <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ffa500; font-size: 12px; color: #ccc;">
                        🔄 Agent 2 surveille en continu et ajuste la température cognitive selon les besoins.
                    </div>
                </div>
            `;

            // Ajouter l'animation CSS si pas déjà présente
            if (!document.getElementById('agent2-animations')) {
                const style = document.createElement('style');
                style.id = 'agent2-animations';
                style.textContent = `
                    @keyframes bulleApparition {
                        from { opacity: 0; transform: scale(0.8) translateY(20px); }
                        to { opacity: 1; transform: scale(1) translateY(0); }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(bubble);
            activeBubbles.push(bubbleId);

            // Auto-fermeture après 30 secondes
            setTimeout(() => {
                if (document.getElementById(bubbleId)) {
                    fermerBulle(bubbleId);
                }
            }, 30000);

            console.log(`💬 Bulle Agent 2 créée: ${bubbleId}`);
        }

        function minimiserBulle(bubbleId) {
            const bubble = document.getElementById(bubbleId);
            if (bubble) {
                if (bubble.style.height === '60px') {
                    bubble.style.height = 'auto';
                    bubble.style.width = '320px';
                } else {
                    bubble.style.height = '60px';
                    bubble.style.width = '200px';
                    bubble.style.overflow = 'hidden';
                }
            }
        }

        function maximiserBulle(bubbleId) {
            const bubble = document.getElementById(bubbleId);
            if (bubble) {
                if (bubble.style.width === '80vw') {
                    bubble.style.width = '320px';
                    bubble.style.height = 'auto';
                    bubble.style.top = `${100 + (parseInt(bubbleId.split('-')[2]) * 60)}px`;
                    bubble.style.right = `${50 + (parseInt(bubbleId.split('-')[2]) * 30)}px`;
                } else {
                    bubble.style.width = '80vw';
                    bubble.style.height = '80vh';
                    bubble.style.top = '10vh';
                    bubble.style.left = '10vw';
                    bubble.style.right = 'auto';
                }
            }
        }

        function fermerBulle(bubbleId) {
            const bubble = document.getElementById(bubbleId);
            if (bubble) {
                bubble.style.animation = 'bulleApparition 0.3s ease-in reverse';
                setTimeout(() => {
                    bubble.remove();
                    activeBubbles = activeBubbles.filter(id => id !== bubbleId);
                }, 300);
            }
        }

        // Déclencher Agent 2 automatiquement lors des messages
        const originalEnvoyerMessage = envoyerMessage;
        envoyerMessage = async function() {
            await originalEnvoyerMessage();

            // Chance que Agent 2 intervienne
            if (Math.random() > 0.6) {
                setTimeout(() => {
                    const input = document.getElementById('messageInput');
                    const lastMessage = input.getAttribute('data-last-message') || "Message utilisateur";
                    creerBulleAgent2(lastMessage);
                }, 3000);
            }
        };

        // Sauvegarder le dernier message pour Agent 2
        document.getElementById('messageInput').addEventListener('input', function() {
            this.setAttribute('data-last-message', this.value);
        });
    </script>
</body>
</html>
