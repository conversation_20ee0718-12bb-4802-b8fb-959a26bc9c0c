// 🔌 SERVEUR JARVIS AVEC SUPPORT MCP
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔌 SERVEUR JARVIS MCP - DÉMARRAGE');

const server = http.createServer((req, res) => {
    console.log(`📡 Requête: ${req.method} ${req.url}`);
    
    // 🏠 SERVIR INTERFACE PRINCIPALE
    if (req.method === 'GET' && req.url === '/') {
        try {
            const html = fs.readFileSync('./interface_complete.html', 'utf8');
            
            res.writeHead(200, {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache',
                'Access-Control-Allow-Origin': '*'
            });
            res.end(html);
            
            console.log('✅ Interface JARVIS MCP servie');
            
        } catch (error) {
            console.error('❌ Erreur lecture interface:', error.message);
            res.writeHead(500);
            res.end('Erreur serveur');
        }
        
    } 
    // 🔌 API MCP - RECHERCHE WEB
    else if (req.method === 'POST' && req.url === '/mcp/search') {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                console.log('🌐 MCP Recherche:', data.query);
                
                // Simulation recherche web
                const resultats = {
                    query: data.query,
                    results: [
                        {
                            title: `Résultat 1 pour "${data.query}"`,
                            url: 'https://example.com/1',
                            snippet: 'Information pertinente trouvée sur le web...'
                        },
                        {
                            title: `Résultat 2 pour "${data.query}"`,
                            url: 'https://example.com/2',
                            snippet: 'Données actualisées 2025...'
                        }
                    ],
                    timestamp: new Date().toISOString(),
                    source: 'MCP Web Search'
                };
                
                res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(JSON.stringify(resultats));
                
            } catch (error) {
                console.error('❌ Erreur MCP Search:', error.message);
                res.writeHead(400);
                res.end(JSON.stringify({ error: 'Données invalides' }));
            }
        });
    }
    
    // 📰 API MCP - ACTUALITÉS
    else if (req.method === 'GET' && req.url === '/mcp/news') {
        const actualites = {
            news: [
                {
                    title: 'Avancées IA Janvier 2025',
                    content: 'Nouvelles découvertes en intelligence artificielle...',
                    date: '2025-01-17',
                    source: 'Tech News'
                },
                {
                    title: 'Développements Technologiques',
                    content: 'Innovations récentes dans le domaine tech...',
                    date: '2025-01-16',
                    source: 'Innovation Daily'
                }
            ],
            timestamp: new Date().toISOString(),
            source: 'MCP News API'
        };
        
        res.writeHead(200, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        });
        res.end(JSON.stringify(actualites));
        console.log('📰 MCP Actualités servies');
    }
    
    // 🌤️ API MCP - MÉTÉO
    else if (req.method === 'GET' && req.url.startsWith('/mcp/weather')) {
        const url = new URL(req.url, `http://${req.headers.host}`);
        const ville = url.searchParams.get('city') || 'Paris';
        
        const meteo = {
            city: ville,
            temperature: Math.round(Math.random() * 20 + 5),
            condition: ['Ensoleillé', 'Nuageux', 'Pluvieux'][Math.floor(Math.random() * 3)],
            humidity: Math.round(Math.random() * 40 + 40),
            wind: Math.round(Math.random() * 20 + 5),
            timestamp: new Date().toISOString(),
            source: 'MCP Weather API'
        };
        
        res.writeHead(200, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        });
        res.end(JSON.stringify(meteo));
        console.log(`🌤️ MCP Météo pour ${ville}`);
    }
    
    // 🧮 API MCP - CALCULATRICE
    else if (req.method === 'POST' && req.url === '/mcp/calculate') {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const expression = data.expression;
                
                // Calcul sécurisé
                const resultat = eval(expression.replace(/[^0-9+\-*/().\s]/g, ''));
                
                res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(JSON.stringify({
                    expression: expression,
                    result: resultat,
                    timestamp: new Date().toISOString(),
                    source: 'MCP Calculator'
                }));
                
                console.log(`🧮 MCP Calcul: ${expression} = ${resultat}`);
                
            } catch (error) {
                res.writeHead(400, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(JSON.stringify({
                    error: 'Erreur de calcul',
                    expression: body
                }));
            }
        });
    }
    
    // 🔗 PROXY VERS JARVIS ORIGINAL
    else if (req.url.startsWith('/completion')) {
        const options = {
            hostname: '127.0.0.1',
            port: 8080,
            path: req.url,
            method: req.method,
            headers: { ...req.headers }
        };

        delete options.headers.host;

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, {
                ...proxyRes.headers,
                'Access-Control-Allow-Origin': '*'
            });
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (error) => {
            console.error('❌ Erreur proxy JARVIS:', error.message);
            res.writeHead(500);
            res.end('Erreur connexion JARVIS');
        });

        req.pipe(proxyReq);
    }
    
    // 🌐 CORS PREFLIGHT
    else if (req.method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        });
        res.end();
    }
    
    // 🚫 404
    else {
        res.writeHead(404);
        res.end('Page non trouvée');
    }
});

// 🚀 DÉMARRAGE SERVEUR
const PORT = 8090;
server.listen(PORT, () => {
    console.log(`✅ SERVEUR JARVIS MCP ACTIF sur http://127.0.0.1:${PORT}`);
    console.log('🔌 Support MCP complet activé');
    console.log('🌐 APIs disponibles:');
    console.log('   • /mcp/search - Recherche web');
    console.log('   • /mcp/news - Actualités');
    console.log('   • /mcp/weather - Météo');
    console.log('   • /mcp/calculate - Calculatrice');
    console.log('🔗 Proxy JARVIS: http://127.0.0.1:8080');
});

// 🛡️ GESTION ERREURS
server.on('error', (error) => {
    console.error('❌ Erreur serveur:', error.message);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt SERVEUR JARVIS MCP');
    server.close();
    process.exit(0);
});
