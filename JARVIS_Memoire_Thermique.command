#!/bin/bash

# 🧠 JARVIS COMPLET - Interface Cognitive avec Mémoire Thermique
# Jean-Luc - Lancement système complet avec tous les systèmes cognitifs

echo "🚀 DÉMARRAGE JARVIS SYSTÈME COMPLET"
echo "🧠 Mémoire thermique + Agents + Systèmes cognitifs"

# 📍 Aller dans le bon répertoire
cd "/Volumes/seagate/Louna_Electron_Latest"

echo "🔄 Vérification des processus existants..."

# 🛑 Arrêter les anciens processus
pkill -f "node proxy_memoire_reel.js" 2>/dev/null
pkill -f "node serveur_jarvis_complet.js" 2>/dev/null
pkill -f "llama-server" 2>/dev/null

sleep 2

echo "🚀 Démarrage llama-server..."
# 🤖 Démarrer llama-server
llama-server -m "/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf" \
    --host 127.0.0.1 \
    --port 8082 \
    --ctx-size 4096 \
    --threads 4 &

sleep 5

echo "🧠 Démarrage proxy mémoire thermique..."
# 🧠 Démarrer proxy avec mémoire thermique
node proxy_memoire_reel.js &

sleep 3

sleep 3

echo "🌐 Ouverture interface JARVIS améliorée..."
# 🌐 Ouvrir interface améliorée (celle qui fonctionne)
open "http://127.0.0.1:8080"

echo ""
echo "✅ JARVIS SYSTÈME COMPLET ACTIF !"
echo "🧠 Mémoire thermique: ✅"
echo "🤖 Agents autonomes: ✅"
echo "🎤 Reconnaissance vocale: ✅"
echo "🔊 Synthèse vocale: ✅"
echo "📹 Vision caméra: ✅"
echo "🧬 Évolution QI: ✅"
echo ""
echo "🌐 Interface JARVIS améliorée: http://127.0.0.1:8080"
echo "🧠 Panneau cognitif intégré avec boutons"
echo "🤖 DeepSeek R1 direct: http://127.0.0.1:8082"
