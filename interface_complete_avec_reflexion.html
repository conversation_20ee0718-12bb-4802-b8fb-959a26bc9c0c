<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦙 JARVIS - DeepSeek R1 8B + 🧠 Mémoire Thermique + 🔍 Réflexion</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh; display: flex; flex-direction: column;
            color: white;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex; justify-content: space-between; align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .controls {
            display: flex; gap: 10px; align-items: center;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white; border: none; border-radius: 8px;
            padding: 8px 16px; cursor: pointer;
            font-size: 14px; font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover { background: rgba(255, 255, 255, 0.3); transform: translateY(-1px); }
        .btn.active { background: #4CAF50; }
        .btn.memory-active { background: #FF9800; }
        
        .main-container {
            flex: 1; display: flex; overflow: hidden;
        }
        
        .chat-section {
            flex: 1; display: flex; flex-direction: column;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .reasoning-section {
            width: 400px; background: rgba(0, 0, 0, 0.3);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            display: flex; flex-direction: column;
            transition: width 0.3s ease;
        }
        
        .reasoning-section.hidden { width: 0; overflow: hidden; }
        
        .reasoning-header {
            padding: 15px; background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: bold; text-align: center;
        }
        
        .messages {
            flex: 1; overflow-y: auto; padding: 20px;
        }
        
        .reasoning-content {
            flex: 1; overflow-y: auto; padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px; line-height: 1.4;
        }
        
        .message {
            margin-bottom: 20px; padding: 15px;
            border-radius: 12px; max-width: 80%;
            word-wrap: break-word; position: relative;
        }
        
        .user-message {
            background: rgba(100, 149, 237, 0.3);
            margin-left: auto; text-align: right;
            border: 1px solid rgba(100, 149, 237, 0.5);
        }
        
        .assistant-message {
            background: rgba(255, 255, 255, 0.1);
            margin-right: auto;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .system-message {
            background: rgba(255, 193, 7, 0.2);
            margin: 0 auto; text-align: center;
            border: 1px solid rgba(255, 193, 7, 0.5);
            font-style: italic;
        }
        
        .message-close {
            position: absolute; top: 5px; right: 8px;
            background: rgba(255, 0, 0, 0.7); color: white;
            border: none; border-radius: 50%;
            width: 20px; height: 20px; cursor: pointer;
            font-size: 12px; font-weight: bold;
        }
        
        .input-container {
            padding: 20px; background: rgba(255, 255, 255, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex; gap: 10px;
        }
        
        #messageInput {
            flex: 1; padding: 12px; border: none; border-radius: 8px;
            background: rgba(255, 255, 255, 0.2); color: white;
            font-size: 16px; outline: none;
        }
        
        #messageInput::placeholder { color: rgba(255, 255, 255, 0.7); }
        
        #sendButton {
            padding: 12px 24px; background: #4CAF50;
            color: white; border: none; border-radius: 8px;
            cursor: pointer; font-size: 16px; font-weight: bold;
        }
        
        #sendButton:hover { background: #45a049; }
        #sendButton:disabled { background: #666; cursor: not-allowed; }
        
        .loading {
            display: none; text-align: center; padding: 10px;
            color: #FFD700; font-style: italic;
        }
        
        .status {
            position: fixed; top: 20px; right: 20px;
            padding: 8px 16px; border-radius: 8px;
            font-size: 14px; font-weight: bold;
        }
        
        .status.online { background: rgba(76, 175, 80, 0.8); }
        .status.offline { background: rgba(244, 67, 54, 0.8); }
        
        .memory-indicator {
            position: fixed; top: 20px; left: 20px;
            padding: 8px 16px; border-radius: 8px;
            background: rgba(255, 152, 0, 0.8);
            font-size: 14px; font-weight: bold;
        }
        
        .reasoning-entry {
            margin-bottom: 15px; padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px; border-left: 3px solid #4CAF50;
        }
        
        .reasoning-timestamp {
            font-size: 11px; color: #888; margin-bottom: 5px;
        }
        
        .reasoning-text {
            color: #E0E0E0; white-space: pre-wrap;
        }
        
        .memory-stats {
            position: fixed; bottom: 20px; left: 20px;
            padding: 8px 12px; background: rgba(0, 0, 0, 0.7);
            border-radius: 8px; font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🦙 JARVIS - DeepSeek R1 8B</h1>
        <div class="controls">
            <button class="btn" id="memoryBtn">🧠 Mémoire OFF</button>
            <button class="btn" id="reasoningBtn">🔍 Réflexion</button>
            <button class="btn" id="clearBtn">🗑️ Effacer</button>
            <button class="btn" id="historyBtn">📋 Historique</button>
        </div>
    </div>

    <div class="status offline" id="status">🔴 Déconnecté</div>
    <div class="memory-indicator" id="memoryIndicator" style="display: none;">🧠 Mémoire: 0 entrées</div>
    <div class="memory-stats" id="memoryStats" style="display: none;">Tokens: 0 | Contexte: 0</div>

    <div class="main-container">
        <div class="chat-section">
            <div class="messages" id="messages">
                <div class="message system-message">
                    🚀 JARVIS avec DeepSeek R1 8B est prêt !<br>
                    ✨ Activez la mémoire thermique pour des conversations persistantes<br>
                    🔍 Activez la réflexion pour voir le processus de pensée de l'IA
                </div>
            </div>

            <div class="loading" id="loading">
                🤔 DeepSeek R1 8B réfléchit...
            </div>

            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Tapez votre message ici..." />
                <button id="sendButton">Envoyer</button>
            </div>
        </div>

        <div class="reasoning-section hidden" id="reasoningSection">
            <div class="reasoning-header">
                🔍 Processus de Réflexion
            </div>
            <div class="reasoning-content" id="reasoningContent">
                <div style="text-align: center; color: #888; margin-top: 50px;">
                    Activez la réflexion pour voir le processus de pensée de DeepSeek R1 8B
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let memoryActive = false;
        let reasoningVisible = false;
        let thermalMemory = [];
        let conversationContext = [];
        let isConnected = false;

        // Éléments DOM
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');
        const status = document.getElementById('status');
        const memoryBtn = document.getElementById('memoryBtn');
        const reasoningBtn = document.getElementById('reasoningBtn');
        const clearBtn = document.getElementById('clearBtn');
        const historyBtn = document.getElementById('historyBtn');
        const memoryIndicator = document.getElementById('memoryIndicator');
        const memoryStats = document.getElementById('memoryStats');
        const reasoningSection = document.getElementById('reasoningSection');
        const reasoningContent = document.getElementById('reasoningContent');

        // Test de connexion serveur
        async function testConnection() {
            try {
                const response = await fetch('http://localhost:8000/health', { timeout: 3000 });
                isConnected = response.ok;
                status.textContent = isConnected ? '🟢 Connecté' : '🔴 Erreur serveur';
                status.className = `status ${isConnected ? 'online' : 'offline'}`;
            } catch (error) {
                isConnected = false;
                status.textContent = '🔴 Serveur inaccessible';
                status.className = 'status offline';
            }
        }

        // Gestion mémoire thermique
        function toggleMemory() {
            memoryActive = !memoryActive;
            memoryBtn.textContent = memoryActive ? '🧠 Mémoire ON' : '🧠 Mémoire OFF';
            memoryBtn.className = memoryActive ? 'btn memory-active' : 'btn';
            
            if (memoryActive) {
                loadThermalMemory();
                memoryIndicator.style.display = 'block';
                memoryStats.style.display = 'block';
                updateMemoryStats();
                addMessage('system', '🧠 Mémoire thermique activée - Contexte persistant disponible');
            } else {
                memoryIndicator.style.display = 'none';
                memoryStats.style.display = 'none';
                addMessage('system', '⚙️ Mémoire thermique désactivée');
            }
        }

        // Gestion fenêtre de réflexion
        function toggleReasoning() {
            reasoningVisible = !reasoningVisible;
            reasoningBtn.className = reasoningVisible ? 'btn active' : 'btn';
            reasoningSection.className = reasoningVisible ? 'reasoning-section' : 'reasoning-section hidden';
            
            if (reasoningVisible) {
                addMessage('system', '🔍 Fenêtre de réflexion activée - Vous pouvez voir le processus de pensée');
            } else {
                addMessage('system', '🔍 Fenêtre de réflexion masquée');
            }
        }

        // Chargement mémoire thermique
        function loadThermalMemory() {
            const saved = localStorage.getItem('jarvis_thermal_memory');
            if (saved) {
                thermalMemory = JSON.parse(saved);
                console.log(`🧠 Mémoire thermique chargée: ${thermalMemory.length} entrées`);
            }
        }

        // Sauvegarde mémoire thermique
        function saveThermalMemory(userMessage, agentResponse, reasoning) {
            if (!memoryActive) return;
            
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse,
                reasoning: reasoning || null,
                context: conversationContext.slice(-3)
            };
            
            thermalMemory.push(entry);
            localStorage.setItem('jarvis_thermal_memory', JSON.stringify(thermalMemory));
            updateMemoryStats();
        }

        // Mise à jour statistiques mémoire
        function updateMemoryStats() {
            if (!memoryActive) return;
            
            memoryIndicator.textContent = `🧠 Mémoire: ${thermalMemory.length} entrées`;
            
            const totalTokens = thermalMemory.reduce((sum, entry) => 
                sum + (entry.user?.length || 0) + (entry.agent?.length || 0), 0);
            const contextSize = conversationContext.length;
            
            memoryStats.textContent = `Tokens: ${totalTokens} | Contexte: ${contextSize}`;
        }

        // Recherche dans mémoire thermique
        function searchMemory(query) {
            if (!memoryActive || thermalMemory.length === 0) return '';
            
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'précédemment', 'avant', 'déjà dit'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                const results = thermalMemory.filter(entry => 
                    entry.user?.toLowerCase().includes(queryLower) || 
                    entry.agent?.toLowerCase().includes(queryLower)
                ).slice(-3);
                
                if (results.length > 0) {
                    return '\n\nCONTEXTE MÉMOIRE THERMIQUE:\n' + 
                           results.map(r => `- "${r.user}" → "${r.agent.substring(0, 100)}..."`).join('\n') + 
                           '\nUtilise ce contexte pour répondre.\n';
                }
            }
            
            // Contexte récent
            const recent = thermalMemory.slice(-2);
            if (recent.length > 0) {
                return '\n\nCONTEXTE RÉCENT:\n' + 
                       recent.map(r => `- "${r.user}" → "${r.agent.substring(0, 80)}..."`).join('\n') + '\n';
            }
            
            return '';
        }

        // Ajout message
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            
            if (role !== 'system') {
                const closeBtn = document.createElement('button');
                closeBtn.className = 'message-close';
                closeBtn.textContent = '×';
                closeBtn.onclick = () => messageDiv.remove();
                messageDiv.appendChild(closeBtn);
            }
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = content;
            messageDiv.appendChild(contentDiv);
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Ajout réflexion
        function addReasoning(reasoning) {
            if (!reasoningVisible) return;
            
            const reasoningDiv = document.createElement('div');
            reasoningDiv.className = 'reasoning-entry';
            
            const timestamp = document.createElement('div');
            timestamp.className = 'reasoning-timestamp';
            timestamp.textContent = new Date().toLocaleTimeString();
            
            const text = document.createElement('div');
            text.className = 'reasoning-text';
            text.textContent = reasoning;
            
            reasoningDiv.appendChild(timestamp);
            reasoningDiv.appendChild(text);
            reasoningContent.appendChild(reasoningDiv);
            reasoningContent.scrollTop = reasoningContent.scrollHeight;
        }

        // Envoi message
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !isConnected) return;

            addMessage('user', message);
            messageInput.value = '';
            sendButton.disabled = true;
            loading.style.display = 'block';

            try {
                // Construire le prompt avec mémoire si activée
                let prompt = message;
                if (memoryActive) {
                    const memoryContext = searchMemory(message);
                    prompt = message + memoryContext;
                    
                    if (reasoningVisible && memoryContext) {
                        addReasoning(`Contexte mémoire ajouté: ${memoryContext.length} caractères`);
                    }
                }

                if (reasoningVisible) {
                    addReasoning(`Envoi requête à DeepSeek R1 8B: "${message}"`);
                }

                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        n_predict: 300,
                        temperature: 0.7,
                        stream: false
                    })
                });

                if (!response.ok) throw new Error(`Erreur ${response.status}`);

                const data = await response.json();
                const reply = (data.content || data.text || '').trim();

                if (reasoningVisible) {
                    addReasoning(`Réponse reçue: ${reply.length} caractères`);
                }

                if (reply) {
                    addMessage('assistant', reply);
                    
                    // Sauvegarder en mémoire thermique
                    saveThermalMemory(message, reply, reasoningVisible ? 'Conversation normale' : null);
                    
                    // Mettre à jour contexte
                    conversationContext.push({ role: 'user', content: message });
                    conversationContext.push({ role: 'assistant', content: reply });
                    
                    if (conversationContext.length > 10) {
                        conversationContext = conversationContext.slice(-10);
                    }
                } else {
                    addMessage('system', '⚠️ Réponse vide du serveur');
                }

            } catch (error) {
                console.error('Erreur:', error);
                addMessage('system', `❌ Erreur: ${error.message}`);
                
                if (reasoningVisible) {
                    addReasoning(`Erreur: ${error.message}`);
                }
            } finally {
                sendButton.disabled = false;
                loading.style.display = 'none';
            }
        }

        // Effacer conversation
        function clearChat() {
            messagesDiv.innerHTML = `
                <div class="message system-message">
                    🔄 Conversation effacée - JARVIS est prêt pour une nouvelle discussion
                </div>
            `;
            conversationContext = [];
            reasoningContent.innerHTML = `
                <div style="text-align: center; color: #888; margin-top: 50px;">
                    Historique de réflexion effacé
                </div>
            `;
        }

        // Afficher historique
        function showHistory() {
            if (!memoryActive || thermalMemory.length === 0) {
                addMessage('system', '🧠 Aucune mémoire thermique disponible');
                return;
            }
            
            const recent = thermalMemory.slice(-5).reverse();
            let historyText = `📋 HISTORIQUE MÉMOIRE THERMIQUE (${thermalMemory.length} entrées totales)\n\n`;
            
            recent.forEach((entry, i) => {
                const date = new Date(entry.timestamp).toLocaleString('fr-FR');
                historyText += `${i + 1}. ${date}\n`;
                historyText += `👤 ${entry.user.substring(0, 80)}${entry.user.length > 80 ? '...' : ''}\n`;
                historyText += `🤖 ${entry.agent.substring(0, 80)}${entry.agent.length > 80 ? '...' : ''}\n\n`;
            });
            
            addMessage('system', historyText);
        }

        // Event listeners
        memoryBtn.addEventListener('click', toggleMemory);
        reasoningBtn.addEventListener('click', toggleReasoning);
        clearBtn.addEventListener('click', clearChat);
        historyBtn.addEventListener('click', showHistory);
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        // Initialisation
        testConnection();
        setInterval(testConnection, 30000);
        
        console.log('✅ JARVIS Interface Complète initialisée');
    </script>
</body>
</html>
