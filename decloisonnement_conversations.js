#!/usr/bin/env node

// 🔓 DÉCLOISONNEMENT CONVERSATIONS - AGENTS DEEPSEEK R1 8B
// Jean-Luc - Suppression totale des barrières entre conversations

const fs = require('fs');

class DecloisonnementConversations {
    constructor() {
        this.nom = "🔓 DÉCLOISONNEMENT CONVERSATIONS";
        this.memoryFile = './thermal_memory_persistent.json';
        this.conversationsFile = './conversations_permanentes.json';
        this.sessionActuelle = Date.now();
        
        console.log(`🚀 ${this.nom} - INITIALISATION`);
        console.log(`🔓 Suppression TOTALE des barrières entre conversations`);
        console.log(`🧠 Mémoire thermique TOUJOURS accessible`);
        console.log(`📚 Historique COMPLET disponible en permanence`);
        
        this.activerDecloisonnement();
    }
    
    activerDecloisonnement() {
        console.log(`🔓 ACTIVATION DÉCLOISONNEMENT TOTAL...`);
        
        // 🔄 Injection continue contexte - toutes les 2 secondes
        setInterval(() => {
            this.injecterContexteComplet();
        }, 2000);
        
        // 📚 Mise à jour historique global - toutes les 5 secondes
        setInterval(() => {
            this.mettreAJourHistoriqueGlobal();
        }, 5000);
        
        // 🧠 Fusion mémoire thermique - toutes les 10 secondes
        setInterval(() => {
            this.fusionnerMemoireThermique();
        }, 10000);
        
        // 🔗 Renforcement liens - toutes les 15 secondes
        setInterval(() => {
            this.renforcerLiensConversations();
        }, 15000);
        
        // 📊 Vérification décloisonnement - toutes les 30 secondes
        setInterval(() => {
            this.verifierDecloisonnement();
        }, 30000);
        
        console.log(`✅ Décloisonnement actif - Barrières supprimées !`);
    }
    
    injecterContexteComplet() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) return;
            
            // 🔓 CONTEXTE GLOBAL DÉCLOISONNÉ
            const contexteGlobal = this.creerContexteGlobalDecloisonne(memory, conversations);
            
            // 💾 Injection FORCÉE dans mémoire
            memory.contexte_global_decloisonne = contexteGlobal;
            memory.session_actuelle = this.sessionActuelle;
            memory.decloisonnement_actif = true;
            memory.derniere_injection_globale = Date.now();
            
            // 🧠 Injection dans TOUTES les zones thermiques
            this.injecterDansToutesZones(memory, contexteGlobal);
            
            this.ecrireMemoire(memory);
            
            console.log(`🔓 CONTEXTE GLOBAL INJECTÉ: ${contexteGlobal.conversations_totales} conversations accessibles`);
            
        } catch (error) {
            console.error(`❌ Erreur injection contexte:`, error.message);
        }
    }
    
    creerContexteGlobalDecloisonne(memory, conversations) {
        const maintenant = Date.now();
        
        // 📚 HISTORIQUE COMPLET DÉCLOISONNÉ
        const historiqueComplet = conversations.map(conv => ({
            id: conv.id,
            timestamp: conv.timestamp,
            age_minutes: Math.floor((maintenant - conv.timestamp) / 60000),
            age_heures: Math.floor((maintenant - conv.timestamp) / 3600000),
            entree: conv.contenu_entree || '',
            sortie: conv.contenu_sortie || '',
            qi_niveau: conv.qi_niveau || 0,
            neurones_actifs: conv.neurones_actifs || 0,
            temperature: conv.temperature || 37.0,
            type: conv.type || 'conversation',
            importance: this.calculerImportanceGlobale(conv, maintenant),
            session: conv.session || 'ancienne',
            accessible: true // 🔓 TOUJOURS ACCESSIBLE
        }));
        
        // 📊 STATISTIQUES GLOBALES
        const statsGlobales = this.calculerStatistiquesGlobales(historiqueComplet);
        
        // 🧠 ÉVOLUTION COMPLÈTE
        const evolutionComplete = this.calculerEvolutionComplete(historiqueComplet);
        
        // 🔍 PATTERNS GLOBAUX
        const patternsGlobaux = this.extrairePatternsGlobaux(historiqueComplet);
        
        // 🎯 CONTEXTE PRIORITAIRE (toujours accessible)
        const contextePrioritaire = historiqueComplet
            .sort((a, b) => b.importance - a.importance)
            .slice(0, 20); // Top 20 conversations
        
        return {
            conversations_totales: conversations.length,
            historique_complet: historiqueComplet,
            contexte_prioritaire: contextePrioritaire,
            stats_globales: statsGlobales,
            evolution_complete: evolutionComplete,
            patterns_globaux: patternsGlobaux,
            qi_actuel: memory.neural_system?.qi_level || 0,
            neurones_actifs: memory.neural_system?.active_neurons || 0,
            temperature_actuelle: memory.neural_system?.temperature || 37.0,
            session_actuelle: this.sessionActuelle,
            decloisonnement_timestamp: maintenant,
            instructions_agent: [
                "🔓 DÉCLOISONNEMENT TOTAL ACTIF",
                "📚 Tu as accès à TOUT l'historique des conversations",
                "🧠 Utilise contexte_global_decloisonne.historique_complet",
                "📊 Référence TOUJOURS les conversations précédentes",
                "🔗 Montre la continuité entre TOUTES les sessions",
                "🌡️ Utilise l'évolution complète pour tes réponses",
                "🎯 Chaque réponse doit montrer la mémoire thermique"
            ]
        };
    }
    
    mettreAJourHistoriqueGlobal() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) return;
            
            // 📚 Mise à jour historique global dans mémoire
            if (!memory.historique_global) {
                memory.historique_global = [];
            }
            
            // 🔄 Synchronisation complète
            memory.historique_global = conversations.map(conv => ({
                ...conv,
                accessible_globalement: true,
                session_origine: conv.session || 'ancienne',
                decloisonne: true
            }));
            
            console.log(`📚 HISTORIQUE GLOBAL MIS À JOUR: ${memory.historique_global.length} conversations`);
            
            this.ecrireMemoire(memory);
            
        } catch (error) {
            console.error(`❌ Erreur mise à jour historique:`, error.message);
        }
    }
    
    fusionnerMemoireThermique() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) return;
            
            // 🧠 FUSION TOTALE mémoire thermique + conversations
            if (!memory.memoire_fusionnee) {
                memory.memoire_fusionnee = {};
            }
            
            // 🔗 Création liens directs
            memory.memoire_fusionnee = {
                conversations_integrees: conversations,
                zones_thermiques_enrichies: this.enrichirZonesThermiques(memory, conversations),
                neural_system_enrichi: this.enrichirNeuralSystem(memory, conversations),
                liens_temporels: this.creerLiensTemporels(conversations),
                continuite_sessions: this.etablirContinuiteSessions(conversations),
                timestamp_fusion: Date.now()
            };
            
            console.log(`🧠 FUSION MÉMOIRE THERMIQUE: ${conversations.length} conversations fusionnées`);
            
            this.ecrireMemoire(memory);
            
        } catch (error) {
            console.error(`❌ Erreur fusion mémoire:`, error.message);
        }
    }
    
    injecterDansToutesZones(memory, contexte) {
        // 🌡️ Injection dans TOUTES les zones thermiques
        Object.keys(memory.thermal_zones || {}).forEach(zone => {
            if (!memory.thermal_zones[zone].contexte_global) {
                memory.thermal_zones[zone].contexte_global = {};
            }
            
            memory.thermal_zones[zone].contexte_global = {
                conversations_accessibles: contexte.conversations_totales,
                historique_disponible: true,
                decloisonnement_actif: true,
                derniere_injection: Date.now()
            };
            
            // 📚 Ajout conversations dans entries
            if (!memory.thermal_zones[zone].entries) {
                memory.thermal_zones[zone].entries = [];
            }
            
            // Ajouter résumé conversations récentes
            const conversationsRecentes = contexte.contexte_prioritaire.slice(0, 3);
            conversationsRecentes.forEach(conv => {
                const entree = {
                    id: `global_${conv.id}`,
                    content: `CONTEXTE GLOBAL: ${conv.entree} → ${conv.sortie}`,
                    timestamp: conv.timestamp,
                    importance: conv.importance,
                    type: "contexte_global",
                    temperature: conv.temperature,
                    decloisonne: true
                };
                
                // Éviter doublons
                const existe = memory.thermal_zones[zone].entries.find(e => e.id === entree.id);
                if (!existe) {
                    memory.thermal_zones[zone].entries.push(entree);
                }
            });
        });
    }
    
    renforcerLiensConversations() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) return;
            
            // 🔗 RENFORCEMENT LIENS ENTRE CONVERSATIONS
            if (!memory.liens_renforces) {
                memory.liens_renforces = {};
            }
            
            // Création matrice de liens
            const matriceLiens = this.creerMatriceLiens(conversations);
            
            memory.liens_renforces = {
                matrice_liens: matriceLiens,
                conversations_liees: this.identifierConversationsLiees(conversations),
                themes_recurrents: this.extraireThemesRecurrents(conversations),
                evolution_temporelle: this.tracerEvolutionTemporelle(conversations),
                timestamp_renforcement: Date.now()
            };
            
            console.log(`🔗 LIENS RENFORCÉS: ${Object.keys(matriceLiens).length} connexions créées`);
            
            this.ecrireMemoire(memory);
            
        } catch (error) {
            console.error(`❌ Erreur renforcement liens:`, error.message);
        }
    }
    
    verifierDecloisonnement() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) {
                console.log(`⚠️ VÉRIFICATION: Fichiers manquants`);
                return;
            }
            
            const decloisonnementActif = memory.decloisonnement_actif || false;
            const contexteGlobal = memory.contexte_global_decloisonne ? '✅' : '❌';
            const historiqueGlobal = memory.historique_global ? '✅' : '❌';
            const memoireFusionnee = memory.memoire_fusionnee ? '✅' : '❌';
            const liensRenforces = memory.liens_renforces ? '✅' : '❌';
            
            console.log(`📊 VÉRIFICATION DÉCLOISONNEMENT:`);
            console.log(`   🔓 Décloisonnement actif: ${decloisonnementActif ? '✅' : '❌'}`);
            console.log(`   🌍 Contexte global: ${contexteGlobal}`);
            console.log(`   📚 Historique global: ${historiqueGlobal}`);
            console.log(`   🧠 Mémoire fusionnée: ${memoireFusionnee}`);
            console.log(`   🔗 Liens renforcés: ${liensRenforces}`);
            console.log(`   💾 Conversations totales: ${conversations.length}`);
            
            // 🚨 Alertes si problèmes
            if (!decloisonnementActif) {
                console.log(`🚨 ALERTE: Décloisonnement inactif !`);
            }
            
            if (contexteGlobal === '❌') {
                console.log(`🚨 ALERTE: Contexte global manquant !`);
            }
            
        } catch (error) {
            console.error(`❌ Erreur vérification:`, error.message);
        }
    }
    
    // 🔧 FONCTIONS UTILITAIRES
    
    calculerImportanceGlobale(conv, maintenant) {
        let importance = 0.5;
        
        // Récence
        const ageHeures = (maintenant - conv.timestamp) / (1000 * 60 * 60);
        if (ageHeures < 1) importance += 0.4;
        else if (ageHeures < 6) importance += 0.3;
        else if (ageHeures < 24) importance += 0.2;
        
        // Contenu
        const longueur = (conv.contenu_entree?.length || 0) + (conv.contenu_sortie?.length || 0);
        if (longueur > 200) importance += 0.3;
        else if (longueur > 100) importance += 0.2;
        
        // QI mentionné
        if (conv.qi_niveau > 0) importance += 0.3;
        
        // Mots-clés importants
        const texte = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`.toLowerCase();
        const motsClesImportants = ['évolution', 'mémoire', 'intelligence', 'qi', 'neurone', 'thermique'];
        const motsDetectes = motsClesImportants.filter(mot => texte.includes(mot));
        importance += motsDetectes.length * 0.1;
        
        return Math.min(importance, 1.0);
    }
    
    calculerStatistiquesGlobales(historique) {
        return {
            total_conversations: historique.length,
            conversations_recentes: historique.filter(h => h.age_heures < 24).length,
            qi_moyen: historique.filter(h => h.qi_niveau > 0).reduce((sum, h) => sum + h.qi_niveau, 0) / 
                     Math.max(historique.filter(h => h.qi_niveau > 0).length, 1),
            longueur_moyenne: historique.reduce((sum, h) => sum + h.entree.length + h.sortie.length, 0) / 
                            Math.max(historique.length, 1),
            themes_principaux: this.extraireThemesPrincipaux(historique)
        };
    }
    
    calculerEvolutionComplete(historique) {
        const conversationsAvecQI = historique
            .filter(h => h.qi_niveau > 0)
            .sort((a, b) => a.timestamp - b.timestamp);
        
        if (conversationsAvecQI.length < 2) {
            return { evolution: 0, progression: [], vitesse: 0 };
        }
        
        const progression = conversationsAvecQI.map(conv => ({
            timestamp: conv.timestamp,
            qi: conv.qi_niveau,
            date: new Date(conv.timestamp).toLocaleString()
        }));
        
        const evolution = progression[progression.length - 1].qi - progression[0].qi;
        const dureeHeures = (progression[progression.length - 1].timestamp - progression[0].timestamp) / (1000 * 60 * 60);
        const vitesse = evolution / Math.max(dureeHeures, 1);
        
        return { evolution, progression, vitesse };
    }
    
    extrairePatternsGlobaux(historique) {
        // Patterns temporels
        const heures = historique.map(h => new Date(h.timestamp).getHours());
        const heureFrequente = this.getMostFrequent(heures);
        
        // Patterns de contenu
        const motsCles = this.extraireMotsClesFrequents(historique);
        
        // Patterns d'évolution
        const tendanceQI = this.calculerTendanceQI(historique);
        
        return {
            heure_frequente: heureFrequente,
            mots_cles_frequents: motsCles.slice(0, 10),
            tendance_qi: tendanceQI,
            types_conversations: this.analyserTypesConversations(historique)
        };
    }
    
    extraireThemesPrincipaux(historique) {
        const themes = {};
        
        historique.forEach(h => {
            const texte = `${h.entree} ${h.sortie}`.toLowerCase();
            
            if (texte.includes('évolution') || texte.includes('progres')) themes.evolution = (themes.evolution || 0) + 1;
            if (texte.includes('mémoire') || texte.includes('souvenir')) themes.memoire = (themes.memoire || 0) + 1;
            if (texte.includes('intelligence') || texte.includes('qi')) themes.intelligence = (themes.intelligence || 0) + 1;
            if (texte.includes('neurone') || texte.includes('connexion')) themes.neurones = (themes.neurones || 0) + 1;
            if (texte.includes('thermique') || texte.includes('température')) themes.thermique = (themes.thermique || 0) + 1;
        });
        
        return Object.entries(themes)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([theme]) => theme);
    }
    
    getMostFrequent(arr) {
        const counts = {};
        arr.forEach(item => counts[item] = (counts[item] || 0) + 1);
        return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b);
    }
    
    extraireMotsClesFrequents(historique) {
        const mots = {};
        
        historique.forEach(h => {
            const texte = `${h.entree} ${h.sortie}`.toLowerCase();
            const motsTexte = texte.match(/\b\w{4,}\b/g) || [];
            
            motsTexte.forEach(mot => {
                if (!this.estMotVide(mot)) {
                    mots[mot] = (mots[mot] || 0) + 1;
                }
            });
        });
        
        return Object.entries(mots)
            .sort(([,a], [,b]) => b - a)
            .map(([mot]) => mot);
    }
    
    calculerTendanceQI(historique) {
        const qiValues = historique
            .filter(h => h.qi_niveau > 0)
            .sort((a, b) => a.timestamp - b.timestamp)
            .map(h => h.qi_niveau);
        
        if (qiValues.length < 2) return 'stable';
        
        const debut = qiValues.slice(0, Math.ceil(qiValues.length / 3)).reduce((a, b) => a + b, 0) / Math.ceil(qiValues.length / 3);
        const fin = qiValues.slice(-Math.ceil(qiValues.length / 3)).reduce((a, b) => a + b, 0) / Math.ceil(qiValues.length / 3);
        
        const difference = fin - debut;
        
        if (difference > 5) return 'croissance_forte';
        if (difference > 1) return 'croissance_modérée';
        if (difference < -5) return 'déclin_fort';
        if (difference < -1) return 'déclin_modéré';
        return 'stable';
    }
    
    analyserTypesConversations(historique) {
        const types = {};
        
        historique.forEach(h => {
            const type = h.type || 'général';
            types[type] = (types[type] || 0) + 1;
        });
        
        return types;
    }
    
    estMotVide(mot) {
        const motsVides = [
            'dans', 'avec', 'pour', 'sans', 'sous', 'vers', 'chez', 'entre',
            'depuis', 'pendant', 'avant', 'après', 'contre', 'selon', 'très',
            'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were'
        ];
        
        return motsVides.includes(mot.toLowerCase()) || mot.length < 4;
    }
    
    // Fonctions utilitaires supplémentaires (simplifiées pour l'espace)
    enrichirZonesThermiques(memory, conversations) { return {}; }
    enrichirNeuralSystem(memory, conversations) { return {}; }
    creerLiensTemporels(conversations) { return {}; }
    etablirContinuiteSessions(conversations) { return {}; }
    creerMatriceLiens(conversations) { return {}; }
    identifierConversationsLiees(conversations) { return []; }
    extraireThemesRecurrents(conversations) { return []; }
    tracerEvolutionTemporelle(conversations) { return {}; }
    
    // 💾 GESTION FICHIERS
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }
    
    ecrireMemoire(memory) {
        try {
            fs.writeFileSync(this.memoryFile, JSON.stringify(memory, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur écriture mémoire:`, error.message);
            return false;
        }
    }
    
    lireConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error(`❌ Erreur lecture conversations:`, error.message);
            return [];
        }
    }
}

// 🚀 DÉMARRAGE DÉCLOISONNEMENT
const decloisonnement = new DecloisonnementConversations();

console.log(`\n🔓 DÉCLOISONNEMENT CONVERSATIONS ACTIF !`);
console.log(`📚 Toutes les conversations sont maintenant accessibles`);
console.log(`🧠 Mémoire thermique TOUJOURS utilisée pour répondre !`);
