const http = require('http');
const zlib = require('zlib');

class ProxyJarvisStable {
    constructor() {
        this.portProxy = 8084;  // Nouveau port pour éviter conflits
        this.portLlama = 8080;
        this.nom = "🧠 JARVIS STABLE";
    }

    demarrer() {
        console.log(`🚀 ${this.nom} - DÉMARRAGE`);
        
        const server = http.createServer((req, res) => {
            try {
                if (req.method === 'GET' && req.url === '/') {
                    this.servirInterface(req, res);
                } else {
                    this.rediriger(req, res);
                }
            } catch (error) {
                this.gererErreur(error, res);
            }
        });

        server.listen(this.portProxy, () => {
            console.log(`✅ ${this.nom} ACTIF sur http://127.0.0.1:${this.portProxy}`);
        });
    }

    async servirInterface(req, res) {
        try {
            const htmlOriginal = await this.recupererHTML();
            const htmlModifie = this.ajouterJarvis(htmlOriginal);
            
            res.writeHead(200, {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache'
            });
            res.end(htmlModifie);
            
        } catch (error) {
            this.gererErreur(error, res);
        }
    }

    recupererHTML() {
        return new Promise((resolve, reject) => {
            const req = http.request({
                hostname: '127.0.0.1',
                port: this.portLlama,
                path: '/',
                method: 'GET'
            }, (res) => {
                let chunks = [];
                res.on('data', chunk => chunks.push(chunk));
                res.on('end', () => {
                    let buffer = Buffer.concat(chunks);
                    const encoding = res.headers['content-encoding'];
                    
                    if (encoding === 'gzip') {
                        zlib.gunzip(buffer, (err, data) => {
                            if (err) reject(err);
                            else resolve(data.toString());
                        });
                    } else {
                        resolve(buffer.toString());
                    }
                });
            });
            req.on('error', reject);
            req.end();
        });
    }

    ajouterJarvis(html) {
        // 🏷️ CHANGER TITRE
        html = html.replace(/<title>.*?<\/title>/i, '<title>🧠 JARVIS - Interface Cognitive</title>');
        
        // 🎨 STYLES SIMPLES
        const styles = `
        <style>
        #jarvis-panel {
            position: fixed !important;
            top: 10px !important;
            right: 10px !important;
            width: 380px !important;
            background: rgba(0,0,0,0.9) !important;
            border: 2px solid #00ffff !important;
            border-radius: 10px !important;
            padding: 15px !important;
            color: #00ffff !important;
            font-family: monospace !important;
            z-index: 99999 !important;
            box-shadow: 0 0 20px rgba(0,255,255,0.5) !important;
        }
        
        #jarvis-header {
            text-align: center !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #00ffff !important;
            padding-bottom: 8px !important;
        }
        
        #jarvis-buttons {
            display: flex !important;
            gap: 8px !important;
            margin-bottom: 10px !important;
            flex-wrap: wrap !important;
        }
        
        .jarvis-btn {
            flex: 1 !important;
            padding: 8px !important;
            background: #1a1a2e !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
            border-radius: 5px !important;
            cursor: pointer !important;
            font-size: 11px !important;
            min-width: 80px !important;
        }
        
        .jarvis-btn:hover {
            background: #00ffff !important;
            color: #000 !important;
        }
        
        #jarvis-status {
            background: rgba(0,255,255,0.1) !important;
            padding: 8px !important;
            border-radius: 5px !important;
            text-align: center !important;
            font-size: 12px !important;
        }
        
        #jarvis-name-input {
            background: rgba(0,255,255,0.1) !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
            padding: 4px !important;
            border-radius: 4px !important;
            width: 150px !important;
            text-align: center !important;
            margin-top: 5px !important;
        }
        </style>`;
        
        // 🧠 PANNEAU JARVIS SIMPLE
        const panneau = `
        <div id="jarvis-panel">
            <div id="jarvis-header">
                <h3>🧠 JARVIS</h3>
                <input type="text" id="jarvis-name-input" placeholder="Nom agent" value="JARVIS">
            </div>
            
            <div id="jarvis-buttons">
                <button class="jarvis-btn" onclick="jarvisMemoire()">🧠 Mémoire</button>
                <button class="jarvis-btn" onclick="jarvisEvoluer()">🚀 Évoluer</button>
                <button class="jarvis-btn" onclick="jarvisAnalyse()">🔍 Analyse</button>
            </div>
            
            <div id="jarvis-buttons">
                <button class="jarvis-btn" onclick="jarvisSettings()">⚙️ Settings</button>
                <button class="jarvis-btn" onclick="jarvisTheme()">🎨 Theme</button>
                <button class="jarvis-btn" onclick="jarvisToggle()">📱 Réduire</button>
            </div>
            
            <div id="jarvis-status">
                <div>QI: <span id="jarvis-qi">341.0</span></div>
                <div>🧠 Mémoire Thermique Active</div>
            </div>
        </div>`;
        
        // 📜 SCRIPT SIMPLE SANS ERREURS
        const script = `
        <script>
        // 🧠 JARVIS SYSTÈME SIMPLE ET STABLE
        let jarvisQI = 341.0;
        let jarvisMinimized = false;
        
        function jarvisMemoire() {
            const nom = document.getElementById('jarvis-name-input').value || 'JARVIS';
            const prompt = nom + ', te souviens-tu de nos conversations précédentes ? Utilise ta mémoire thermique.';
            jarvisEnvoyerPrompt(prompt);
            console.log('🧠 Mémoire thermique déclenchée');
        }
        
        function jarvisEvoluer() {
            jarvisQI += Math.random() * 2 + 0.5;
            document.getElementById('jarvis-qi').textContent = jarvisQI.toFixed(1);
            
            const nom = document.getElementById('jarvis-name-input').value || 'JARVIS';
            const prompt = nom + ', comment évolue ton QI ? Explique tes nouvelles capacités cognitives.';
            jarvisEnvoyerPrompt(prompt);
            console.log('🚀 Évolution QI:', jarvisQI.toFixed(1));
        }
        
        function jarvisAnalyse() {
            const nom = document.getElementById('jarvis-name-input').value || 'JARVIS';
            const prompt = nom + ', analyse tes pensées et explique ton processus de réflexion actuel.';
            jarvisEnvoyerPrompt(prompt);
            console.log('🔍 Analyse cognitive déclenchée');
        }
        
        function jarvisSettings() {
            // Chercher bouton settings original
            const settingsBtn = document.querySelector('button[title*="Settings"], .settings-btn, button:contains("Settings")');
            if (settingsBtn) {
                settingsBtn.click();
                console.log('⚙️ Settings ouvert');
            } else {
                alert('⚙️ Bouton Settings non trouvé dans l\\'interface originale');
            }
        }
        
        function jarvisTheme() {
            // Chercher bouton theme original
            const themeBtn = document.querySelector('button[title*="Theme"], .theme-btn, button:contains("Theme")');
            if (themeBtn) {
                themeBtn.click();
                console.log('🎨 Theme changé');
            } else {
                alert('🎨 Bouton Theme non trouvé dans l\\'interface originale');
            }
        }
        
        function jarvisToggle() {
            const panel = document.getElementById('jarvis-panel');
            if (panel) {
                if (jarvisMinimized) {
                    panel.style.height = 'auto';
                    jarvisMinimized = false;
                } else {
                    panel.style.height = '50px';
                    panel.style.overflow = 'hidden';
                    jarvisMinimized = true;
                }
            }
        }
        
        function jarvisEnvoyerPrompt(prompt) {
            // Trouver zone de saisie
            const input = document.querySelector('textarea, input[type="text"]:not(#jarvis-name-input)');
            if (input) {
                input.value = prompt;
                input.focus();
                
                // Essayer d'envoyer
                setTimeout(() => {
                    const sendBtn = document.querySelector('button[type="submit"], .send-btn');
                    if (sendBtn) {
                        sendBtn.click();
                    } else {
                        // Essayer Enter
                        const event = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            keyCode: 13,
                            bubbles: true
                        });
                        input.dispatchEvent(event);
                    }
                }, 100);
            }
        }
        
        // 🚀 INITIALISATION SIMPLE
        function jarvisInit() {
            console.log('🧠 JARVIS Système Stable - Initialisé');
            
            // Évolution QI automatique
            setInterval(() => {
                jarvisQI += Math.random() * 0.1;
                const qiElement = document.getElementById('jarvis-qi');
                if (qiElement) {
                    qiElement.textContent = jarvisQI.toFixed(1);
                }
            }, 10000);
            
            // Mise à jour nom agent
            const nameInput = document.getElementById('jarvis-name-input');
            if (nameInput) {
                nameInput.addEventListener('input', function() {
                    const newName = this.value.trim().toUpperCase();
                    if (newName) {
                        document.title = newName + ' - Interface Cognitive';
                        console.log('🏷️ Nom agent:', newName);
                    }
                });
            }
        }
        
        // Démarrage sécurisé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', jarvisInit);
        } else {
            setTimeout(jarvisInit, 1000);
        }
        </script>`;
        
        // 🔧 INJECTION DANS HTML
        html = html.replace('</head>', styles + '</head>');
        html = html.replace('</body>', panneau + script + '</body>');
        
        return html;
    }

    rediriger(req, res) {
        const options = {
            hostname: '127.0.0.1',
            port: this.portLlama,
            path: req.url,
            method: req.method,
            headers: { ...req.headers }
        };

        delete options.headers.host;

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (error) => {
            this.gererErreur(error, res);
        });

        req.pipe(proxyReq);
    }

    gererErreur(error, res) {
        console.error(`❌ ERREUR:`, error.message);
        
        if (!res.headersSent) {
            res.writeHead(500, { 'Content-Type': 'text/html' });
            res.end(`
                <h1>❌ ERREUR JARVIS</h1>
                <p>Erreur: ${error.message}</p>
                <button onclick="location.reload()">🔄 Réessayer</button>
            `);
        }
    }
}

// 🚀 DÉMARRAGE
const proxy = new ProxyJarvisStable();
proxy.demarrer();
