#!/usr/bin/env node

// Test RÉEL automatisé de la mémoire thermique avec DeepSeek R1 8B
// Ce script fait de VRAIS appels au serveur, pas de simulation

const http = require('http');
const fs = require('fs');

console.log('🧪 DÉBUT TEST RÉEL MÉMOIRE THERMIQUE AVEC DEEPSEEK R1 8B');
console.log('=' .repeat(60));

let thermalMemory = [];
let testResults = [];

// Fonctions mémoire thermique (exactes de l'interface)
function saveThermalMemory(userMessage, agentResponse) {
    const entry = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        user: userMessage,
        agent: agentResponse,
        keywords: extractKeywords(userMessage + ' ' + agentResponse),
        context_type: determineContextType(userMessage),
        importance: calculateImportance(userMessage, agentResponse)
    };
    
    thermalMemory.push(entry);
    
    console.log(`💾 Sauvegardé: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`);
    console.log(`📊 Mots-clés: ${entry.keywords.join(', ')} | Type: ${entry.context_type} | Score: ${entry.importance}`);
    
    return entry;
}

function searchMemory(query) {
    if (thermalMemory.length === 0) {
        console.log('🧠 Mémoire vide');
        return '';
    }
    
    console.log(`🔍 Recherche pour: "${query}"`);
    console.log(`🧠 Base: ${thermalMemory.length} entrée(s)`);
    
    const queryAnalysis = analyzeQuery(query);
    console.log(`🎯 Type: ${queryAnalysis.type}, Triggers: ${queryAnalysis.hasMemoryTrigger}`);
    
    const searchResults = performMemorySearch(queryAnalysis);
    console.log(`📁 Trouvé: ${searchResults.length} résultat(s)`);
    
    if (searchResults.length > 0) {
        const context = buildContextFromResults(searchResults, queryAnalysis);
        console.log(`🧠 Contexte: ${context.length} caractères`);
        return context;
    }
    
    return '';
}

function extractKeywords(text) {
    const words = text.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 2);
    
    const important = ['nom', 'appelle', 'suis', 'jean-luc', 'bonjour', 'souviens', 'rappelle'];
    return words.filter(word => important.includes(word) || word.length > 4);
}

function determineContextType(message) {
    const msg = message.toLowerCase();
    if (msg.includes('appelle') || msg.includes('nom')) return 'identity';
    if (msg.includes('souviens') || msg.includes('rappelle')) return 'memory_query';
    if (msg.includes('bonjour') || msg.includes('salut')) return 'greeting';
    return 'general';
}

function calculateImportance(user, response) {
    let score = 1;
    if (user.toLowerCase().includes('appelle')) score += 3;
    if (user.toLowerCase().includes('souviens')) score += 2;
    if (response.toLowerCase().includes('jean-luc')) score += 3;
    return Math.min(score, 5);
}

function analyzeQuery(query) {
    const queryLower = query.toLowerCase();
    const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom', 'comment je', 'qui suis'];
    
    let type = 'general';
    if (queryLower.includes('souviens') || queryLower.includes('rappelle')) {
        type = 'memory_recall';
    } else if (queryLower.includes('nom') || queryLower.includes('appelle')) {
        type = 'identity_query';
    }
    
    const hasMemoryTrigger = triggers.some(trigger => queryLower.includes(trigger));
    const keywords = queryLower.split(' ').filter(word => word.length > 2);
    
    return { type, keywords, hasMemoryTrigger, originalQuery: query };
}

function performMemorySearch(queryAnalysis) {
    let results = [];
    
    thermalMemory.forEach(entry => {
        let relevanceScore = 0;
        
        queryAnalysis.keywords.forEach(keyword => {
            if (entry.keywords && entry.keywords.includes(keyword)) relevanceScore += 2;
            if (entry.user.toLowerCase().includes(keyword)) relevanceScore += 1;
            if (entry.agent.toLowerCase().includes(keyword)) relevanceScore += 1;
        });
        
        if (entry.context_type === queryAnalysis.type) relevanceScore += 1;
        relevanceScore += entry.importance || 0;
        
        if (queryAnalysis.type === 'memory_recall' || queryAnalysis.type === 'identity_query') {
            if (entry.user.toLowerCase().includes('appelle') || 
                entry.user.toLowerCase().includes('nom') ||
                entry.agent.toLowerCase().includes('jean-luc')) {
                relevanceScore += 5;
            }
        }
        
        if (relevanceScore > 0) {
            results.push({ ...entry, relevanceScore });
        }
    });
    
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
}

function buildContextFromResults(results, queryAnalysis) {
    const topResults = results.slice(0, 3);
    
    let context = '\n\n=== CONTEXTE MÉMOIRE THERMIQUE ===\n';
    context += `Recherche: "${queryAnalysis.originalQuery}"\n`;
    context += `Fichiers: ${results.length}, Utilisés: ${topResults.length}\n\n`;
    
    topResults.forEach((result, i) => {
        context += `FICHIER ${i+1} [Score: ${result.relevanceScore}]:\n`;
        context += `👤 "${result.user}"\n`;
        context += `🤖 "${result.agent}"\n\n`;
    });
    
    context += 'INSTRUCTION: Utilise ce contexte pour répondre.\n\n';
    return context;
}

// Appel RÉEL au serveur DeepSeek R1 8B
function callRealAgent(prompt, timeout = 30000) {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({
            prompt: prompt,
            n_predict: 100,
            temperature: 0.7,
            stream: false
        });
        
        const options = {
            hostname: 'localhost',
            port: 8000,
            path: '/completion',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(data)
            },
            timeout: timeout
        };
        
        console.log(`📤 Envoi au serveur: "${prompt.substring(0, 50)}..."`);
        
        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(responseData);
                    const reply = (parsed.content || parsed.text || '').trim();
                    console.log(`📥 Réponse reçue: "${reply.substring(0, 50)}..."`);
                    resolve(reply);
                } catch (error) {
                    reject(new Error(`Erreur parsing: ${error.message}`));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error(`Erreur requête: ${error.message}`));
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Timeout serveur'));
        });
        
        req.write(data);
        req.end();
    });
}

// Test de connexion
async function testConnection() {
    return new Promise((resolve, reject) => {
        const req = http.request({
            hostname: 'localhost',
            port: 8000,
            path: '/health',
            method: 'GET',
            timeout: 5000
        }, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(data);
                    resolve(parsed.status === 'ok');
                } catch (error) {
                    reject(error);
                }
            });
        });
        
        req.on('error', reject);
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Timeout connexion'));
        });
        
        req.end();
    });
}

// TEST PRINCIPAL
async function runRealMemoryTest() {
    try {
        // Test 1: Connexion
        console.log('🔧 Test connexion serveur...');
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Serveur inaccessible');
        }
        console.log('✅ Serveur DeepSeek R1 8B accessible');
        
        // Test 2: Première interaction (présentation)
        console.log('\n📝 ÉTAPE 1: Présentation');
        const message1 = 'Bonjour, je m\'appelle Jean-Luc';
        const response1 = await callRealAgent(message1);
        
        if (!response1) {
            throw new Error('Réponse vide du serveur');
        }
        
        saveThermalMemory(message1, response1);
        console.log(`👤 "${message1}"`);
        console.log(`🤖 "${response1}"`);
        
        // Attendre un peu
        console.log('\n⏳ Attente 3 secondes...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test 3: Question avec mémoire
        console.log('\n📝 ÉTAPE 2: Test rappel mémoire');
        const message2 = 'Tu te souviens de mon nom ?';
        const memoryContext = searchMemory(message2);
        
        if (memoryContext) {
            console.log('🧠 CONTEXTE TROUVÉ !');
            console.log('📋 Contexte:', memoryContext.substring(0, 200) + '...');
        } else {
            console.log('❌ AUCUN CONTEXTE TROUVÉ');
        }
        
        const promptWithMemory = message2 + memoryContext;
        const response2 = await callRealAgent(promptWithMemory);
        
        if (!response2) {
            throw new Error('Réponse vide du serveur');
        }
        
        saveThermalMemory(message2, response2);
        console.log(`👤 "${message2}"`);
        console.log(`🤖 "${response2}"`);
        
        // Test 4: Vérification finale
        console.log('\n🔍 VÉRIFICATION FINALE');
        const remembersName = response2.toLowerCase().includes('jean-luc') || 
                             response2.toLowerCase().includes('jean luc');
        
        console.log('=' .repeat(60));
        if (remembersName) {
            console.log('🎉 SUCCÈS ! L\'agent se souvient du nom "Jean-Luc"');
            console.log('✅ La mémoire thermique fonctionne parfaitement !');
        } else {
            console.log('❌ ÉCHEC ! L\'agent ne se souvient pas du nom');
            console.log('❌ Problème avec la mémoire thermique');
        }
        console.log('=' .repeat(60));
        
        // Sauvegarder les résultats
        const results = {
            timestamp: new Date().toISOString(),
            success: remembersName,
            memory: thermalMemory,
            test1: { message: message1, response: response1 },
            test2: { message: message2, response: response2, context: memoryContext }
        };
        
        fs.writeFileSync('test_results.json', JSON.stringify(results, null, 2));
        console.log('💾 Résultats sauvegardés dans test_results.json');
        
        return remembersName;
        
    } catch (error) {
        console.error('❌ Test échoué:', error.message);
        return false;
    }
}

// Exécution
if (require.main === module) {
    runRealMemoryTest().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { runRealMemoryTest };
