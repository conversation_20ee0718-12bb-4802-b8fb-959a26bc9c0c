// Test RÉEL des fonctions mémoire - Pas de simulation !

console.log('🧪 TEST RÉEL DES FONCTIONS MÉMOIRE');
console.log('=' .repeat(40));

let thermalMemory = [];

// Fonction de sauvegarde RÉELLE
function saveThermalMemory(userMessage, agentResponse) {
    const entry = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        user: userMessage,
        agent: agentResponse,
        keywords: extractKeywords(userMessage + ' ' + agentResponse)
    };
    
    thermalMemory.push(entry);
    console.log(`💾 SAUVEGARDÉ: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`);
    console.log(`📊 Mots-clés: ${entry.keywords.join(', ')}`);
    console.log(`📈 Total: ${thermalMemory.length} entrée(s)`);
    
    return entry;
}

// Fonction de recherche RÉELLE
function searchMemory(query) {
    console.log(`🔍 RECHERCHE: "${query}"`);
    console.log(`🧠 Base: ${thermalMemory.length} entrée(s)`);
    
    if (thermalMemory.length === 0) {
        console.log('❌ Mémoire vide');
        return '';
    }
    
    // Afficher contenu mémoire
    thermalMemory.forEach((entry, i) => {
        console.log(`📋 Entrée ${i+1}: "${entry.user}" → "${entry.agent.substring(0, 30)}..."`);
        console.log(`   Mots-clés: ${entry.keywords.join(', ')}`);
    });
    
    const queryLower = query.toLowerCase();
    const triggers = ['souviens', 'rappelle', 'nom', 'appelle'];
    
    if (triggers.some(trigger => queryLower.includes(trigger))) {
        console.log('🎯 Trigger détecté');
        
        const results = thermalMemory.filter(entry => 
            entry.user.toLowerCase().includes('appelle') ||
            entry.user.toLowerCase().includes('nom') ||
            entry.agent.toLowerCase().includes('jean-luc') ||
            entry.keywords.includes('jean-luc')
        );
        
        console.log(`📁 Résultats: ${results.length}`);
        
        if (results.length > 0) {
            const context = `\n\nCONTEXTE MÉMOIRE:\n${results.map(r => `- "${r.user}" → "${r.agent}"`).join('\n')}\nUtilise ce contexte.\n`;
            console.log(`🧠 Contexte: ${context.length} caractères`);
            console.log('📋 Contexte généré:');
            console.log(context);
            return context;
        }
    } else {
        console.log('⚠️ Aucun trigger');
    }
    
    return '';
}

// Extraction mots-clés RÉELLE
function extractKeywords(text) {
    const textLower = text.toLowerCase();

    // Chercher "jean-luc" spécifiquement
    const keywords = [];

    if (textLower.includes('jean-luc') || (textLower.includes('jean') && textLower.includes('luc'))) {
        keywords.push('jean-luc');
    }

    if (textLower.includes('appelle')) keywords.push('appelle');
    if (textLower.includes('nom')) keywords.push('nom');
    if (textLower.includes('bonjour')) keywords.push('bonjour');
    if (textLower.includes('souviens')) keywords.push('souviens');
    if (textLower.includes('rappelle')) keywords.push('rappelle');

    console.log(`🔤 Texte analysé: "${text}"`);
    console.log(`🎯 Mots-clés trouvés: ${keywords.join(', ')}`);

    return keywords;
}

// TEST PRINCIPAL
function runTest() {
    console.log('🚀 DÉBUT TEST');
    
    // Test 1: Sauvegarde
    console.log('\n📝 TEST 1: Sauvegarde');
    const entry = saveThermalMemory(
        'Bonjour, je m\'appelle Jean-Luc', 
        'Bonjour Jean-Luc ! Ravi de vous rencontrer.'
    );
    
    // Vérification sauvegarde
    if (thermalMemory.length === 1) {
        console.log('✅ Sauvegarde OK');
    } else {
        console.log('❌ Sauvegarde ÉCHEC');
        return false;
    }
    
    if (entry.keywords.includes('jean-luc')) {
        console.log('✅ Mots-clés OK');
    } else {
        console.log('❌ Mots-clés ÉCHEC');
        return false;
    }
    
    // Test 2: Recherche
    console.log('\n📝 TEST 2: Recherche');
    const context = searchMemory('Tu te souviens de mon nom ?');
    
    // Vérification recherche
    if (context && context.length > 0) {
        console.log('✅ Contexte généré');
    } else {
        console.log('❌ Aucun contexte');
        return false;
    }
    
    if (context.includes('Jean-Luc')) {
        console.log('✅ "Jean-Luc" trouvé dans contexte');
    } else {
        console.log('❌ "Jean-Luc" ABSENT du contexte');
        return false;
    }
    
    // Test 3: Triggers
    console.log('\n📝 TEST 3: Triggers');
    const triggers = ['Tu te souviens', 'Rappelle-toi', 'Mon nom', 'Comment je m\'appelle'];
    let triggerOK = 0;
    
    triggers.forEach(trigger => {
        const result = searchMemory(trigger);
        if (result.includes('Jean-Luc')) {
            console.log(`✅ "${trigger}" → Trouvé`);
            triggerOK++;
        } else {
            console.log(`❌ "${trigger}" → Pas trouvé`);
        }
    });
    
    console.log(`📊 Triggers OK: ${triggerOK}/${triggers.length}`);
    
    // RÉSULTAT FINAL
    console.log('\n' + '=' .repeat(40));
    if (triggerOK === triggers.length) {
        console.log('🎉 TOUTES LES FONCTIONS MÉMOIRE MARCHENT !');
        console.log('✅ MÉMOIRE THERMIQUE 100% FONCTIONNELLE');
        return true;
    } else {
        console.log('❌ PROBLÈMES DÉTECTÉS');
        return false;
    }
}

// Exécution
const success = runTest();
console.log(`\n🏁 RÉSULTAT: ${success ? 'SUCCÈS' : 'ÉCHEC'}`);

if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runTest, saveThermalMemory, searchMemory };
}
