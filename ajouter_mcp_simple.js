// 🔌 SCRIPT SIMPLE POUR AJOUTER MCP À TON INTERFACE EXISTANTE
// Ce script s'injecte dans ton interface llama.cpp sans la modifier

console.log('🔌 INJECTION MCP DANS INTERFACE EXISTANTE');

// Attendre que la page soit chargée
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMCP);
} else {
    initMCP();
}

function initMCP() {
    console.log('🚀 Initialisation MCP pour interface llama.cpp');
    
    // Créer un bouton MCP simple
    const mcpButton = document.createElement('button');
    mcpButton.innerHTML = '🔌 MCP';
    mcpButton.style.cssText = `
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        z-index: 9999 !important;
        background: rgba(0,255,0,0.8) !important;
        border: 2px solid #00ff00 !important;
        color: #000 !important;
        padding: 10px 15px !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        font-weight: bold !important;
        font-size: 14px !important;
        box-shadow: 0 0 10px rgba(0,255,0,0.5) !important;
    `;
    
    mcpButton.onclick = function() {
        const message = prompt('🔌 MCP - Que voulez-vous faire ?\n\n1. Actualités 2025\n2. Météo\n3. Recherche web\n\nTapez votre demande:');
        if (message) {
            envoyerAvecMCP(message);
        }
    };
    
    document.body.appendChild(mcpButton);
    
    // Créer zone de status
    const statusDiv = document.createElement('div');
    statusDiv.id = 'mcp-status';
    statusDiv.style.cssText = `
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        z-index: 9999 !important;
        background: rgba(0,0,0,0.9) !important;
        border: 1px solid #00ffff !important;
        color: #00ffff !important;
        padding: 10px !important;
        border-radius: 8px !important;
        font-family: monospace !important;
        font-size: 12px !important;
        max-width: 300px !important;
        display: none !important;
    `;
    
    document.body.appendChild(statusDiv);
    
    console.log('✅ MCP injecté dans ton interface');
}

// Fonction pour envoyer avec MCP
async function envoyerAvecMCP(message) {
    const status = document.getElementById('mcp-status');
    status.style.display = 'block';
    status.innerHTML = '🔌 Traitement MCP...';
    
    try {
        let donneesMCP = '';
        
        // Détecter le type de demande
        if (message.toLowerCase().includes('actualité') || message.toLowerCase().includes('news')) {
            status.innerHTML = '📰 Récupération actualités...';
            const response = await fetch('http://127.0.0.1:8086/mcp/news');
            const data = await response.json();
            donneesMCP = `📰 ACTUALITÉS 2025:\n${data.actualites.map(a => `• ${a.titre}`).join('\n')}`;

        } else if (message.toLowerCase().includes('météo') || message.toLowerCase().includes('temps')) {
            status.innerHTML = '🌤️ Récupération météo...';
            const ville = extraireVille(message);
            const response = await fetch(`http://127.0.0.1:8086/mcp/weather?city=${ville}`);
            const data = await response.json();
            donneesMCP = `🌤️ MÉTÉO ${data.ville}:\n🌡️ ${data.donnees.temperature}°C\n☁️ ${data.donnees.conditions}`;

        } else if (message.toLowerCase().includes('recherche') || message.toLowerCase().includes('cherche')) {
            status.innerHTML = '🌐 Recherche web...';
            const query = message.replace(/recherche|cherche/gi, '').trim();
            const response = await fetch(`http://127.0.0.1:8086/mcp/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            donneesMCP = `🌐 RECHERCHE "${query}":\n${data.resultats.map(r => `• ${r.titre}`).join('\n')}`;
        }

        // Construire le prompt complet
        const promptComplet = `🔌 DONNÉES MCP RÉCUPÉRÉES:\n${donneesMCP}\n\nUtilise ces informations pour répondre à: ${message}`;
        
        // Envoyer à ton interface
        envoyerVersInterface(promptComplet);
        
        status.innerHTML = '✅ MCP envoyé vers JARVIS';
        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
        
    } catch (error) {
        status.innerHTML = `❌ Erreur MCP: ${error.message}`;
        setTimeout(() => {
            status.style.display = 'none';
        }, 5000);
    }
}

// Fonction pour envoyer vers ton interface
function envoyerVersInterface(message) {
    // Chercher la zone de saisie dans ton interface
    const selectors = [
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]',
        '.chat-input',
        '.message-input'
    ];
    
    let input = null;
    for (const selector of selectors) {
        input = document.querySelector(selector);
        if (input && input.offsetParent !== null) break; // Visible
    }
    
    if (input) {
        // Insérer le message
        if (input.tagName === 'TEXTAREA' || input.tagName === 'INPUT') {
            input.value = message;
            input.focus();
            
            // Déclencher les événements
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
        } else {
            input.textContent = message;
            input.focus();
        }
        
        console.log('✅ Message inséré dans ton interface');
        
        // Essayer de trouver et cliquer le bouton d'envoi
        setTimeout(() => {
            const sendButtons = [
                'button[type="submit"]',
                'button:contains("Send")',
                'button:contains("Envoyer")',
                '.send-button',
                '.submit-button'
            ];
            
            for (const selector of sendButtons) {
                const btn = document.querySelector(selector);
                if (btn && btn.offsetParent !== null) {
                    btn.click();
                    console.log('✅ Bouton envoi cliqué');
                    return;
                }
            }
            
            // Si pas de bouton trouvé, essayer Enter
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true
            });
            input.dispatchEvent(enterEvent);
            console.log('✅ Enter envoyé');
            
        }, 500);
        
    } else {
        console.log('❌ Zone de saisie non trouvée');
        alert('❌ Impossible de trouver la zone de saisie dans ton interface');
    }
}

// Fonction utilitaire
function extraireVille(message) {
    const mots = message.split(' ');
    for (let i = 0; i < mots.length; i++) {
        if (['météo', 'temps'].includes(mots[i].toLowerCase()) && mots[i + 1]) {
            return mots[i + 1];
        }
    }
    return 'Paris';
}

console.log('🔌 Script MCP chargé et prêt');
