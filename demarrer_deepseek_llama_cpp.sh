#!/bin/bash

# 🤖 DÉMARRAGE DEEPSEEK R1 8B VIA LLAMA.CPP
# Script pour lancer le modèle DeepSeek R1 avec llama.cpp

echo "🚀 DÉMARRAGE DEEPSEEK R1 8B VIA LLAMA.CPP"
echo "========================================"

# Chemin vers le modèle GGUF
MODEL_PATH="/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf"

# Vérifier si llama-server est disponible
if ! command -v llama-server &> /dev/null; then
    echo "❌ llama-server n'est pas installé ou pas dans le PATH !"
    echo "💡 Installation requise:"
    echo "   Téléchargez llama.cpp depuis: https://github.com/ggerganov/llama.cpp"
    echo "   Ou installez via Homebrew: brew install llama.cpp"
    exit 1
fi

echo "✅ llama-server trouvé: $(which llama-server)"

# Vérifier si le modèle existe
if [ ! -f "$MODEL_PATH" ]; then
    echo "❌ Modèle non trouvé: $MODEL_PATH"
    echo "💡 Vérifiez le chemin du modèle DeepSeek R1 8B GGUF"
    exit 1
fi

echo "✅ Modèle trouvé: $MODEL_PATH"

# Vérifier si le port 8000 est libre
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ Port 8000 déjà utilisé"
    echo "🛑 Arrêt du processus existant..."
    pkill -f "llama-server.*8000" || echo "Aucun processus llama-server trouvé"
    sleep 2
fi

echo "🧠 Démarrage du modèle DeepSeek R1 8B..."
echo "📡 Port: 8000"
echo "🔗 URL: http://localhost:8000"
echo "📁 Modèle: $(basename "$MODEL_PATH")"
echo ""
echo "⏳ Chargement du modèle (peut prendre quelques minutes)..."
echo ""

# Démarrer llama-server avec DeepSeek R1
llama-server \
    --model "$MODEL_PATH" \
    --host 0.0.0.0 \
    --port 8000 \
    --ctx-size 4096 \
    --threads 8 \
    --n-gpu-layers 0 \
    --verbose

echo ""
echo "🛑 llama-server arrêté"
