<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦙 llama.cpp - chat v2</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: #0f0f0f;
            color: #e0e0e0;
            height: 100vh;
            display: flex;
        }

        /* 📱 BARRE LATÉRALE */
        .sidebar {
            width: 280px;
            background: #1a1a1a;
            border-right: 1px solid #333;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #333;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 10px;
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section h3 {
            color: #00ff88;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-button {
            width: 100%;
            padding: 12px;
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 6px;
            color: #e0e0e0;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .sidebar-button:hover {
            background: #333;
            border-color: #00ff88;
        }

        /* 🎯 ZONE PRINCIPALE - PRÉSERVATION DE L'AGENT */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0f0f0f;
        }

        /* Interface llama.cpp d'origine préservée */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .chat-title {
            font-size: 24px;
            color: #e0e0e0;
            margin-bottom: 10px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            min-height: 400px;
        }

        .welcome-message {
            text-align: center;
            color: #888;
            font-size: 16px;
            margin-top: 100px;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 50px;
            padding: 15px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            color: #e0e0e0;
            resize: vertical;
            font-family: inherit;
        }

        .chat-input:focus {
            outline: none;
            border-color: #00ff88;
        }

        .send-button {
            padding: 15px 20px;
            background: #00ff88;
            color: #000;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s ease;
        }

        .send-button:hover {
            background: #00cc6a;
        }

        .send-button:disabled {
            background: #333;
            color: #666;
            cursor: not-allowed;
        }

        /* 🧠 FENÊTRE DE RAISONNEMENT */
        .reasoning-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 80vh;
            background: #1a1a1a;
            border: 2px solid #00ff88;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
            z-index: 2000;
            display: none;
            flex-direction: column;
        }

        .reasoning-header {
            padding: 15px;
            background: #2a2a2a;
            border-bottom: 1px solid #00ff88;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .reasoning-title {
            color: #00ff88;
            font-weight: bold;
            flex: 1;
        }

        .reasoning-close {
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 14px;
        }

        .reasoning-content {
            padding: 15px;
            overflow-y: auto;
            flex: 1;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #e0e0e0;
        }

        .reasoning-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #00ff88;
            color: #000;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            z-index: 1999;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        /* 📱 Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(280px);
            }

            .reasoning-panel {
                width: 90%;
                right: 5%;
                top: 80px;
            }
        }
    </style>
</head>
<body>
    <!-- 📱 BARRE LATÉRALE -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">🤖 JARVIS v2</div>
            <div style="font-size: 12px; color: #888;">DeepSeek R1 8B Agent</div>
        </div>
        
        <div class="sidebar-content">
            <div class="sidebar-section">
                <h3>💬 Conversations</h3>
                <button class="sidebar-button">+ Nouvelle conversation</button>
                <button class="sidebar-button">Conversation 1</button>
                <button class="sidebar-button">Conversation 2</button>
            </div>
            
            <div class="sidebar-section">
                <h3>🧠 Mémoire Thermique</h3>
                <button class="sidebar-button">Activer mémoire</button>
                <button class="sidebar-button">Historique</button>
            </div>
            
            <div class="sidebar-section">
                <h3>🔧 Outils</h3>
                <button class="sidebar-button">Mode MCP</button>
                <button class="sidebar-button">Internet</button>
            </div>
            
            <div class="sidebar-section">
                <h3>⚙️ Paramètres</h3>
                <button class="sidebar-button">Configuration</button>
                <button class="sidebar-button">Thème</button>
            </div>
        </div>
    </div>

    <!-- 🧠 BOUTON RAISONNEMENT -->
    <button class="reasoning-toggle" id="reasoningToggle" title="Voir le raisonnement">🧠</button>

    <!-- 🧠 FENÊTRE DE RAISONNEMENT -->
    <div class="reasoning-panel" id="reasoningPanel">
        <div class="reasoning-header">
            <div class="reasoning-title">🧠 Raisonnement DeepSeek R1 8B</div>
            <button class="reasoning-close" id="reasoningClose">×</button>
        </div>
        <div class="reasoning-content" id="reasoningContent">
            <div style="color: #888; text-align: center; padding: 20px;">
                Envoyez un message pour voir le raisonnement de l'agent...
            </div>
        </div>
    </div>

    <!-- 🎯 ZONE PRINCIPALE - AGENT PRÉSERVÉ -->
    <div class="main-content">
        <div class="chat-container">
            <div class="chat-header">
                <h1 class="chat-title">🦙 llama.cpp</h1>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    DeepSeek R1 8B Agent
                </div>
            </div>
            
            <div class="chat-input-container">
                <textarea 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="Tapez votre message ici..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton">Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        // 🎯 PRÉSERVATION DE L'AGENT PRINCIPAL
        console.log('🎯 Interface v2 - Agent principal préservé');
        
        // Variables globales
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        
        // 📱 Gestion responsive sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }
        
        // 🚀 ACCÉLÉRATEURS DE PERFORMANCE
        let conversationContext = '';
        let promptCache = new Map();

        // 🎯 FONCTIONS AGENT PRINCIPAL - CONNECTÉ À LLAMA.CPP AVEC ACCÉLÉRATEURS
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // Afficher le message utilisateur
            addMessage('user', message);
            chatInput.value = '';
            sendButton.disabled = true;
            sendButton.textContent = 'Traitement...';

            // 🤔 ANIMATION DE RÉFLEXION
            const thinkingMessage = addThinkingMessage();

            try {
                // 🚀 ACCÉLÉRATEUR 1: Prompt optimisé et contexte
                const optimizedPrompt = buildOptimizedPrompt(message);

                console.log('🚀 Envoi accéléré vers llama.cpp:', optimizedPrompt);

                // 🧠 ADAPTER LES PARAMÈTRES SELON L'ANALYSE
                const analysis = window.lastAnalysis || { type: 'medium', tokens: 300 };

                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                    },
                    body: JSON.stringify({
                        prompt: optimizedPrompt,
                        n_predict: analysis.tokens,
                        temperature: analysis.type === 'short' ? 0.3 : 0.7,
                        top_p: analysis.type === 'short' ? 0.8 : 0.9,
                        top_k: analysis.type === 'short' ? 20 : 40,
                        repeat_penalty: 1.1,
                        stream: false,
                        stop: analysis.type === 'short' ? ["\n\n", "."] : []
                    })
                });

                console.log('📡 Statut réponse:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Erreur API ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('📥 Données reçues:', data);

                // 🧠 CAPTURER LE RAISONNEMENT
                captureReasoning(message, data, optimizedPrompt);

                // Supprimer l'animation de réflexion
                removeThinkingMessage(thinkingMessage);

                // 🧹 NETTOYER LA RÉPONSE
                let assistantMessage = data.content || data.text || 'Réponse vide de DeepSeek R1 8B';
                assistantMessage = cleanDeepSeekResponse(assistantMessage);

                addMessage('assistant', assistantMessage);
                console.log('✅ Réponse affichée:', assistantMessage);

            } catch (error) {
                console.error('❌ Erreur complète:', error);
                removeThinkingMessage(thinkingMessage);
                addMessage('assistant', `❌ Erreur: ${error.message}`);
            } finally {
                sendButton.disabled = false;
                sendButton.textContent = 'Envoyer';
            }
        }

        // 🤔 Animation de réflexion AMÉLIORÉE
        function addThinkingMessage() {
            const thinkingDiv = document.createElement('div');
            thinkingDiv.className = 'thinking-message';
            thinkingDiv.style.cssText = `
                margin: 10px 0;
                padding: 15px;
                border-radius: 8px;
                background: linear-gradient(45deg, #1a3a1a, #2a4a2a);
                border-left: 4px solid #00ff88;
                border: 1px solid #00ff88;
                box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
                animation: pulse 1.5s infinite;
            `;

            // Ajouter l'animation CSS
            if (!document.querySelector('#thinking-animation-style')) {
                const style = document.createElement('style');
                style.id = 'thinking-animation-style';
                style.textContent = `
                    @keyframes pulse {
                        0% { box-shadow: 0 0 5px rgba(0, 255, 136, 0.3); }
                        50% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
                        100% { box-shadow: 0 0 5px rgba(0, 255, 136, 0.3); }
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    .spinner {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        border: 2px solid #333;
                        border-top: 2px solid #00ff88;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin-right: 8px;
                    }
                `;
                document.head.appendChild(style);
            }

            thinkingDiv.innerHTML = `
                <strong>🤖 DeepSeek R1 8B:</strong>
                <span class="spinner"></span>
                <span class="thinking-text">Génération de la réponse</span>
                <span class="dots">...</span>
            `;

            chatMessages.appendChild(thinkingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Animation des points et du texte
            let dotCount = 0;
            let textIndex = 0;
            const texts = ['Génération de la réponse', 'Analyse en cours', 'Traitement', 'Réflexion'];

            const interval = setInterval(() => {
                const dotsSpan = thinkingDiv.querySelector('.dots');
                const textSpan = thinkingDiv.querySelector('.thinking-text');

                if (dotsSpan && textSpan) {
                    // Animation des points
                    dotCount = (dotCount + 1) % 4;
                    dotsSpan.textContent = '.'.repeat(dotCount + 1);

                    // Changement de texte toutes les 2 secondes
                    if (dotCount === 0) {
                        textIndex = (textIndex + 1) % texts.length;
                        textSpan.textContent = texts[textIndex];
                    }
                }
            }, 300);

            thinkingDiv.interval = interval;
            return thinkingDiv;
        }

        function removeThinkingMessage(thinkingDiv) {
            if (thinkingDiv && thinkingDiv.interval) {
                clearInterval(thinkingDiv.interval);
                thinkingDiv.remove();
            }
        }

        // 🧠 ANALYSEUR INTELLIGENT DE QUESTIONS
        function analyzeQuestionType(message) {
            const lowerMessage = message.toLowerCase();

            // Mots-clés pour réponses COURTES
            const shortKeywords = [
                'quelle est', 'quel est', 'qui est', 'où est', 'quand est',
                'combien', 'capitale', 'âge', 'date', 'nom', 'prix',
                'rapidement', 'bref', 'court', 'simple', 'juste'
            ];

            // Mots-clés pour réponses DÉTAILLÉES
            const detailKeywords = [
                'explique', 'détaille', 'décris', 'raconte', 'comment',
                'pourquoi', 'analyse', 'développe', 'approfondi',
                'complet', 'détaillé', 'maximum', 'tout', 'entièrement'
            ];

            // Mots-clés pour réponses MOYENNES
            const mediumKeywords = [
                'présente', 'montre', 'donne', 'liste', 'résume'
            ];

            // Compter les occurrences
            let shortScore = 0;
            let detailScore = 0;
            let mediumScore = 0;

            shortKeywords.forEach(keyword => {
                if (lowerMessage.includes(keyword)) shortScore++;
            });

            detailKeywords.forEach(keyword => {
                if (lowerMessage.includes(keyword)) detailScore++;
            });

            mediumKeywords.forEach(keyword => {
                if (lowerMessage.includes(keyword)) mediumScore++;
            });

            // Déterminer le type de réponse
            if (detailScore > 0) {
                return { type: 'detailed', tokens: 1000, description: 'Réponse détaillée demandée' };
            } else if (shortScore > 0 || (lowerMessage.length < 30 && !mediumScore)) {
                return { type: 'short', tokens: 100, description: 'Réponse courte suffisante' };
            } else {
                return { type: 'medium', tokens: 300, description: 'Réponse équilibrée' };
            }
        }

        // 🚀 ACCÉLÉRATEURS DE PERFORMANCE RÉELS
        function buildOptimizedPrompt(message) {
            const analysis = analyzeQuestionType(message);

            // Prompt structuré comme ChatGPT avec français forcé
            let optimizedPrompt;

            if (analysis.type === 'short') {
                optimizedPrompt = `[INSTRUCTION] Tu es un assistant IA qui répond UNIQUEMENT en français. Donne une réponse courte, précise et directe.

Question: ${message}

Réponse:`;
            } else if (analysis.type === 'detailed') {
                optimizedPrompt = `[INSTRUCTION] Tu es un assistant IA qui répond UNIQUEMENT en français. Structure ta réponse avec des titres, sous-titres, et points clés pour une lecture claire.

Question: ${message}

Réponse structurée:`;
            } else {
                optimizedPrompt = `[INSTRUCTION] Tu es un assistant IA qui répond UNIQUEMENT en français. Organise ta réponse de manière claire et structurée.

Question: ${message}

Réponse:`;
            }

            // Accélérateur: Cache du contexte
            if (conversationContext.length > 500) {
                conversationContext = conversationContext.slice(-300);
            }

            // Stocker l'analyse pour l'affichage
            window.lastAnalysis = analysis;

            return optimizedPrompt;
        }

        // 🚀 ACCÉLÉRATEUR: Préchargement du modèle
        async function warmupModel() {
            try {
                await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: "Test",
                        n_predict: 1,
                        temperature: 0.1
                    })
                });
                console.log('🚀 Modèle préchauffé');
            } catch (e) {
                console.log('⚠️ Préchauffage échoué:', e.message);
            }
        }

        // 🚀 ACCÉLÉRATEUR: Optimisation automatique des paramètres
        function getOptimizedParams(messageLength) {
            if (messageLength < 10) {
                return { n_predict: 50, temperature: 0.01 }; // Ultra rapide pour questions courtes
            } else if (messageLength < 50) {
                return { n_predict: 80, temperature: 0.05 }; // Rapide
            } else {
                return { n_predict: 120, temperature: 0.1 }; // Standard
            }
        }

        // 🧹 FORMATAGE INTELLIGENT DES RÉPONSES (STYLE CHATGPT)
        function cleanDeepSeekResponse(response) {
            if (!response) return 'Réponse vide';

            let cleaned = response.trim();
            let thinking = null;

            // Chercher format RÉFLEXION/RÉPONSE
            const reflectionMatch = cleaned.match(/RÉFLEXION:\s*(.*?)\s*RÉPONSE:\s*(.*)/s);
            if (reflectionMatch) {
                thinking = reflectionMatch[1].trim();
                cleaned = reflectionMatch[2].trim();
                displayThinking(thinking);
            }
            // Chercher balises <think>
            else {
                const thinkMatch = cleaned.match(/<think>(.*?)<\/think>/s);
                if (thinkMatch) {
                    thinking = thinkMatch[1].trim();
                    cleaned = cleaned.replace(/<think>.*?<\/think>/s, '').trim();
                    displayThinking(thinking);
                }
            }

            // Nettoyer les espaces en début
            cleaned = cleaned.replace(/^[\s\n\r]+/, '');

            // Si la réponse est vide après nettoyage
            if (!cleaned || cleaned.length < 2) {
                return thinking ? 'Réflexion capturée, mais réponse vide' : 'Réponse générée mais vide';
            }

            // 🎨 FORMATAGE STYLE CHATGPT
            cleaned = formatChatGPTStyle(cleaned);

            return cleaned;
        }

        // 🎨 FORMATAGE STYLE CHATGPT
        function formatChatGPTStyle(text) {
            // Convertir les patterns en HTML structuré
            let formatted = text;

            // Titres principaux (## ou **Titre**)
            formatted = formatted.replace(/^##\s*(.+)$/gm, '<h3 style="color: #00ff88; font-weight: bold; margin: 15px 0 10px 0; border-bottom: 2px solid #00ff88; padding-bottom: 5px;">$1</h3>');
            formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong style="color: #00ff88;">$1</strong>');

            // Listes à puces
            formatted = formatted.replace(/^[-•]\s*(.+)$/gm, '<div style="margin: 5px 0; padding-left: 20px;"><span style="color: #00ff88;">•</span> $1</div>');

            // Numérotation
            formatted = formatted.replace(/^(\d+)\.\s*(.+)$/gm, '<div style="margin: 5px 0; padding-left: 20px;"><span style="color: #ffaa00; font-weight: bold;">$1.</span> $2</div>');

            // Paragraphes
            formatted = formatted.replace(/\n\n/g, '</p><p style="margin: 10px 0; line-height: 1.6;">');
            formatted = '<p style="margin: 10px 0; line-height: 1.6;">' + formatted + '</p>';

            // Mots importants en italique
            formatted = formatted.replace(/\*([^*]+)\*/g, '<em style="color: #ffcc88;">$1</em>');

            return formatted;
        }

        // 🧠 AFFICHER LES PENSÉES
        function displayThinking(thinking) {
            const reasoningContent = document.getElementById('reasoningContent');
            const timestamp = new Date().toLocaleTimeString();

            const thinkingHTML = `
                <div style="border: 2px solid #00ff88; border-radius: 8px; padding: 15px; margin: 10px 0; background: #0a2a0a;">
                    <div style="color: #00ff88; font-weight: bold; margin-bottom: 10px;">
                        🧠 [${timestamp}] Pensées de l'agent :
                    </div>
                    <div style="color: #e0e0e0; line-height: 1.6; white-space: pre-wrap;">
                        ${thinking}
                    </div>
                </div>
            `;

            reasoningContent.innerHTML = thinkingHTML + reasoningContent.innerHTML;

            // Ouvrir automatiquement la fenêtre si elle est fermée
            const reasoningPanel = document.getElementById('reasoningPanel');
            if (reasoningPanel.style.display !== 'flex') {
                reasoningPanel.style.display = 'flex';
            }
        }
        
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');

            // Styles selon le rôle (style ChatGPT amélioré)
            let bgColor, borderColor, roleLabel, emoji;
            switch(role) {
                case 'user':
                    bgColor = '#1a3a1a';
                    borderColor = '#88ff88';
                    roleLabel = 'Vous';
                    emoji = '👤';
                    break;
                case 'assistant':
                    bgColor = '#0a2a0a';
                    borderColor = '#00ff88';
                    roleLabel = 'DeepSeek R1 8B';
                    emoji = '🤖';
                    break;
                case 'system':
                    bgColor = '#2a1a0a';
                    borderColor = '#ffaa00';
                    roleLabel = 'Système';
                    emoji = '⚙️';
                    break;
                default:
                    bgColor = '#1a1a1a';
                    borderColor = '#666';
                    roleLabel = 'Agent';
                    emoji = '🔧';
            }

            messageDiv.style.cssText = `
                margin: 15px 0;
                padding: 0;
                border-radius: 12px;
                background: ${bgColor};
                border: 1px solid ${borderColor};
                position: relative;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            `;

            // Contenu formaté style ChatGPT
            messageDiv.innerHTML = `
                <div style="padding: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span style="font-size: 16px; margin-right: 8px;">${emoji}</span>
                        <strong style="color: ${borderColor}; font-size: 14px;">${roleLabel}</strong>
                        ${role === 'assistant' ? '<button onclick="this.closest(\'.message\').remove()" style="position: absolute; top: 10px; right: 10px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 14px;">×</button>' : ''}
                    </div>
                    <div style="color: #e0e0e0; line-height: 1.6; font-size: 14px;">
                        ${content}
                    </div>
                </div>
            `;

            // Supprimer le message de bienvenue
            const welcomeMsg = chatMessages.querySelector('.welcome-message');
            if (welcomeMsg) welcomeMsg.remove();

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 🎯 ÉVÉNEMENTS
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 📱 FONCTIONNALITÉS BARRE LATÉRALE
        document.querySelectorAll('.sidebar-button').forEach(button => {
            button.addEventListener('click', () => {
                const buttonText = button.textContent.trim();
                console.log('🔘 Bouton sidebar:', buttonText);

                switch(buttonText) {
                    case '+ Nouvelle conversation':
                        nouvelleConversation();
                        break;
                    case 'Activer mémoire':
                        activerMemoireThermique();
                        break;
                    case 'Mode MCP':
                        activerModeMCP();
                        break;
                    case 'Internet':
                        activerInternet();
                        break;
                    case 'Configuration':
                        ouvrirConfiguration();
                        break;
                    default:
                        console.log('🔘 Fonction à implémenter:', buttonText);
                }
            });
        });

        // 🔄 Nouvelle conversation
        function nouvelleConversation() {
            chatMessages.innerHTML = '<div class="welcome-message">DeepSeek R1 8B Agent</div>';
            console.log('🔄 Nouvelle conversation démarrée');
        }

        // 🧠 Mémoire thermique
        function activerMemoireThermique() {
            addMessage('system', '🧠 Mémoire thermique activée - Historique des conversations chargé');
            console.log('🧠 Mémoire thermique activée');
        }

        // 🔌 Mode MCP
        function activerModeMCP() {
            addMessage('system', '🔌 Mode MCP activé - Accès aux outils externes disponible');
            console.log('🔌 Mode MCP activé');
        }

        // 🌐 Internet
        function activerInternet() {
            addMessage('system', '🌐 Accès Internet activé - Recherche web disponible');
            console.log('🌐 Internet activé');
        }

        // ⚙️ Configuration
        function ouvrirConfiguration() {
            addMessage('system', '⚙️ Panneau de configuration - Paramètres du modèle DeepSeek R1 8B');
            console.log('⚙️ Configuration ouverte');
        }
        
        console.log('✅ Interface v2 initialisée - Agent principal préservé');

        // 🧠 MÉMOIRE THERMIQUE ILLIMITÉE
        let thermalMemory = [];
        let memoryIndex = 0;

        // Sauvegarde continue en arrière-plan
        function saveThermalMemory(userMessage, agentResponse, reasoning) {
            const memoryEntry = {
                id: ++memoryIndex,
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse,
                reasoning: reasoning || null,
                context: conversationContext.slice(-5) // Derniers 5 échanges
            };

            thermalMemory.push(memoryEntry);

            // Sauvegarde locale illimitée
            localStorage.setItem('jarvis_thermal_memory', JSON.stringify(thermalMemory));
            console.log('🧠 Mémoire thermique sauvegardée:', memoryEntry);
        }

        // Chargement de la mémoire au démarrage
        function loadThermalMemory() {
            const saved = localStorage.getItem('jarvis_thermal_memory');
            if (saved) {
                thermalMemory = JSON.parse(saved);
                memoryIndex = thermalMemory.length;
                console.log(`🧠 Mémoire thermique chargée: ${thermalMemory.length} entrées`);
            }
        }

        // 🧠 GESTION FENÊTRE DE RAISONNEMENT
        const reasoningToggle = document.getElementById('reasoningToggle');
        const reasoningPanel = document.getElementById('reasoningPanel');
        const reasoningClose = document.getElementById('reasoningClose');
        const reasoningContent = document.getElementById('reasoningContent');

        reasoningToggle.addEventListener('click', () => {
            reasoningPanel.style.display = reasoningPanel.style.display === 'flex' ? 'none' : 'flex';
        });

        reasoningClose.addEventListener('click', () => {
            reasoningPanel.style.display = 'none';
        });

        // 🧠 CAPTURE COMPLÈTE DU PROCESSUS MENTAL
        function captureReasoning(userMessage, apiResponse, sentPrompt) {
            const timestamp = new Date().toLocaleTimeString();

            // TOUT le contenu est le processus mental de l'agent
            const fullContent = apiResponse.content || '';

            // Récupérer l'analyse de la question
            const analysis = window.lastAnalysis || { type: 'unknown', tokens: 0, description: 'Non analysé' };

            // Analyser le type de réflexion
            const hasExplicitThinking = fullContent.includes('je pense') ||
                                      fullContent.includes('réfléchis') ||
                                      fullContent.includes('analysons') ||
                                      fullContent.includes('d\'abord') ||
                                      fullContent.includes('ensuite') ||
                                      fullContent.includes('considérons');

            const reasoningHTML = `
                <div style="border: 2px solid #00ff88; border-radius: 8px; padding: 15px; margin: 10px 0; background: #0a2a0a;">
                    <div style="color: #00ff88; font-weight: bold; margin-bottom: 15px;">
                        🧠 [${timestamp}] PROCESSUS MENTAL COMPLET DE L'AGENT
                    </div>

                    <div style="margin: 15px 0;">
                        <strong style="color: #88ff88;">📤 Votre question:</strong><br>
                        <div style="background: #1a3a1a; padding: 12px; border-radius: 6px; margin: 8px 0; color: #e0e0e0;">
                            ${userMessage}
                        </div>
                    </div>

                    <div style="margin: 15px 0;">
                        <strong style="color: #8888ff;">🔍 Analyse intelligente:</strong><br>
                        <div style="background: #1a1a3a; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 12px; color: #aaccff;">
                            • Type détecté: <span style="color: #00ff88;">${analysis.type.toUpperCase()}</span><br>
                            • Tokens alloués: <span style="color: #ffaa00;">${analysis.tokens}</span><br>
                            • Stratégie: <span style="color: #ffcc88;">${analysis.description}</span>
                        </div>
                    </div>

                    <div style="margin: 15px 0;">
                        <strong style="color: #ffaa00;">🧠 PENSÉES ET GÉNÉRATION DE L'AGENT:</strong><br>
                        <div style="background: #2a1a0a; padding: 12px; border-radius: 6px; margin: 8px 0; color: #ffcc88; line-height: 1.6; white-space: pre-wrap; max-height: 400px; overflow-y: auto; border: 1px solid #ffaa00;">
                            ${fullContent}
                        </div>
                        <div style="color: ${hasExplicitThinking ? '#00ff88' : '#ffaa00'}; font-size: 12px; margin-top: 5px;">
                            ${hasExplicitThinking ? '✅ Réflexion explicite détectée' : 'ℹ️ Génération directe - processus mental implicite'}
                        </div>
                    </div>

                    <div style="margin: 15px 0;">
                        <strong style="color: #ffff88;">📊 Métadonnées de génération:</strong><br>
                        <div style="background: #2a2a1a; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 12px; color: #ffff88;">
                            • Tokens générés: ${apiResponse.tokens_predicted || 'N/A'} / ${analysis.tokens} alloués<br>
                            • Temps total: ${Math.round(apiResponse.timings?.predicted_ms || 0)}ms<br>
                            • Vitesse: ${Math.round(apiResponse.timings?.predicted_per_token_ms || 0)}ms/token<br>
                            • Longueur: ${fullContent.length} caractères<br>
                            • Modèle: ${apiResponse.model || 'DeepSeek R1 8B'}
                        </div>
                    </div>
                </div>
            `;

            reasoningContent.innerHTML = reasoningHTML + reasoningContent.innerHTML;

            // 🧠 SAUVEGARDER EN MÉMOIRE THERMIQUE ILLIMITÉE
            saveThermalMemory(userMessage, fullContent, fullContent);
        }

        // 🧠 INITIALISATION MÉMOIRE THERMIQUE
        loadThermalMemory();

        // 🚀 DÉMARRAGE DES ACCÉLÉRATEURS
        setTimeout(() => {
            warmupModel(); // Préchauffage du modèle après 2 secondes
        }, 2000);
    </script>
</body>
</html>
