<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦙 llama.cpp - chat v2</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: #0f0f0f;
            color: #e0e0e0;
            height: 100vh;
            display: flex;
        }

        /* 📱 BARRE LATÉRALE */
        .sidebar {
            width: 280px;
            background: #1a1a1a;
            border-right: 1px solid #333;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #333;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 10px;
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section h3 {
            color: #00ff88;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-button {
            width: 100%;
            padding: 12px;
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 6px;
            color: #e0e0e0;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .sidebar-button:hover {
            background: #333;
            border-color: #00ff88;
        }

        /* 🎯 ZONE PRINCIPALE - PRÉSERVATION DE L'AGENT */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0f0f0f;
        }

        /* Interface llama.cpp d'origine préservée */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .chat-title {
            font-size: 24px;
            color: #e0e0e0;
            margin-bottom: 10px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            min-height: 400px;
        }

        .welcome-message {
            text-align: center;
            color: #888;
            font-size: 16px;
            margin-top: 100px;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 50px;
            padding: 15px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            color: #e0e0e0;
            resize: vertical;
            font-family: inherit;
        }

        .chat-input:focus {
            outline: none;
            border-color: #00ff88;
        }

        .send-button {
            padding: 15px 20px;
            background: #00ff88;
            color: #000;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s ease;
        }

        .send-button:hover {
            background: #00cc6a;
        }

        .send-button:disabled {
            background: #333;
            color: #666;
            cursor: not-allowed;
        }

        /* 📱 Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
            }
            
            .sidebar.open {
                transform: translateX(280px);
            }
        }
    </style>
</head>
<body>
    <!-- 📱 BARRE LATÉRALE -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">🤖 JARVIS v2</div>
            <div style="font-size: 12px; color: #888;">DeepSeek R1 8B Agent</div>
        </div>
        
        <div class="sidebar-content">
            <div class="sidebar-section">
                <h3>💬 Conversations</h3>
                <button class="sidebar-button">+ Nouvelle conversation</button>
                <button class="sidebar-button">Conversation 1</button>
                <button class="sidebar-button">Conversation 2</button>
            </div>
            
            <div class="sidebar-section">
                <h3>🧠 Mémoire Thermique</h3>
                <button class="sidebar-button">Activer mémoire</button>
                <button class="sidebar-button">Historique</button>
            </div>
            
            <div class="sidebar-section">
                <h3>🔧 Outils</h3>
                <button class="sidebar-button">Mode MCP</button>
                <button class="sidebar-button">Internet</button>
            </div>
            
            <div class="sidebar-section">
                <h3>⚙️ Paramètres</h3>
                <button class="sidebar-button">Configuration</button>
                <button class="sidebar-button">Thème</button>
            </div>
        </div>
    </div>

    <!-- 🎯 ZONE PRINCIPALE - AGENT PRÉSERVÉ -->
    <div class="main-content">
        <div class="chat-container">
            <div class="chat-header">
                <h1 class="chat-title">🦙 llama.cpp</h1>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    DeepSeek R1 8B Agent
                </div>
            </div>
            
            <div class="chat-input-container">
                <textarea 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="Tapez votre message ici..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton">Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        // 🎯 PRÉSERVATION DE L'AGENT PRINCIPAL
        console.log('🎯 Interface v2 - Agent principal préservé');
        
        // Variables globales
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        
        // 📱 Gestion responsive sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }
        
        // 🎯 FONCTIONS AGENT PRINCIPAL - CONNECTÉ À LLAMA.CPP
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // Afficher le message utilisateur
            addMessage('user', message);
            chatInput.value = '';
            sendButton.disabled = true;
            sendButton.textContent = 'Envoi...';

            try {
                // 🔗 CONNEXION À L'API LLAMA.CPP LOCALHOST:8000
                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: message,
                        n_predict: 512,
                        temperature: 0.7,
                        top_p: 0.9,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`Erreur API: ${response.status}`);
                }

                const data = await response.json();
                const assistantMessage = data.content || 'Réponse reçue de DeepSeek R1 8B';

                addMessage('assistant', assistantMessage);
                console.log('✅ Réponse reçue de llama.cpp:', assistantMessage);

            } catch (error) {
                console.error('❌ Erreur connexion llama.cpp:', error);
                addMessage('assistant', `❌ Erreur de connexion: ${error.message}. Vérifiez que llama.cpp tourne sur localhost:8000`);
            } finally {
                sendButton.disabled = false;
                sendButton.textContent = 'Envoyer';
            }
        }
        
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');

            // Styles selon le rôle
            let bgColor, borderColor, roleLabel;
            switch(role) {
                case 'user':
                    bgColor = '#2a2a2a';
                    borderColor = '#00ff88';
                    roleLabel = 'Vous';
                    break;
                case 'assistant':
                    bgColor = '#1a3a1a';
                    borderColor = '#0088ff';
                    roleLabel = 'DeepSeek R1 8B';
                    break;
                case 'system':
                    bgColor = '#3a1a1a';
                    borderColor = '#ff8800';
                    roleLabel = 'Système';
                    break;
                default:
                    bgColor = '#1a1a1a';
                    borderColor = '#666';
                    roleLabel = 'Agent';
            }

            messageDiv.style.cssText = `
                margin: 10px 0;
                padding: 15px;
                border-radius: 8px;
                background: ${bgColor};
                border-left: 4px solid ${borderColor};
            `;
            messageDiv.innerHTML = `<strong>${roleLabel}:</strong> ${content}`;

            // Supprimer le message de bienvenue
            const welcomeMsg = chatMessages.querySelector('.welcome-message');
            if (welcomeMsg) welcomeMsg.remove();

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 🎯 ÉVÉNEMENTS
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 📱 FONCTIONNALITÉS BARRE LATÉRALE
        document.querySelectorAll('.sidebar-button').forEach(button => {
            button.addEventListener('click', () => {
                const buttonText = button.textContent.trim();
                console.log('🔘 Bouton sidebar:', buttonText);

                switch(buttonText) {
                    case '+ Nouvelle conversation':
                        nouvelleConversation();
                        break;
                    case 'Activer mémoire':
                        activerMemoireThermique();
                        break;
                    case 'Mode MCP':
                        activerModeMCP();
                        break;
                    case 'Internet':
                        activerInternet();
                        break;
                    case 'Configuration':
                        ouvrirConfiguration();
                        break;
                    default:
                        console.log('🔘 Fonction à implémenter:', buttonText);
                }
            });
        });

        // 🔄 Nouvelle conversation
        function nouvelleConversation() {
            chatMessages.innerHTML = '<div class="welcome-message">DeepSeek R1 8B Agent</div>';
            console.log('🔄 Nouvelle conversation démarrée');
        }

        // 🧠 Mémoire thermique
        function activerMemoireThermique() {
            addMessage('system', '🧠 Mémoire thermique activée - Historique des conversations chargé');
            console.log('🧠 Mémoire thermique activée');
        }

        // 🔌 Mode MCP
        function activerModeMCP() {
            addMessage('system', '🔌 Mode MCP activé - Accès aux outils externes disponible');
            console.log('🔌 Mode MCP activé');
        }

        // 🌐 Internet
        function activerInternet() {
            addMessage('system', '🌐 Accès Internet activé - Recherche web disponible');
            console.log('🌐 Internet activé');
        }

        // ⚙️ Configuration
        function ouvrirConfiguration() {
            addMessage('system', '⚙️ Panneau de configuration - Paramètres du modèle DeepSeek R1 8B');
            console.log('⚙️ Configuration ouverte');
        }
        
        console.log('✅ Interface v2 initialisée - Agent principal préservé');
    </script>
</body>
</html>
