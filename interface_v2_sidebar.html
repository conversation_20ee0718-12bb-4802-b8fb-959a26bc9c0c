<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦙 llama.cpp - chat v2</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: #0f0f0f;
            color: #e0e0e0;
            height: 100vh;
            display: flex;
        }

        /* 📱 BARRE LATÉRALE */
        .sidebar {
            width: 280px;
            background: #1a1a1a;
            border-right: 1px solid #333;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #333;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 10px;
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section h3 {
            color: #00ff88;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-button {
            width: 100%;
            padding: 12px;
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 6px;
            color: #e0e0e0;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .sidebar-button:hover {
            background: #333;
            border-color: #00ff88;
        }

        /* 🎯 ZONE PRINCIPALE - PRÉSERVATION DE L'AGENT */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0f0f0f;
        }

        /* Interface llama.cpp d'origine préservée */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        .chat-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .chat-title {
            font-size: 24px;
            color: #e0e0e0;
            margin-bottom: 10px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            min-height: 400px;
        }

        .welcome-message {
            text-align: center;
            color: #888;
            font-size: 16px;
            margin-top: 100px;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 50px;
            padding: 15px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            color: #e0e0e0;
            resize: vertical;
            font-family: inherit;
        }

        .chat-input:focus {
            outline: none;
            border-color: #00ff88;
        }

        .send-button {
            padding: 15px 20px;
            background: #00ff88;
            color: #000;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s ease;
        }

        .send-button:hover {
            background: #00cc6a;
        }

        .send-button:disabled {
            background: #333;
            color: #666;
            cursor: not-allowed;
        }

        /* 🧠 FENÊTRE DE RAISONNEMENT */
        .reasoning-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 80vh;
            background: #1a1a1a;
            border: 2px solid #00ff88;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
            z-index: 2000;
            display: none;
            flex-direction: column;
        }

        .reasoning-header {
            padding: 15px;
            background: #2a2a2a;
            border-bottom: 1px solid #00ff88;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .reasoning-title {
            color: #00ff88;
            font-weight: bold;
            flex: 1;
        }

        .reasoning-close {
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 14px;
        }

        .reasoning-content {
            padding: 15px;
            overflow-y: auto;
            flex: 1;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #e0e0e0;
        }

        .reasoning-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #00ff88;
            color: #000;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            z-index: 1999;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        /* 📱 Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(280px);
            }

            .reasoning-panel {
                width: 90%;
                right: 5%;
                top: 80px;
            }
        }
    </style>
</head>
<body>
    <!-- 📱 BARRE LATÉRALE -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">🤖 JARVIS v2</div>
            <div style="font-size: 12px; color: #888;">DeepSeek R1 8B Agent</div>
        </div>
        
        <div class="sidebar-content">
            <div class="sidebar-section">
                <h3>💬 Conversations</h3>
                <button class="sidebar-button">+ Nouvelle conversation</button>
                <button class="sidebar-button">Conversation 1</button>
                <button class="sidebar-button">Conversation 2</button>
            </div>
            
            <div class="sidebar-section">
                <h3>🧠 Mémoire Thermique</h3>
                <button class="sidebar-button">Activer mémoire</button>
                <button class="sidebar-button">Historique</button>
            </div>
            
            <div class="sidebar-section">
                <h3>🔧 Outils</h3>
                <button class="sidebar-button">Mode MCP</button>
                <button class="sidebar-button">Internet</button>
            </div>
            
            <div class="sidebar-section">
                <h3>⚙️ Paramètres</h3>
                <button class="sidebar-button">Configuration</button>
                <button class="sidebar-button">Thème</button>
            </div>
        </div>
    </div>

    <!-- 🧠 BOUTON RAISONNEMENT -->
    <button class="reasoning-toggle" id="reasoningToggle" title="Voir le raisonnement">🧠</button>

    <!-- 🧠 FENÊTRE DE RAISONNEMENT -->
    <div class="reasoning-panel" id="reasoningPanel">
        <div class="reasoning-header">
            <div class="reasoning-title">🧠 Raisonnement DeepSeek R1 8B</div>
            <button class="reasoning-close" id="reasoningClose">×</button>
        </div>
        <div class="reasoning-content" id="reasoningContent">
            <div style="color: #888; text-align: center; padding: 20px;">
                Envoyez un message pour voir le raisonnement de l'agent...
            </div>
        </div>
    </div>

    <!-- 🎯 ZONE PRINCIPALE - AGENT PRÉSERVÉ -->
    <div class="main-content">
        <div class="chat-container">
            <div class="chat-header">
                <h1 class="chat-title">🦙 llama.cpp</h1>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    DeepSeek R1 8B Agent
                </div>
            </div>
            
            <div class="chat-input-container">
                <textarea 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="Tapez votre message ici..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton">Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        // 🎯 PRÉSERVATION DE L'AGENT PRINCIPAL
        console.log('🎯 Interface v2 - Agent principal préservé');
        
        // Variables globales
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        
        // 📱 Gestion responsive sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }
        
        // 🚀 ACCÉLÉRATEURS DE PERFORMANCE
        let conversationContext = '';
        let promptCache = new Map();

        // 🎯 FONCTIONS AGENT PRINCIPAL - CONNECTÉ À LLAMA.CPP AVEC ACCÉLÉRATEURS
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // Afficher le message utilisateur
            addMessage('user', message);
            chatInput.value = '';
            sendButton.disabled = true;
            sendButton.textContent = 'Traitement...';

            // 🤔 ANIMATION DE RÉFLEXION
            const thinkingMessage = addThinkingMessage();

            try {
                // 🚀 ACCÉLÉRATEUR 1: Prompt optimisé et contexte
                const optimizedPrompt = buildOptimizedPrompt(message);

                console.log('🚀 Envoi accéléré vers llama.cpp:', optimizedPrompt);

                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                    },
                    body: JSON.stringify({
                        prompt: optimizedPrompt,
                        n_predict: 100,
                        temperature: 0.7,
                        top_p: 0.9,
                        top_k: 40,
                        repeat_penalty: 1.1,
                        stream: false,
                        stop: ["\n\n", "User:", "Assistant:", "Q:", "A:"]
                    })
                });

                console.log('📡 Statut réponse:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Erreur API ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('📥 Données reçues:', data);

                // 🧠 CAPTURER LE RAISONNEMENT
                captureReasoning(message, data, optimizedPrompt);

                // Supprimer l'animation de réflexion
                removeThinkingMessage(thinkingMessage);

                // 🧹 NETTOYER LA RÉPONSE
                let assistantMessage = data.content || data.text || 'Réponse vide de DeepSeek R1 8B';
                assistantMessage = cleanDeepSeekResponse(assistantMessage);

                addMessage('assistant', assistantMessage);
                console.log('✅ Réponse affichée:', assistantMessage);

            } catch (error) {
                console.error('❌ Erreur complète:', error);
                removeThinkingMessage(thinkingMessage);
                addMessage('assistant', `❌ Erreur: ${error.message}`);
            } finally {
                sendButton.disabled = false;
                sendButton.textContent = 'Envoyer';
            }
        }

        // 🤔 Animation de réflexion AMÉLIORÉE
        function addThinkingMessage() {
            const thinkingDiv = document.createElement('div');
            thinkingDiv.className = 'thinking-message';
            thinkingDiv.style.cssText = `
                margin: 10px 0;
                padding: 15px;
                border-radius: 8px;
                background: linear-gradient(45deg, #1a3a1a, #2a4a2a);
                border-left: 4px solid #00ff88;
                border: 1px solid #00ff88;
                box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
                animation: pulse 1.5s infinite;
            `;

            // Ajouter l'animation CSS
            if (!document.querySelector('#thinking-animation-style')) {
                const style = document.createElement('style');
                style.id = 'thinking-animation-style';
                style.textContent = `
                    @keyframes pulse {
                        0% { box-shadow: 0 0 5px rgba(0, 255, 136, 0.3); }
                        50% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
                        100% { box-shadow: 0 0 5px rgba(0, 255, 136, 0.3); }
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    .spinner {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        border: 2px solid #333;
                        border-top: 2px solid #00ff88;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin-right: 8px;
                    }
                `;
                document.head.appendChild(style);
            }

            thinkingDiv.innerHTML = `
                <strong>🤖 DeepSeek R1 8B:</strong>
                <span class="spinner"></span>
                <span class="thinking-text">Génération de la réponse</span>
                <span class="dots">...</span>
            `;

            chatMessages.appendChild(thinkingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Animation des points et du texte
            let dotCount = 0;
            let textIndex = 0;
            const texts = ['Génération de la réponse', 'Analyse en cours', 'Traitement', 'Réflexion'];

            const interval = setInterval(() => {
                const dotsSpan = thinkingDiv.querySelector('.dots');
                const textSpan = thinkingDiv.querySelector('.thinking-text');

                if (dotsSpan && textSpan) {
                    // Animation des points
                    dotCount = (dotCount + 1) % 4;
                    dotsSpan.textContent = '.'.repeat(dotCount + 1);

                    // Changement de texte toutes les 2 secondes
                    if (dotCount === 0) {
                        textIndex = (textIndex + 1) % texts.length;
                        textSpan.textContent = texts[textIndex];
                    }
                }
            }, 300);

            thinkingDiv.interval = interval;
            return thinkingDiv;
        }

        function removeThinkingMessage(thinkingDiv) {
            if (thinkingDiv && thinkingDiv.interval) {
                clearInterval(thinkingDiv.interval);
                thinkingDiv.remove();
            }
        }

        // 🚀 ACCÉLÉRATEURS DE PERFORMANCE RÉELS
        function buildOptimizedPrompt(message) {
            // Prompt simple qui fonctionne
            const simplePrompt = `${message}`;

            // Accélérateur: Cache du contexte
            if (conversationContext.length > 500) {
                conversationContext = conversationContext.slice(-300);
            }

            return simplePrompt;
        }

        // 🚀 ACCÉLÉRATEUR: Préchargement du modèle
        async function warmupModel() {
            try {
                await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: "Test",
                        n_predict: 1,
                        temperature: 0.1
                    })
                });
                console.log('🚀 Modèle préchauffé');
            } catch (e) {
                console.log('⚠️ Préchauffage échoué:', e.message);
            }
        }

        // 🚀 ACCÉLÉRATEUR: Optimisation automatique des paramètres
        function getOptimizedParams(messageLength) {
            if (messageLength < 10) {
                return { n_predict: 50, temperature: 0.01 }; // Ultra rapide pour questions courtes
            } else if (messageLength < 50) {
                return { n_predict: 80, temperature: 0.05 }; // Rapide
            } else {
                return { n_predict: 120, temperature: 0.1 }; // Standard
            }
        }

        // 🧹 NETTOYAGE SIMPLE DES RÉPONSES
        function cleanDeepSeekResponse(response) {
            if (!response) return 'Réponse vide';

            // Juste nettoyer les espaces
            let cleaned = response.trim();

            return cleaned;
        }
        
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');

            // Styles selon le rôle
            let bgColor, borderColor, roleLabel;
            switch(role) {
                case 'user':
                    bgColor = '#2a2a2a';
                    borderColor = '#00ff88';
                    roleLabel = 'Vous';
                    break;
                case 'assistant':
                    bgColor = '#1a3a1a';
                    borderColor = '#0088ff';
                    roleLabel = 'DeepSeek R1 8B';
                    break;
                case 'system':
                    bgColor = '#3a1a1a';
                    borderColor = '#ff8800';
                    roleLabel = 'Système';
                    break;
                default:
                    bgColor = '#1a1a1a';
                    borderColor = '#666';
                    roleLabel = 'Agent';
            }

            messageDiv.style.cssText = `
                margin: 10px 0;
                padding: 15px;
                border-radius: 8px;
                background: ${bgColor};
                border-left: 4px solid ${borderColor};
            `;
            messageDiv.innerHTML = `<strong>${roleLabel}:</strong> ${content}`;

            // Supprimer le message de bienvenue
            const welcomeMsg = chatMessages.querySelector('.welcome-message');
            if (welcomeMsg) welcomeMsg.remove();

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 🎯 ÉVÉNEMENTS
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 📱 FONCTIONNALITÉS BARRE LATÉRALE
        document.querySelectorAll('.sidebar-button').forEach(button => {
            button.addEventListener('click', () => {
                const buttonText = button.textContent.trim();
                console.log('🔘 Bouton sidebar:', buttonText);

                switch(buttonText) {
                    case '+ Nouvelle conversation':
                        nouvelleConversation();
                        break;
                    case 'Activer mémoire':
                        activerMemoireThermique();
                        break;
                    case 'Mode MCP':
                        activerModeMCP();
                        break;
                    case 'Internet':
                        activerInternet();
                        break;
                    case 'Configuration':
                        ouvrirConfiguration();
                        break;
                    default:
                        console.log('🔘 Fonction à implémenter:', buttonText);
                }
            });
        });

        // 🔄 Nouvelle conversation
        function nouvelleConversation() {
            chatMessages.innerHTML = '<div class="welcome-message">DeepSeek R1 8B Agent</div>';
            console.log('🔄 Nouvelle conversation démarrée');
        }

        // 🧠 Mémoire thermique
        function activerMemoireThermique() {
            addMessage('system', '🧠 Mémoire thermique activée - Historique des conversations chargé');
            console.log('🧠 Mémoire thermique activée');
        }

        // 🔌 Mode MCP
        function activerModeMCP() {
            addMessage('system', '🔌 Mode MCP activé - Accès aux outils externes disponible');
            console.log('🔌 Mode MCP activé');
        }

        // 🌐 Internet
        function activerInternet() {
            addMessage('system', '🌐 Accès Internet activé - Recherche web disponible');
            console.log('🌐 Internet activé');
        }

        // ⚙️ Configuration
        function ouvrirConfiguration() {
            addMessage('system', '⚙️ Panneau de configuration - Paramètres du modèle DeepSeek R1 8B');
            console.log('⚙️ Configuration ouverte');
        }
        
        console.log('✅ Interface v2 initialisée - Agent principal préservé');

        // 🧠 GESTION FENÊTRE DE RAISONNEMENT
        const reasoningToggle = document.getElementById('reasoningToggle');
        const reasoningPanel = document.getElementById('reasoningPanel');
        const reasoningClose = document.getElementById('reasoningClose');
        const reasoningContent = document.getElementById('reasoningContent');

        reasoningToggle.addEventListener('click', () => {
            reasoningPanel.style.display = reasoningPanel.style.display === 'flex' ? 'none' : 'flex';
        });

        reasoningClose.addEventListener('click', () => {
            reasoningPanel.style.display = 'none';
        });

        // 🧠 CAPTURE DU RAISONNEMENT
        function captureReasoning(userMessage, apiResponse, sentPrompt) {
            const timestamp = new Date().toLocaleTimeString();

            const reasoningHTML = `
                <div style="border-bottom: 1px solid #333; margin-bottom: 15px; padding-bottom: 15px;">
                    <div style="color: #00ff88; font-weight: bold;">[${timestamp}] Nouvelle interaction</div>

                    <div style="margin: 10px 0;">
                        <strong style="color: #88ff88;">📤 Message utilisateur:</strong><br>
                        <div style="background: #2a2a2a; padding: 8px; border-radius: 4px; margin: 5px 0;">
                            ${userMessage}
                        </div>
                    </div>

                    <div style="margin: 10px 0;">
                        <strong style="color: #8888ff;">🔗 Prompt envoyé:</strong><br>
                        <div style="background: #1a1a2a; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 11px;">
                            ${sentPrompt}
                        </div>
                    </div>

                    <div style="margin: 10px 0;">
                        <strong style="color: #ff8888;">🧠 Données brutes reçues:</strong><br>
                        <div style="background: #2a1a1a; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 11px;">
                            <pre>${JSON.stringify(apiResponse, null, 2)}</pre>
                        </div>
                    </div>

                    <div style="margin: 10px 0;">
                        <strong style="color: #ffff88;">📊 Statistiques:</strong><br>
                        <div style="background: #2a2a1a; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 11px;">
                            • Tokens générés: ${apiResponse.tokens_predicted || 'N/A'}<br>
                            • Temps: ${apiResponse.timings?.predicted_ms || 'N/A'}ms<br>
                            • Vitesse: ${apiResponse.timings?.predicted_per_token_ms || 'N/A'}ms/token
                        </div>
                    </div>
                </div>
            `;

            reasoningContent.innerHTML = reasoningHTML + reasoningContent.innerHTML;
        }

        // 🚀 DÉMARRAGE DES ACCÉLÉRATEURS
        setTimeout(() => {
            warmupModel(); // Préchauffage du modèle après 2 secondes
        }, 2000);
    </script>
</body>
</html>
