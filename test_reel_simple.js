#!/usr/bin/env node

// Test RÉEL simplifié avec timeouts étendus
const http = require('http');

console.log('🧪 TEST RÉEL SIMPLIFIÉ - MÉMOIRE THERMIQUE');
console.log('=' .repeat(50));

let thermalMemory = [];

// Fonctions mémoire essentielles
function saveThermalMemory(user, agent) {
    const entry = {
        id: Date.now(),
        user: user,
        agent: agent,
        keywords: user.toLowerCase().includes('appelle') ? ['jean-luc', 'appelle'] : []
    };
    thermalMemory.push(entry);
    console.log(`💾 Sauvegardé: "${user}" → "${agent.substring(0, 30)}..."`);
    return entry;
}

function searchMemory(query) {
    console.log(`🔍 Recherche: "${query}"`);
    
    if (thermalMemory.length === 0) {
        console.log('❌ Mémoire vide');
        return '';
    }
    
    const queryLower = query.toLowerCase();
    if (queryLower.includes('souviens') || queryLower.includes('nom')) {
        const found = thermalMemory.find(entry => 
            entry.user.toLowerCase().includes('appelle') ||
            entry.agent.toLowerCase().includes('jean-luc')
        );
        
        if (found) {
            const context = `\n\nCONTEXTE MÉMOIRE:\nPrécédemment: "${found.user}" → "${found.agent}"\nUtilise ce contexte.\n`;
            console.log(`✅ Contexte trouvé: ${context.length} caractères`);
            return context;
        }
    }
    
    console.log('❌ Aucun contexte trouvé');
    return '';
}

// Appel serveur avec timeout étendu
function callServer(prompt, timeout = 60000) {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({
            prompt: prompt,
            n_predict: 50,
            temperature: 0.3,
            stream: false
        });
        
        console.log(`📤 Envoi: "${prompt.substring(0, 40)}..."`);
        console.log(`⏱️ Timeout: ${timeout/1000}s`);
        
        const req = http.request({
            hostname: 'localhost',
            port: 8000,
            path: '/completion',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(data)
            },
            timeout: timeout
        }, (res) => {
            let responseData = '';
            res.on('data', chunk => responseData += chunk);
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(responseData);
                    const reply = (parsed.content || parsed.text || '').trim();
                    console.log(`📥 Reçu: "${reply.substring(0, 40)}..."`);
                    resolve(reply);
                } catch (error) {
                    reject(new Error(`Parse error: ${error.message}`));
                }
            });
        });
        
        req.on('error', reject);
        req.on('timeout', () => {
            req.destroy();
            reject(new Error(`Timeout après ${timeout/1000}s`));
        });
        
        req.write(data);
        req.end();
    });
}

// Test principal
async function runSimpleTest() {
    try {
        console.log('🔧 Test connexion...');
        
        // Test connexion simple
        const healthCheck = await new Promise((resolve, reject) => {
            const req = http.request({
                hostname: 'localhost',
                port: 8000,
                path: '/health',
                timeout: 5000
            }, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => resolve(data.includes('ok')));
            });
            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Health check timeout'));
            });
            req.end();
        });
        
        if (!healthCheck) {
            throw new Error('Health check failed');
        }
        console.log('✅ Serveur accessible');
        
        // Test 1: Message simple d'abord
        console.log('\n📝 TEST 1: Message simple');
        const simpleResponse = await callServer('Bonjour', 30000);
        console.log(`🤖 Réponse simple: "${simpleResponse}"`);
        
        // Test 2: Présentation
        console.log('\n📝 TEST 2: Présentation');
        const message1 = 'Je m\'appelle Jean-Luc';
        const response1 = await callServer(message1, 45000);
        
        saveThermalMemory(message1, response1);
        console.log(`👤 "${message1}"`);
        console.log(`🤖 "${response1}"`);
        
        // Attendre
        console.log('\n⏳ Attente 5 secondes...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 3: Question mémoire
        console.log('\n📝 TEST 3: Question mémoire');
        const message2 = 'Tu te souviens de mon nom ?';
        const context = searchMemory(message2);
        const promptWithMemory = message2 + context;
        
        console.log(`📋 Prompt final: "${promptWithMemory.substring(0, 100)}..."`);
        
        const response2 = await callServer(promptWithMemory, 45000);
        
        console.log(`👤 "${message2}"`);
        console.log(`🤖 "${response2}"`);
        
        // Vérification
        console.log('\n🔍 VÉRIFICATION');
        const hasName = response2.toLowerCase().includes('jean-luc') || 
                       response2.toLowerCase().includes('jean luc');
        
        console.log('=' .repeat(50));
        if (hasName) {
            console.log('🎉 SUCCÈS ! Agent se souvient du nom');
            console.log('✅ Mémoire thermique FONCTIONNE');
        } else {
            console.log('❌ ÉCHEC ! Agent ne se souvient pas');
            console.log('❌ Problème mémoire thermique');
        }
        console.log('=' .repeat(50));
        
        return hasName;
        
    } catch (error) {
        console.error(`❌ Erreur: ${error.message}`);
        return false;
    }
}

// Exécution
runSimpleTest().then(success => {
    console.log(`\n🏁 Résultat final: ${success ? 'SUCCÈS' : 'ÉCHEC'}`);
    process.exit(success ? 0 : 1);
});
