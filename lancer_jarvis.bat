@echo off
REM 🤖 LANCEMENT JARVIS - Agent DeepSeek R1 8B (Windows)
REM Jean-Luc - Ra<PERSON>urci Windows avec adaptation automatique

echo 🚀 DÉMARRAGE JARVIS - Agent DeepSeek R1 8B (Windows)
echo 📍 Répertoire: %CD%

REM Vérifier Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erreur: Node.js non installé
    pause
    exit /b 1
)

REM Vérifier fichiers
if not exist "agent1_reel_simple.js" (
    echo ❌ Erreur: agent1_reel_simple.js non trouvé
    pause
    exit /b 1
)

if not exist "mpc_bureau_adaptatif.js" (
    echo ❌ Erreur: mpc_bureau_adaptatif.js non trouvé
    pause
    exit /b 1
)

echo ✅ Fichiers trouvés
echo 🖥️ Adaptation Windows en cours...
echo 🤖 Lancement de JARVIS avec MPC Adaptatif...

REM Lancer l'agent avec adaptation
node agent1_reel_simple.js

pause
