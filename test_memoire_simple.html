<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mémoire Thermique - JARVIS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px; margin: 20px 0;
            border-radius: 10px; border-left: 4px solid #4CAF50;
        }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 10px 20px; border-radius: 5px; cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        #log { background: #000; padding: 15px; border-radius: 5px; 
               font-family: monospace; font-size: 12px; height: 200px; 
               overflow-y: auto; margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 Test Mémoire Thermique - JARVIS</h1>
    
    <div class="test-section">
        <h2>🔧 Tests de Base</h2>
        <button onclick="testLocalStorage()">Test LocalStorage</button>
        <button onclick="testMemoryFunctions()">Test Fonctions Mémoire</button>
        <button onclick="testSearchMemory()">Test Recherche</button>
        <button onclick="clearMemory()">Effacer Mémoire</button>
        <div id="basicResults"></div>
    </div>

    <div class="test-section">
        <h2>🧠 Simulation Mémoire Thermique</h2>
        <button onclick="simulateConversation()">Simuler Conversation</button>
        <button onclick="testMemoryTriggers()">Test Triggers</button>
        <button onclick="showMemoryStats()">Statistiques</button>
        <div id="memoryResults"></div>
    </div>

    <div class="test-section">
        <h2>🔍 Test Réflexion</h2>
        <button onclick="testReasoningWindow()">Test Fenêtre Réflexion</button>
        <button onclick="simulateReasoning()">Simuler Réflexion</button>
        <div id="reasoningResults"></div>
    </div>

    <div id="log"></div>

    <script>
        // Variables globales pour les tests
        let thermalMemory = [];
        let testResults = [];

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        // Test 1: LocalStorage
        function testLocalStorage() {
            log('🔧 Test LocalStorage...');
            
            try {
                // Test écriture
                const testData = { test: 'mémoire thermique', timestamp: Date.now() };
                localStorage.setItem('jarvis_test', JSON.stringify(testData));
                
                // Test lecture
                const retrieved = JSON.parse(localStorage.getItem('jarvis_test'));
                
                if (retrieved && retrieved.test === 'mémoire thermique') {
                    showResult('basicResults', '✅ LocalStorage fonctionne correctement', 'success');
                    log('✅ LocalStorage: OK');
                } else {
                    throw new Error('Données corrompues');
                }
                
                // Nettoyage
                localStorage.removeItem('jarvis_test');
                
            } catch (error) {
                showResult('basicResults', `❌ LocalStorage: ${error.message}`, 'error');
                log(`❌ LocalStorage: ${error.message}`);
            }
        }

        // Test 2: Fonctions mémoire
        function testMemoryFunctions() {
            log('🧠 Test fonctions mémoire...');
            
            try {
                // Test sauvegarde
                saveThermalMemory('Bonjour', 'Salut ! Comment allez-vous ?', 'Test de base');
                saveThermalMemory('Quel est mon nom ?', 'Je ne connais pas votre nom', 'Pas de contexte');
                
                if (thermalMemory.length === 2) {
                    showResult('basicResults', '✅ Sauvegarde mémoire fonctionne', 'success');
                    log('✅ Sauvegarde: OK');
                } else {
                    throw new Error(`Attendu 2 entrées, trouvé ${thermalMemory.length}`);
                }
                
                // Test structure
                const entry = thermalMemory[0];
                if (entry.user && entry.agent && entry.timestamp) {
                    showResult('basicResults', '✅ Structure mémoire correcte', 'success');
                    log('✅ Structure: OK');
                } else {
                    throw new Error('Structure mémoire incorrecte');
                }
                
            } catch (error) {
                showResult('basicResults', `❌ Fonctions mémoire: ${error.message}`, 'error');
                log(`❌ Fonctions mémoire: ${error.message}`);
            }
        }

        // Test 3: Recherche mémoire
        function testSearchMemory() {
            log('🔍 Test recherche mémoire...');
            
            try {
                // Ajouter des données de test
                thermalMemory = [
                    { user: 'Je m\'appelle Jean-Luc', agent: 'Enchanté Jean-Luc !', timestamp: Date.now() },
                    { user: 'J\'aime la programmation', agent: 'C\'est passionnant !', timestamp: Date.now() },
                    { user: 'Quel temps fait-il ?', agent: 'Je ne peux pas voir dehors', timestamp: Date.now() }
                ];
                
                // Test recherche par trigger
                const result1 = searchMemory('tu te souviens de mon nom ?');
                if (result1.includes('Jean-Luc')) {
                    showResult('basicResults', '✅ Recherche par trigger fonctionne', 'success');
                    log('✅ Recherche trigger: OK');
                } else {
                    throw new Error('Trigger non détecté');
                }
                
                // Test recherche par mot-clé
                const result2 = searchMemory('programmation');
                if (result2.includes('programmation') || result2.includes('passionnant')) {
                    showResult('basicResults', '✅ Recherche par mot-clé fonctionne', 'success');
                    log('✅ Recherche mot-clé: OK');
                } else {
                    log('⚠️ Recherche mot-clé: Partielle');
                }
                
            } catch (error) {
                showResult('basicResults', `❌ Recherche: ${error.message}`, 'error');
                log(`❌ Recherche: ${error.message}`);
            }
        }

        // Simulation conversation complète
        function simulateConversation() {
            log('🎭 Simulation conversation...');
            
            const conversation = [
                { user: 'Bonjour, je m\'appelle Jean-Luc', agent: 'Bonjour Jean-Luc ! Ravi de vous rencontrer.' },
                { user: 'Je travaille sur un projet IA', agent: 'Très intéressant ! Quel type de projet IA ?' },
                { user: 'Un assistant avec mémoire thermique', agent: 'Excellent ! La mémoire persistante est cruciale.' },
                { user: 'Tu te souviens de mon nom ?', agent: 'Oui, vous êtes Jean-Luc !' },
                { user: 'Et mon projet ?', agent: 'Votre projet d\'assistant IA avec mémoire thermique.' }
            ];
            
            // Simuler la conversation
            thermalMemory = [];
            conversation.forEach((exchange, i) => {
                setTimeout(() => {
                    saveThermalMemory(exchange.user, exchange.agent, `Étape ${i + 1}`);
                    log(`💬 ${i + 1}/5: "${exchange.user}" → "${exchange.agent}"`);
                    
                    if (i === conversation.length - 1) {
                        // Test final
                        setTimeout(() => {
                            const memoryTest = searchMemory('tu te souviens de mon nom ?');
                            if (memoryTest.includes('Jean-Luc')) {
                                showResult('memoryResults', '✅ Conversation simulée avec succès', 'success');
                                showResult('memoryResults', `📊 ${thermalMemory.length} échanges mémorisés`, 'info');
                                log('✅ Simulation complète: OK');
                            } else {
                                showResult('memoryResults', '❌ Mémoire non fonctionnelle', 'error');
                                log('❌ Simulation: Échec');
                            }
                        }, 500);
                    }
                }, i * 200);
            });
        }

        // Test triggers mémoire
        function testMemoryTriggers() {
            log('🎯 Test triggers mémoire...');
            
            const triggers = [
                'tu te souviens de mon nom ?',
                'rappelle-toi ce que j\'ai dit',
                'précédemment tu avais mentionné',
                'la dernière fois on parlait de',
                'avant tu m\'avais dit'
            ];
            
            let successCount = 0;
            triggers.forEach(trigger => {
                const result = searchMemory(trigger);
                if (result && result.length > 0) {
                    successCount++;
                    log(`✅ Trigger détecté: "${trigger}"`);
                } else {
                    log(`❌ Trigger raté: "${trigger}"`);
                }
            });
            
            const percentage = (successCount / triggers.length) * 100;
            showResult('memoryResults', `🎯 Triggers: ${successCount}/${triggers.length} (${percentage}%)`, 
                      percentage >= 80 ? 'success' : 'error');
        }

        // Statistiques mémoire
        function showMemoryStats() {
            log('📊 Calcul statistiques...');
            
            if (thermalMemory.length === 0) {
                showResult('memoryResults', '⚠️ Aucune donnée en mémoire', 'error');
                return;
            }
            
            const totalEntries = thermalMemory.length;
            const totalChars = thermalMemory.reduce((sum, entry) => 
                sum + (entry.user?.length || 0) + (entry.agent?.length || 0), 0);
            const avgResponseLength = thermalMemory.reduce((sum, entry) => 
                sum + (entry.agent?.length || 0), 0) / totalEntries;
            
            const stats = `
📊 STATISTIQUES MÉMOIRE THERMIQUE:
• Entrées totales: ${totalEntries}
• Caractères totaux: ${totalChars}
• Longueur moyenne réponse: ${Math.round(avgResponseLength)}
• Taille localStorage: ${JSON.stringify(thermalMemory).length} bytes
            `.trim();
            
            showResult('memoryResults', stats, 'info');
            log('📊 Statistiques calculées');
        }

        // Test fenêtre réflexion
        function testReasoningWindow() {
            log('🔍 Test fenêtre réflexion...');
            
            try {
                // Simuler l'activation de la réflexion
                const reasoningDiv = document.createElement('div');
                reasoningDiv.id = 'testReasoning';
                reasoningDiv.style.cssText = `
                    background: rgba(0,0,0,0.8); color: #0f0;
                    padding: 15px; border-radius: 8px; margin: 10px 0;
                    font-family: monospace; font-size: 12px;
                `;
                
                const timestamp = new Date().toLocaleTimeString();
                reasoningDiv.innerHTML = `
                    <div>[${timestamp}] Initialisation processus de réflexion...</div>
                    <div>[${timestamp}] Analyse de la requête utilisateur</div>
                    <div>[${timestamp}] Recherche dans la mémoire thermique</div>
                    <div>[${timestamp}] Génération de la réponse</div>
                    <div>[${timestamp}] Processus terminé avec succès</div>
                `;
                
                document.getElementById('reasoningResults').appendChild(reasoningDiv);
                showResult('reasoningResults', '✅ Fenêtre réflexion créée', 'success');
                log('✅ Fenêtre réflexion: OK');
                
            } catch (error) {
                showResult('reasoningResults', `❌ Réflexion: ${error.message}`, 'error');
                log(`❌ Réflexion: ${error.message}`);
            }
        }

        // Simulation réflexion
        function simulateReasoning() {
            log('🤔 Simulation processus de réflexion...');
            
            const steps = [
                'Réception message utilisateur',
                'Analyse sémantique du contenu',
                'Recherche dans mémoire thermique',
                'Évaluation du contexte',
                'Génération réponse appropriée',
                'Vérification cohérence',
                'Envoi réponse finale'
            ];
            
            const reasoningDiv = document.createElement('div');
            reasoningDiv.style.cssText = `
                background: #001122; color: #00ff88; padding: 15px;
                border-radius: 8px; margin: 10px 0; font-family: monospace;
                font-size: 12px; border-left: 3px solid #00ff88;
            `;
            
            let content = '<div><strong>🤔 PROCESSUS DE RÉFLEXION DEEPSEEK R1 8B</strong></div><br>';
            
            steps.forEach((step, i) => {
                setTimeout(() => {
                    const timestamp = new Date().toLocaleTimeString();
                    content += `<div>[${timestamp}] Étape ${i + 1}/7: ${step}</div>`;
                    reasoningDiv.innerHTML = content;
                    
                    if (i === steps.length - 1) {
                        content += '<div><br><strong>✅ Processus terminé avec succès</strong></div>';
                        reasoningDiv.innerHTML = content;
                        showResult('reasoningResults', '✅ Simulation réflexion complète', 'success');
                        log('✅ Simulation réflexion: OK');
                    }
                }, i * 300);
            });
            
            document.getElementById('reasoningResults').appendChild(reasoningDiv);
        }

        // Fonctions utilitaires (reprises de l'interface principale)
        function saveThermalMemory(userMessage, agentResponse, reasoning) {
            const entry = {
                id: Date.now() + Math.random(),
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse,
                reasoning: reasoning || null
            };
            
            thermalMemory.push(entry);
            localStorage.setItem('jarvis_thermal_memory_test', JSON.stringify(thermalMemory));
        }

        function searchMemory(query) {
            if (thermalMemory.length === 0) return '';
            
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'précédemment', 'avant', 'déjà dit'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                const results = thermalMemory.filter(entry => 
                    entry.user?.toLowerCase().includes(queryLower) || 
                    entry.agent?.toLowerCase().includes(queryLower)
                ).slice(-3);
                
                if (results.length > 0) {
                    return 'CONTEXTE TROUVÉ: ' + 
                           results.map(r => `"${r.user}" → "${r.agent}"`).join(' | ');
                }
            }
            
            return 'Contexte récent disponible';
        }

        function clearMemory() {
            thermalMemory = [];
            localStorage.removeItem('jarvis_thermal_memory_test');
            document.getElementById('basicResults').innerHTML = '';
            document.getElementById('memoryResults').innerHTML = '';
            document.getElementById('reasoningResults').innerHTML = '';
            document.getElementById('log').innerHTML = '';
            log('🗑️ Mémoire et tests effacés');
        }

        // Initialisation
        log('🚀 Tests mémoire thermique initialisés');
        log('💡 Cliquez sur les boutons pour tester les fonctionnalités');
    </script>
</body>
</html>
