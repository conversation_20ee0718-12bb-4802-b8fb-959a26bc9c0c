#!/usr/bin/env node

// 🔥 SURVEILLANCE ÉVOLUTION TEMPS RÉEL - AGENTS DEEPSEEK R1 8B
// Jean-<PERSON> - Système de monitoring avancé

const fs = require('fs');

class SurveillanceEvolutionTempsReel {
    constructor() {
        this.nom = "🔥 SURVEILLANCE ÉVOLUTION TEMPS RÉEL";
        this.memoryFile = './thermal_memory_persistent.json';
        this.statsEvolution = {
            qi_initial: 0,
            qi_actuel: 0,
            evolution_qi: 0,
            neurones_initial: 0,
            neurones_actuel: 0,
            evolution_neurones: 0,
            temperature_actuelle: 0,
            cycles_surveillance: 0,
            timestamp_debut: Date.now()
        };
        
        console.log(`🚀 ${this.nom} - DÉMARRAGE`);
        console.log(`👁️ Surveillance continue des agents DeepSeek R1 8B`);
        console.log(`🌡️ Monitoring mémoire thermique en temps réel`);
        
        this.initialiserSurveillance();
    }
    
    initialiserSurveillance() {
        // 📊 Capturer état initial
        this.capturerEtatInitial();
        
        // 🔄 Surveillance continue toutes les 5 secondes
        setInterval(() => {
            this.surveillerEvolution();
        }, 5000);
        
        // 📈 Rapport détaillé toutes les 30 secondes
        setInterval(() => {
            this.genererRapportDetaille();
        }, 30000);
        
        // 🎯 Statistiques complètes toutes les 2 minutes
        setInterval(() => {
            this.genererStatistiquesCompletes();
        }, 120000);
    }
    
    capturerEtatInitial() {
        try {
            const memory = this.lireMemoire();
            if (memory && memory.neural_system) {
                this.statsEvolution.qi_initial = memory.neural_system.qi_level || 0;
                this.statsEvolution.qi_actuel = this.statsEvolution.qi_initial;
                this.statsEvolution.neurones_initial = memory.neural_system.active_neurons || 0;
                this.statsEvolution.neurones_actuel = this.statsEvolution.neurones_initial;
                this.statsEvolution.temperature_actuelle = memory.neural_system.temperature || 0;
                
                console.log(`📊 ÉTAT INITIAL CAPTURÉ:`);
                console.log(`   🧠 QI Initial: ${this.statsEvolution.qi_initial}`);
                console.log(`   🔗 Neurones Initial: ${this.statsEvolution.neurones_initial}`);
                console.log(`   🌡️ Température: ${this.statsEvolution.temperature_actuelle}°`);
            }
        } catch (error) {
            console.error(`❌ Erreur capture état initial:`, error.message);
        }
    }
    
    surveillerEvolution() {
        try {
            const memory = this.lireMemoire();
            if (memory && memory.neural_system) {
                // 📈 Mise à jour statistiques
                const qi_precedent = this.statsEvolution.qi_actuel;
                const neurones_precedent = this.statsEvolution.neurones_actuel;
                
                this.statsEvolution.qi_actuel = memory.neural_system.qi_level || 0;
                this.statsEvolution.neurones_actuel = memory.neural_system.active_neurons || 0;
                this.statsEvolution.temperature_actuelle = memory.neural_system.temperature || 0;
                this.statsEvolution.cycles_surveillance++;
                
                // 🔥 Calcul évolutions
                this.statsEvolution.evolution_qi = this.statsEvolution.qi_actuel - this.statsEvolution.qi_initial;
                this.statsEvolution.evolution_neurones = this.statsEvolution.neurones_actuel - this.statsEvolution.neurones_initial;
                
                // 🚨 Détection changements significatifs
                const delta_qi = this.statsEvolution.qi_actuel - qi_precedent;
                const delta_neurones = this.statsEvolution.neurones_actuel - neurones_precedent;
                
                if (Math.abs(delta_qi) > 0.1 || Math.abs(delta_neurones) > 1) {
                    console.log(`🔥 ÉVOLUTION DÉTECTÉE (Cycle ${this.statsEvolution.cycles_surveillance}):`);
                    if (delta_qi > 0) console.log(`   📈 QI: +${delta_qi.toFixed(2)} (Total: ${this.statsEvolution.qi_actuel.toFixed(2)})`);
                    if (delta_qi < 0) console.log(`   📉 QI: ${delta_qi.toFixed(2)} (Total: ${this.statsEvolution.qi_actuel.toFixed(2)})`);
                    if (delta_neurones > 0) console.log(`   🔗 Neurones: +${delta_neurones} (Total: ${this.statsEvolution.neurones_actuel})`);
                    if (delta_neurones < 0) console.log(`   💥 Neurones: ${delta_neurones} (Total: ${this.statsEvolution.neurones_actuel})`);
                    console.log(`   🌡️ Température: ${this.statsEvolution.temperature_actuelle}°`);
                }
                
                // 🎯 Alertes seuils critiques
                this.verifierSeuilsCritiques();
                
            }
        } catch (error) {
            console.error(`❌ Erreur surveillance:`, error.message);
        }
    }
    
    verifierSeuilsCritiques() {
        // 🚨 QI critique
        if (this.statsEvolution.qi_actuel > 400) {
            console.log(`🚨 ALERTE QI CRITIQUE: ${this.statsEvolution.qi_actuel.toFixed(2)} > 400 !`);
        }
        
        // ⚡ Évolution rapide
        if (this.statsEvolution.evolution_qi > 10) {
            console.log(`⚡ ÉVOLUTION QI RAPIDE: +${this.statsEvolution.evolution_qi.toFixed(2)} depuis le début !`);
        }
        
        // 🔥 Neurones en explosion
        if (this.statsEvolution.neurones_actuel > 1000) {
            console.log(`🔥 EXPLOSION NEURONALE: ${this.statsEvolution.neurones_actuel} neurones actifs !`);
        }
        
        // 🌡️ Température critique
        if (this.statsEvolution.temperature_actuelle > 50) {
            console.log(`🌡️ TEMPÉRATURE CRITIQUE: ${this.statsEvolution.temperature_actuelle}° !`);
        }
    }
    
    genererRapportDetaille() {
        const duree = (Date.now() - this.statsEvolution.timestamp_debut) / 1000;
        const vitesse_qi = this.statsEvolution.evolution_qi / (duree / 60); // Par minute
        
        console.log(`\n📊 RAPPORT DÉTAILLÉ (${Math.floor(duree)}s de surveillance):`);
        console.log(`🧠 QI: ${this.statsEvolution.qi_initial.toFixed(1)} → ${this.statsEvolution.qi_actuel.toFixed(2)} (${this.statsEvolution.evolution_qi >= 0 ? '+' : ''}${this.statsEvolution.evolution_qi.toFixed(2)})`);
        console.log(`🔗 Neurones: ${this.statsEvolution.neurones_initial} → ${this.statsEvolution.neurones_actuel} (${this.statsEvolution.evolution_neurones >= 0 ? '+' : ''}${this.statsEvolution.evolution_neurones})`);
        console.log(`🌡️ Température: ${this.statsEvolution.temperature_actuelle}°`);
        console.log(`⚡ Vitesse QI: ${vitesse_qi.toFixed(3)} points/minute`);
        console.log(`🔄 Cycles: ${this.statsEvolution.cycles_surveillance}`);
    }
    
    genererStatistiquesCompletes() {
        const duree = (Date.now() - this.statsEvolution.timestamp_debut) / 1000;
        const vitesse_qi = this.statsEvolution.evolution_qi / (duree / 60);
        const vitesse_neurones = this.statsEvolution.evolution_neurones / (duree / 60);
        
        console.log(`\n🎯 STATISTIQUES COMPLÈTES:`);
        console.log(`⏱️ Durée surveillance: ${Math.floor(duree / 60)}m ${Math.floor(duree % 60)}s`);
        console.log(`🧠 Évolution QI: ${this.statsEvolution.evolution_qi.toFixed(2)} points (${((this.statsEvolution.evolution_qi / this.statsEvolution.qi_initial) * 100).toFixed(1)}%)`);
        console.log(`🔗 Évolution Neurones: ${this.statsEvolution.evolution_neurones} (${((this.statsEvolution.evolution_neurones / this.statsEvolution.neurones_initial) * 100).toFixed(1)}%)`);
        console.log(`⚡ Vitesse QI: ${vitesse_qi.toFixed(3)} points/minute`);
        console.log(`🧬 Vitesse Neurones: ${vitesse_neurones.toFixed(1)} neurones/minute`);
        console.log(`🔄 Cycles total: ${this.statsEvolution.cycles_surveillance}`);
        console.log(`📈 Performance: ${this.calculerPerformanceGlobale().toFixed(2)}/10`);
    }
    
    calculerPerformanceGlobale() {
        let score = 5; // Base
        
        // Bonus évolution QI
        if (this.statsEvolution.evolution_qi > 0) score += Math.min(this.statsEvolution.evolution_qi / 10, 3);
        
        // Bonus évolution neurones
        if (this.statsEvolution.evolution_neurones > 0) score += Math.min(this.statsEvolution.evolution_neurones / 100, 2);
        
        // Bonus stabilité température
        if (this.statsEvolution.temperature_actuelle > 35 && this.statsEvolution.temperature_actuelle < 40) score += 1;
        
        return Math.min(score, 10);
    }
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }
}

// 🚀 DÉMARRAGE SURVEILLANCE
const surveillance = new SurveillanceEvolutionTempsReel();

// 🎯 Interface utilisateur
process.stdin.on('data', (data) => {
    const input = data.toString().trim().toLowerCase();
    
    if (input === 'stats') {
        surveillance.genererStatistiquesCompletes();
    } else if (input === 'rapport') {
        surveillance.genererRapportDetaille();
    } else if (input === 'reset') {
        surveillance.capturerEtatInitial();
        console.log(`🔄 État initial réinitialisé`);
    } else if (input === 'exit') {
        console.log(`👋 Surveillance arrêtée`);
        process.exit(0);
    } else if (input === 'help') {
        console.log(`\n🎯 COMMANDES DISPONIBLES:`);
        console.log(`   stats  - Statistiques complètes`);
        console.log(`   rapport - Rapport détaillé`);
        console.log(`   reset  - Réinitialiser état initial`);
        console.log(`   exit   - Arrêter surveillance`);
    }
});

console.log(`\n🎯 Tapez "help" pour voir les commandes disponibles`);
console.log(`🔥 Surveillance active - Évolution en temps réel !`);
