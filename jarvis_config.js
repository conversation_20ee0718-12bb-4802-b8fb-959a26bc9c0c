// Configuration JARVIS - DeepSeek R1 8B
// Fichier de configuration pour l'interface JARVIS

const JARVIS_CONFIG = {
    // Mémoire thermique
    memory: {
        enabled: true,              // Activée par défaut
        maxEntries: 1000,          // Limite d'entrées (0 = illimité)
        autoSave: true,            // Sauvegarde automatique
        persistAcrossSessions: true, // Persiste entre les sessions
        searchTriggers: [
            'tu te souviens',
            'rappelle-toi', 
            'précédemment',
            'avant',
            'déjà dit',
            'mon nom',
            'comment je',
            'qui suis'
        ]
    },
    
    // Interface utilisateur
    ui: {
        reasoningVisible: false,    // Fenêtre réflexion fermée par défaut
        showMemoryStats: true,      // Afficher statistiques mémoire
        showMemoryIndicator: true,  // Afficher indicateur mémoire
        autoScroll: true,          // Défilement automatique
        showCloseButtons: true,    // Boutons X sur messages
        defaultLanguage: 'fr'      // Français par défaut
    },
    
    // Agent DeepSeek R1 8B
    agent: {
        serverUrl: 'http://localhost:8000',
        maxTokens: 300,            // Tokens par réponse
        temperature: 0.7,          // Créativité
        timeout: 15000,            // Timeout en ms
        retryAttempts: 2,          // Tentatives en cas d'échec
        healthCheckInterval: 30000  // Vérification connexion (ms)
    },
    
    // Mémoire thermique avancée
    memoryAdvanced: {
        contextWindow: 10,         // Nombre d'échanges récents
        smartSearch: true,         // Recherche intelligente
        categoryFiltering: true,   // Filtrage par catégorie
        relevanceScoring: true,    // Score de pertinence
        debugMode: true           // Mode debug activé
    },
    
    // Messages système
    messages: {
        welcome: `🚀 JARVIS avec DeepSeek R1 8B est prêt !
✅ Mémoire thermique activée par défaut
🔍 Activez la réflexion pour voir le processus de pensée
💡 La mémoire se souvient de toutes vos conversations`,
        
        memoryActivated: '🧠 Mémoire thermique réactivée - Contexte persistant disponible',
        memoryDeactivated: '⚙️ Mémoire thermique temporairement désactivée',
        reasoningActivated: '🔍 Fenêtre de réflexion activée - Processus de pensée visible',
        reasoningDeactivated: '🔍 Fenêtre de réflexion masquée',
        conversationCleared: '🔄 Conversation effacée - JARVIS prêt pour une nouvelle discussion',
        noMemoryAvailable: '🧠 Aucune mémoire thermique disponible',
        serverError: '❌ Erreur de connexion au serveur DeepSeek R1 8B',
        serverConnected: '🟢 Connecté à DeepSeek R1 8B',
        serverDisconnected: '🔴 Déconnecté du serveur'
    },
    
    // Thèmes (pour future extension)
    themes: {
        default: 'blue-gradient',
        available: ['blue-gradient', 'dark-mode', 'light-mode', 'matrix', 'cyberpunk']
    },
    
    // Fonctionnalités expérimentales
    experimental: {
        voiceInput: false,         // Entrée vocale
        voiceOutput: false,        // Sortie vocale
        imageAnalysis: false,      // Analyse d'images
        fileUpload: false,         // Upload de fichiers
        exportConversations: true  // Export conversations
    }
};

// Fonction pour charger la configuration
function loadJarvisConfig() {
    const savedConfig = localStorage.getItem('jarvis_config');
    if (savedConfig) {
        try {
            const parsed = JSON.parse(savedConfig);
            return { ...JARVIS_CONFIG, ...parsed };
        } catch (error) {
            console.warn('Configuration corrompue, utilisation des valeurs par défaut');
            return JARVIS_CONFIG;
        }
    }
    return JARVIS_CONFIG;
}

// Fonction pour sauvegarder la configuration
function saveJarvisConfig(config) {
    try {
        localStorage.setItem('jarvis_config', JSON.stringify(config));
        console.log('✅ Configuration JARVIS sauvegardée');
        return true;
    } catch (error) {
        console.error('❌ Erreur sauvegarde configuration:', error);
        return false;
    }
}

// Fonction pour réinitialiser la configuration
function resetJarvisConfig() {
    localStorage.removeItem('jarvis_config');
    console.log('🔄 Configuration JARVIS réinitialisée');
    return JARVIS_CONFIG;
}

// Fonction pour mettre à jour une option
function updateJarvisConfig(path, value) {
    const config = loadJarvisConfig();
    const keys = path.split('.');
    let current = config;
    
    for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    saveJarvisConfig(config);
    
    console.log(`✅ Configuration mise à jour: ${path} = ${value}`);
    return config;
}

// Export pour utilisation
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        JARVIS_CONFIG,
        loadJarvisConfig,
        saveJarvisConfig,
        resetJarvisConfig,
        updateJarvisConfig
    };
}

// Log de chargement
console.log('📋 Configuration JARVIS chargée:', JARVIS_CONFIG);
