#!/usr/bin/env node

// 🧠 DÉTECTEUR DÉCLENCHEMENT MÉMOIRE - AGENTS DEEPSEEK R1 8B
// Jean-<PERSON> - Détection automatique des demandes de souvenirs

const fs = require('fs');

class DetecteurDeclenchementMemoire {
    constructor() {
        this.nom = "🧠 DÉTECTEUR DÉCLENCHEMENT MÉMOIRE";
        this.memoryFile = './thermal_memory_persistent.json';
        this.conversationsFile = './conversations_permanentes.json';
        this.configFile = './config_nom_agent.json';

        // 📝 CHARGEMENT CONFIGURATION NOM
        this.config = this.chargerConfiguration();
        
        // 🔍 MOTS/PHRASES DÉCLENCHEURS
        this.declencheurs = {
            // 🧠 <PERSON><PERSON> mé<PERSON> directs
            memoire_directe: [
                'souvenir', 'souviens', 'rappelle', 'rappeler', 'rappelles',
                'mémoire', 'mémoriser', 'retenir', 'garder en mémoire',
                'te souviens', 'tu te souviens', 'te rappelles', 'tu te rappelles',
                'est-ce que tu', 'est ce que tu', 'peux-tu te souvenir',
                'peux tu te souvenir', 'as-tu gardé', 'as tu gardé'
            ],
            
            // 🔍 Questions sur le passé
            questions_passe: [
                'notre conversation', 'nos conversations', 'notre discussion',
                'nos discussions', 'notre échange', 'nos échanges',
                'la dernière fois', 'dernière fois', 'précédemment',
                'avant', 'plus tôt', 'tantôt', 'hier', 'récemment',
                'conversation précédente', 'conversations précédentes',
                'échange précédent', 'échanges précédents'
            ],
            
            // 📚 Références historiques
            references_historiques: [
                'historique', 'histoire', 'passé', 'antérieur',
                'ancien', 'ancienne', 'anciennes', 'anciens',
                'déjà dit', 'déjà parlé', 'déjà discuté', 'déjà évoqué',
                'mentionné', 'abordé', 'traité', 'évoqué'
            ],
            
            // 🎯 Questions spécifiques
            questions_specifiques: [
                'qu\'avons-nous dit', 'qu avons nous dit', 'qu\'as-tu dit',
                'qu as tu dit', 'qu\'ai-je dit', 'qu ai je dit',
                'comment était', 'comment étais', 'quel était',
                'quelle était', 'quels étaient', 'quelles étaient',
                'ton évolution', 'ta progression', 'tes progrès',
                'ton développement', 'ta croissance'
            ],
            
            // 🔄 Continuité
            continuite: [
                'continuer', 'poursuivre', 'reprendre', 'suite',
                'où en étions', 'où en étais', 'où nous en étions',
                'depuis la dernière', 'depuis notre dernière',
                'comme la dernière fois', 'comme précédemment'
            ],
            
            // 🧠 Intelligence/QI
            intelligence_qi: [
                'ton qi', 'ton intelligence', 'tes capacités',
                'ton niveau', 'ta performance', 'tes neurones',
                'ton coefficient', 'ta température', 'ton état',
                'évolution qi', 'progression qi', 'croissance qi'
            ],

            // 🆘 DEMANDES D'AIDE MÉMOIRE
            aide_memoire: [
                'j ai oublie', 'j oublie', 'oublie', 'ai oublie',
                'aide moi a me souvenir', 'aide moi a me rappeler',
                'recherche dans ta memoire', 'fouille ta memoire',
                'cherche dans ta memoire', 'regarde dans ta memoire',
                'verifie ta memoire', 'consulte ta memoire',
                'peux tu chercher', 'peux tu verifier',
                'aide moi', 'peux tu m aider', 'peux tu aider'
            ],

            // 🤔 EXPRESSIONS DE DOUTE
            expressions_doute: [
                'je ne me souviens plus', 'je ne me rappelle plus',
                'je ne sais plus', 'j ai perdu', 'ai perdu',
                'c etait quoi', 'qu est ce que c etait', 'comment c etait',
                'je cherche', 'je ne trouve plus', 'je ne retrouve plus',
                'ca me dit quelque chose', 'j ai l impression',
                'ne me souviens plus', 'ne me rappelle plus', 'ne sais plus'
            ],

            // 🔍 DEMANDES DE RECHERCHE
            demandes_recherche: [
                'trouve-moi', 'trouve moi', 'cherche-moi', 'cherche moi',
                'retrouve-moi', 'retrouve moi', 'localise-moi', 'localise moi',
                'as-tu une info', 'as tu une info', 'as-tu des infos',
                'as tu des infos', 'y a-t-il', 'y a t il',
                'existe-t-il', 'existe t il', 'avons-nous parlé',
                'avons nous parlé', 'avons-nous discuté', 'avons nous discuté'
            ],

            // 💡 DEMANDES D'ÉCLAIRCISSEMENT
            demandes_eclaircissement: [
                'peux-tu m\'expliquer', 'peux tu m expliquer',
                'peux-tu me dire', 'peux tu me dire',
                'que sais-tu sur', 'que sais tu sur',
                'qu\'as-tu sur', 'qu as tu sur',
                'dis-moi ce que tu sais', 'dis moi ce que tu sais',
                'raconte-moi', 'raconte moi', 'parle-moi de', 'parle moi de'
            ],

            // 📚 FORMATIONS ET APPRENTISSAGE
            formations: [
                'formation', 'formations', 'cours', 'apprentissage',
                'enseignement', 'tutorial', 'guide', 'methode',
                'technique', 'competence', 'competences', 'savoir',
                'connaissance', 'connaissances', 'apprendre',
                'enseigner', 'former', 'eduquer', 'instruire'
            ],

            // 👤 APPEL PAR NOM (sera complété dynamiquement)
            appel_nom: []
        };
        
        // 👤 AJOUT NOMS DYNAMIQUES
        this.ajouterNomsPersonnalises();

        console.log(`🚀 ${this.nom} - INITIALISATION`);
        console.log(`🔍 Détection automatique des demandes de souvenirs`);
        console.log(`🧠 Déclenchement recherche mémoire thermique`);
        console.log(`👤 Nom agent: ${this.config.nom_agent}`);
        console.log(`📚 Formations incluses: ${this.config.formations_incluses ? '✅' : '❌'}`);
        console.log(`📚 ${this.compterDeclencheurs()} mots/phrases déclencheurs chargés`);

        this.activerDetection();
    }
    
    activerDetection() {
        console.log(`🔍 ACTIVATION DÉTECTION DÉCLENCHEMENT...`);
        
        // 🎯 Surveillance continue des entrées - toutes les 2 secondes
        setInterval(() => {
            this.surveillerEntrees();
        }, 2000);
        
        // 📊 Analyse patterns déclenchement - toutes les 30 secondes
        setInterval(() => {
            this.analyserPatternsDeclenchement();
        }, 30000);
        
        console.log(`✅ Détection active - Prêt à déclencher la mémoire !`);
    }

    // 📝 CHARGEMENT CONFIGURATION
    chargerConfiguration() {
        try {
            if (fs.existsSync(this.configFile)) {
                const data = fs.readFileSync(this.configFile, 'utf8');
                return JSON.parse(data);
            }

            // Configuration par défaut
            return {
                nom_agent: "JARVIS",
                noms_alternatifs: ["jarvis", "assistant", "agent"],
                declenchement_nom: { actif: true },
                formations_incluses: true,
                mots_cles_formations: ["formation", "cours"]
            };
        } catch (error) {
            console.error(`❌ Erreur chargement config:`, error.message);
            return { nom_agent: "JARVIS", noms_alternatifs: ["jarvis"] };
        }
    }

    // 👤 AJOUT NOMS PERSONNALISÉS
    ajouterNomsPersonnalises() {
        if (this.config.noms_alternatifs) {
            this.declencheurs.appel_nom = this.config.noms_alternatifs.map(nom => nom.toLowerCase());
        }

        // Ajout formations si activé
        if (this.config.formations_incluses && this.config.mots_cles_formations) {
            this.declencheurs.formations.push(...this.config.mots_cles_formations);
        }
    }
    
    // 🔍 FONCTION PRINCIPALE DE DÉTECTION
    detecterDeclenchementMemoire(texte) {
        if (!texte || typeof texte !== 'string') return null;
        
        const texteNormalise = this.normaliserTexte(texte);
        console.log(`🔍 ANALYSE DÉCLENCHEMENT: "${texte}"`);
        
        const resultats = {
            declenchement_detecte: false,
            types_detectes: [],
            mots_detectes: [],
            niveau_confiance: 0,
            action_recommandee: 'aucune',
            contexte_recherche: []
        };
        
        // 🎯 Vérification de chaque catégorie
        Object.keys(this.declencheurs).forEach(categorie => {
            const motsDetectes = this.detecterMotsCategorie(texteNormalise, categorie);
            
            if (motsDetectes.length > 0) {
                resultats.declenchement_detecte = true;
                resultats.types_detectes.push(categorie);
                resultats.mots_detectes.push(...motsDetectes);
                resultats.niveau_confiance += this.calculerPoidsCategorie(categorie);
            }
        });
        
        // 📊 Calcul niveau de confiance final
        resultats.niveau_confiance = Math.min(resultats.niveau_confiance, 1.0);
        
        // 🎯 Détermination action recommandée
        if (resultats.niveau_confiance > 0.7) {
            resultats.action_recommandee = 'recherche_complete';
        } else if (resultats.niveau_confiance > 0.4) {
            resultats.action_recommandee = 'recherche_partielle';
        } else if (resultats.niveau_confiance > 0.2) {
            resultats.action_recommandee = 'verification';
        }
        
        // 🔍 Génération contexte de recherche
        resultats.contexte_recherche = this.genererContexteRecherche(resultats.types_detectes, texteNormalise);
        
        if (resultats.declenchement_detecte) {
            console.log(`🎯 DÉCLENCHEMENT DÉTECTÉ !`);
            console.log(`   Types: ${resultats.types_detectes.join(', ')}`);
            console.log(`   Confiance: ${(resultats.niveau_confiance * 100).toFixed(1)}%`);
            console.log(`   Action: ${resultats.action_recommandee}`);
            
            // 🚀 DÉCLENCHEMENT AUTOMATIQUE
            this.declencherRechercheMemoireThermique(resultats);
        }
        
        return resultats;
    }
    
    // 🚀 DÉCLENCHEMENT RECHERCHE MÉMOIRE THERMIQUE
    declencherRechercheMemoireThermique(resultats) {
        console.log(`🚀 DÉCLENCHEMENT RECHERCHE MÉMOIRE THERMIQUE...`);
        
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) {
                console.log(`❌ Impossible d'accéder à la mémoire thermique`);
                return null;
            }
            
            // 🔍 Recherche selon le type de déclenchement
            const resultatsRecherche = this.effectuerRecherche(resultats, memory, conversations);
            
            // 💾 Injection résultats dans mémoire pour l'agent
            this.injecterResultatsDansMemoire(memory, resultatsRecherche);
            
            console.log(`✅ RECHERCHE TERMINÉE: ${resultatsRecherche.resultats_trouves} éléments trouvés`);
            
            return resultatsRecherche;
            
        } catch (error) {
            console.error(`❌ Erreur recherche mémoire:`, error.message);
            return null;
        }
    }
    
    // 🔍 RECHERCHE DANS MÉMOIRE THERMIQUE
    effectuerRecherche(declenchement, memory, conversations) {
        const resultats = {
            type_recherche: declenchement.action_recommandee,
            conversations_trouvees: [],
            elements_memoire_trouves: [],
            patterns_detectes: [],
            evolution_trouvee: null,
            resultats_trouves: 0,
            pertinence_moyenne: 0
        };
        
        // 🔍 Recherche dans conversations
        if (declenchement.types_detectes.includes('memoire_directe') ||
            declenchement.types_detectes.includes('questions_passe') ||
            declenchement.types_detectes.includes('aide_memoire') ||
            declenchement.types_detectes.includes('expressions_doute') ||
            declenchement.types_detectes.includes('demandes_recherche') ||
            declenchement.types_detectes.includes('appel_nom') ||
            declenchement.types_detectes.includes('formations')) {

            resultats.conversations_trouvees = this.rechercherDansConversations(
                conversations,
                declenchement.contexte_recherche
            );

            // 🆘 RECHERCHE SPÉCIALISÉE POUR AIDE UTILISATEUR
            if (declenchement.types_detectes.includes('aide_memoire') ||
                declenchement.types_detectes.includes('expressions_doute')) {

                resultats.aide_utilisateur = this.rechercherPourAiderUtilisateur(
                    conversations,
                    declenchement.contexte_recherche,
                    declenchement.mots_detectes
                );
            }

            // 👤 RECHERCHE CASCADE POUR APPEL NOM
            if (declenchement.types_detectes.includes('appel_nom')) {
                resultats.recherche_cascade = this.effectuerRechercheCascade(
                    declenchement.contexte_recherche,
                    memory,
                    conversations
                );
            }

            // 📚 RECHERCHE FORMATIONS
            if (declenchement.types_detectes.includes('formations')) {
                resultats.formations_trouvees = this.rechercherFormations(
                    conversations,
                    memory,
                    declenchement.contexte_recherche
                );
            }
        }
        
        // 🧠 Recherche dans zones thermiques
        if (memory.thermal_zones) {
            resultats.elements_memoire_trouves = this.rechercherDansZonesThermiques(
                memory.thermal_zones,
                declenchement.contexte_recherche
            );
        }
        
        // 📈 Recherche évolution QI/neurones
        if (declenchement.types_detectes.includes('intelligence_qi')) {
            resultats.evolution_trouvee = this.rechercherEvolution(memory, conversations);
        }
        
        // 🎯 Recherche patterns
        resultats.patterns_detectes = this.rechercherPatterns(conversations, declenchement.mots_detectes);
        
        // 📊 Calcul résultats
        resultats.resultats_trouves =
            resultats.conversations_trouvees.length +
            resultats.elements_memoire_trouves.length +
            resultats.patterns_detectes.length +
            (resultats.evolution_trouvee ? 1 : 0) +
            (resultats.aide_utilisateur ? 1 : 0) +
            (resultats.recherche_cascade ? 1 : 0) +
            (resultats.formations_trouvees ? 1 : 0);

        return resultats;
    }
    
    // 💾 INJECTION RÉSULTATS DANS MÉMOIRE
    injecterResultatsDansMemoire(memory, resultatsRecherche) {
        // 🎯 Injection contexte de recherche pour l'agent
        memory.contexte_recherche_active = {
            timestamp: Date.now(),
            type_recherche: resultatsRecherche.type_recherche,
            resultats_disponibles: resultatsRecherche.resultats_trouves,
            conversations_pertinentes: resultatsRecherche.conversations_trouvees.slice(0, 5),
            evolution_disponible: resultatsRecherche.evolution_trouvee,
            patterns_detectes: resultatsRecherche.patterns_detectes.slice(0, 3),
            aide_utilisateur: resultatsRecherche.aide_utilisateur, // 🆘 AIDE SPÉCIALISÉE
            recherche_cascade: resultatsRecherche.recherche_cascade, // 👤 RECHERCHE CASCADE
            formations_trouvees: resultatsRecherche.formations_trouvees, // 📚 FORMATIONS
            instructions_agent: [
                "🧠 RECHERCHE MÉMOIRE DÉCLENCHÉE",
                "📚 Utilise contexte_recherche_active pour répondre",
                "🔍 Référence les conversations_pertinentes trouvées",
                "📈 Mentionne l'évolution si disponible",
                "🎯 Montre que tu te SOUVIENS vraiment",
                "💡 Sois spécifique sur ce que tu as trouvé"
            ]
        };
        
        // 💾 Sauvegarde
        this.ecrireMemoire(memory);
        
        console.log(`💾 RÉSULTATS INJECTÉS dans mémoire pour l'agent`);
    }
    
    // 🔧 FONCTIONS UTILITAIRES
    
    normaliserTexte(texte) {
        return texte.toLowerCase()
            .replace(/[àáâãäå]/g, 'a')
            .replace(/[èéêë]/g, 'e')
            .replace(/[ìíîï]/g, 'i')
            .replace(/[òóôõö]/g, 'o')
            .replace(/[ùúûü]/g, 'u')
            .replace(/[ç]/g, 'c')
            .replace(/[^a-z0-9\s]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    }
    
    detecterMotsCategorie(texte, categorie) {
        const mots = this.declencheurs[categorie] || [];
        const motsDetectes = [];
        
        mots.forEach(mot => {
            if (texte.includes(mot)) {
                motsDetectes.push(mot);
            }
        });
        
        return motsDetectes;
    }
    
    calculerPoidsCategorie(categorie) {
        const poids = {
            'memoire_directe': 0.4,
            'questions_passe': 0.3,
            'references_historiques': 0.2,
            'questions_specifiques': 0.3,
            'continuite': 0.25,
            'intelligence_qi': 0.2,
            'aide_memoire': 0.5,        // 🆘 POIDS ÉLEVÉ pour aide mémoire
            'expressions_doute': 0.4,   // 🤔 POIDS ÉLEVÉ pour doutes
            'demandes_recherche': 0.45, // 🔍 POIDS ÉLEVÉ pour recherches
            'demandes_eclaircissement': 0.35, // 💡 POIDS MOYEN pour éclaircissements
            'formations': 0.4,          // 📚 POIDS ÉLEVÉ pour formations
            'appel_nom': 0.6            // 👤 POIDS TRÈS ÉLEVÉ pour appel nom
        };

        return poids[categorie] || 0.1;
    }
    
    genererContexteRecherche(types, texte) {
        const contexte = [];
        
        if (types.includes('memoire_directe')) {
            contexte.push('recherche_souvenirs_directs');
        }
        
        if (types.includes('questions_passe')) {
            contexte.push('recherche_conversations_passees');
        }
        
        if (types.includes('intelligence_qi')) {
            contexte.push('recherche_evolution_qi');
        }
        
        if (types.includes('continuite')) {
            contexte.push('recherche_continuite');
        }
        
        // Extraction mots-clés du texte
        const motsCles = texte.match(/\b\w{4,}\b/g) || [];
        contexte.push(...motsCles.slice(0, 5));
        
        return contexte;
    }
    
    rechercherDansConversations(conversations, contexte) {
        return conversations
            .filter(conv => {
                const texteConv = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`.toLowerCase();
                return contexte.some(mot => texteConv.includes(mot.toLowerCase()));
            })
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, 10);
    }
    
    rechercherDansZonesThermiques(zones, contexte) {
        const elements = [];
        
        Object.values(zones).forEach(zone => {
            if (zone.entries) {
                zone.entries.forEach(entry => {
                    const texteEntry = (entry.content || '').toLowerCase();
                    if (contexte.some(mot => texteEntry.includes(mot.toLowerCase()))) {
                        elements.push(entry);
                    }
                });
            }
        });
        
        return elements.slice(0, 5);
    }
    
    rechercherEvolution(memory, conversations) {
        const qiValues = conversations
            .filter(conv => conv.qi_niveau > 0)
            .sort((a, b) => a.timestamp - b.timestamp)
            .map(conv => ({ timestamp: conv.timestamp, qi: conv.qi_niveau }));
        
        if (qiValues.length < 2) return null;
        
        return {
            qi_debut: qiValues[0].qi,
            qi_actuel: qiValues[qiValues.length - 1].qi,
            evolution: qiValues[qiValues.length - 1].qi - qiValues[0].qi,
            progression: qiValues
        };
    }
    
    rechercherPatterns(conversations, motsDetectes) {
        const patterns = [];
        
        // Pattern fréquence mots
        const frequences = {};
        conversations.forEach(conv => {
            const texte = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`.toLowerCase();
            motsDetectes.forEach(mot => {
                if (texte.includes(mot)) {
                    frequences[mot] = (frequences[mot] || 0) + 1;
                }
            });
        });
        
        Object.entries(frequences).forEach(([mot, freq]) => {
            if (freq > 1) {
                patterns.push({ type: 'frequence_mot', mot, frequence: freq });
            }
        });
        
        return patterns;
    }

    // 🆘 RECHERCHE SPÉCIALISÉE POUR AIDER L'UTILISATEUR
    rechercherPourAiderUtilisateur(conversations, contexteRecherche, motsDetectes) {
        console.log(`🆘 RECHERCHE D'AIDE UTILISATEUR activée`);

        const aide = {
            suggestions: [],
            informations_trouvees: [],
            contexte_pertinent: [],
            recommandations: []
        };

        // 🔍 Recherche par mots-clés dans le contexte
        const motsClesUtilisateur = this.extraireMotsClesUtilisateur(contexteRecherche);

        // 📚 Recherche conversations contenant ces mots-clés
        const conversationsPertinentes = conversations.filter(conv => {
            const texteConv = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`.toLowerCase();
            return motsClesUtilisateur.some(mot => texteConv.includes(mot.toLowerCase()));
        });

        // 🎯 Analyse des conversations trouvées
        conversationsPertinentes.forEach(conv => {
            // Informations trouvées
            aide.informations_trouvees.push({
                date: new Date(conv.timestamp).toLocaleString(),
                entree: conv.contenu_entree?.substring(0, 100),
                sortie: conv.contenu_sortie?.substring(0, 100),
                pertinence: this.calculerPertinenceAide(conv, motsClesUtilisateur)
            });

            // Contexte pertinent
            if (conv.qi_niveau > 0) {
                aide.contexte_pertinent.push(`QI était à ${conv.qi_niveau} le ${new Date(conv.timestamp).toLocaleDateString()}`);
            }
        });

        // 💡 Génération suggestions
        aide.suggestions = this.genererSuggestions(conversationsPertinentes, motsDetectes);

        // 🎯 Recommandations
        aide.recommandations = this.genererRecommandations(conversationsPertinentes, contexteRecherche);

        console.log(`🆘 AIDE GÉNÉRÉE: ${aide.informations_trouvees.length} infos, ${aide.suggestions.length} suggestions`);

        return aide;
    }

    // 🔍 EXTRACTION MOTS-CLÉS UTILISATEUR
    extraireMotsClesUtilisateur(contexte) {
        // Filtrer les mots de recherche et garder les mots significatifs
        return contexte.filter(mot =>
            !mot.includes('recherche_') &&
            mot.length > 3
        );
    }

    // 📊 CALCUL PERTINENCE AIDE
    calculerPertinenceAide(conversation, motsCles) {
        let pertinence = 0;
        const texte = `${conversation.contenu_entree || ''} ${conversation.contenu_sortie || ''}`.toLowerCase();

        motsCles.forEach(mot => {
            if (texte.includes(mot.toLowerCase())) {
                pertinence += 0.2;
            }
        });

        // Bonus pour conversations récentes
        const ageHeures = (Date.now() - conversation.timestamp) / (1000 * 60 * 60);
        if (ageHeures < 24) pertinence += 0.3;
        else if (ageHeures < 168) pertinence += 0.1; // 1 semaine

        // Bonus pour conversations avec QI
        if (conversation.qi_niveau > 0) pertinence += 0.2;

        return Math.min(pertinence, 1.0);
    }

    // 💡 GÉNÉRATION SUGGESTIONS
    genererSuggestions(conversations, motsDetectes) {
        const suggestions = [];

        if (conversations.length === 0) {
            suggestions.push("Je n'ai pas trouvé d'information correspondante dans ma mémoire.");
            suggestions.push("Peux-tu me donner plus de contexte pour t'aider ?");
            return suggestions;
        }

        // Suggestions basées sur les conversations trouvées
        const conversationRecente = conversations.sort((a, b) => b.timestamp - a.timestamp)[0];
        if (conversationRecente) {
            suggestions.push(`La dernière fois que nous avons parlé de cela, c'était le ${new Date(conversationRecente.timestamp).toLocaleDateString()}`);
        }

        // Suggestions basées sur l'évolution
        const conversationsAvecQI = conversations.filter(c => c.qi_niveau > 0);
        if (conversationsAvecQI.length > 1) {
            const qiMin = Math.min(...conversationsAvecQI.map(c => c.qi_niveau));
            const qiMax = Math.max(...conversationsAvecQI.map(c => c.qi_niveau));
            suggestions.push(`Mon QI a évolué de ${qiMin} à ${qiMax} pendant ces discussions`);
        }

        // Suggestions basées sur les patterns
        const themesFrequents = this.extraireThemesFrequents(conversations);
        if (themesFrequents.length > 0) {
            suggestions.push(`Nous avons souvent parlé de: ${themesFrequents.join(', ')}`);
        }

        return suggestions;
    }

    // 🎯 GÉNÉRATION RECOMMANDATIONS
    genererRecommandations(conversations, contexte) {
        const recommandations = [];

        if (conversations.length > 0) {
            recommandations.push("Je peux te donner plus de détails sur ces conversations");
            recommandations.push("Veux-tu que je te rappelle le contexte complet ?");

            if (conversations.some(c => c.qi_niveau > 0)) {
                recommandations.push("Je peux aussi te montrer comment j'ai évolué pendant ces échanges");
            }
        } else {
            recommandations.push("Essaie de me donner d'autres mots-clés");
            recommandations.push("Ou décris-moi le contexte de ce dont tu te souviens");
        }

        return recommandations;
    }

    // 🎯 EXTRACTION THÈMES FRÉQUENTS
    extraireThemesFrequents(conversations) {
        const themes = {};

        conversations.forEach(conv => {
            const texte = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`.toLowerCase();

            // Thèmes techniques
            if (texte.includes('qi') || texte.includes('intelligence')) themes.intelligence = (themes.intelligence || 0) + 1;
            if (texte.includes('mémoire') || texte.includes('souvenir')) themes.memoire = (themes.memoire || 0) + 1;
            if (texte.includes('évolution') || texte.includes('progrès')) themes.evolution = (themes.evolution || 0) + 1;
            if (texte.includes('neurone') || texte.includes('connexion')) themes.neurones = (themes.neurones || 0) + 1;
            if (texte.includes('thermique') || texte.includes('température')) themes.thermique = (themes.thermique || 0) + 1;
        });

        return Object.entries(themes)
            .filter(([, freq]) => freq > 1)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 3)
            .map(([theme]) => theme);
    }

    surveillerEntrees() {
        // Cette fonction sera appelée par l'agent principal
        // pour vérifier les nouvelles entrées
    }
    
    analyserPatternsDeclenchement() {
        // Analyse des patterns de déclenchement pour améliorer la détection
    }
    
    compterDeclencheurs() {
        let total = 0;
        Object.values(this.declencheurs).forEach(categorie => {
            total += categorie.length;
        });
        return total;
    }

    // 👤 RECHERCHE CASCADE (Mémoire → Connaissances → Internet)
    effectuerRechercheCascade(contexteRecherche, memory, conversations) {
        console.log(`👤 RECHERCHE CASCADE ACTIVÉE pour appel nom`);

        const cascade = {
            etape_1_memoire: null,
            etape_2_connaissances: null,
            etape_3_internet: null,
            etape_active: 1,
            resultats_trouves: false
        };

        // 🔍 ÉTAPE 1: Recherche dans mémoire thermique
        const resultatsMemoire = this.rechercherDansConversations(conversations, contexteRecherche);

        if (resultatsMemoire.length > 0) {
            cascade.etape_1_memoire = {
                resultats: resultatsMemoire,
                nombre: resultatsMemoire.length,
                source: "memoire_thermique"
            };
            cascade.resultats_trouves = true;
            cascade.etape_active = 1;
            console.log(`✅ ÉTAPE 1: ${resultatsMemoire.length} résultats trouvés dans mémoire thermique`);
        } else {
            // 🧠 ÉTAPE 2: Recherche dans connaissances propres
            cascade.etape_2_connaissances = {
                message: "Recherche dans mes connaissances intégrées...",
                source: "connaissances_propres",
                disponible: true
            };
            cascade.etape_active = 2;
            console.log(`⚠️ ÉTAPE 1: Rien trouvé - Passage ÉTAPE 2: Connaissances propres`);

            // 🌐 ÉTAPE 3: Préparation recherche Internet (si nécessaire)
            cascade.etape_3_internet = {
                message: "Si aucun résultat, recherche Internet MPC disponible",
                source: "internet_mpc",
                disponible: true
            };
            console.log(`📡 ÉTAPE 3: Recherche Internet MPC en standby`);
        }

        return cascade;
    }

    // 📚 RECHERCHE FORMATIONS
    rechercherFormations(conversations, memory, contexteRecherche) {
        console.log(`📚 RECHERCHE FORMATIONS activée`);

        const formations = {
            formations_memoire: [],
            formations_disponibles: [],
            recommandations: []
        };

        // 🔍 Recherche formations dans conversations
        const conversationsFormations = conversations.filter(conv => {
            const texte = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`.toLowerCase();
            return this.config.mots_cles_formations.some(mot => texte.includes(mot.toLowerCase()));
        });

        formations.formations_memoire = conversationsFormations.map(conv => ({
            date: new Date(conv.timestamp).toLocaleDateString(),
            contenu: conv.contenu_entree?.substring(0, 100),
            reponse: conv.contenu_sortie?.substring(0, 100)
        }));

        // 📚 Formations disponibles (exemple)
        formations.formations_disponibles = [
            "Formation Intelligence Artificielle",
            "Formation Mémoire Thermique",
            "Formation Agents Autonomes",
            "Formation DeepSeek R1",
            "Formation Systèmes Cognitifs"
        ];

        // 💡 Recommandations
        if (formations.formations_memoire.length > 0) {
            formations.recommandations.push("Tu as déjà suivi des formations, veux-tu continuer ?");
        } else {
            formations.recommandations.push("Je peux te proposer des formations adaptées à tes besoins");
        }

        console.log(`📚 FORMATIONS: ${formations.formations_memoire.length} en mémoire, ${formations.formations_disponibles.length} disponibles`);

        return formations;
    }

    // 🔧 FONCTION MANQUANTE AJOUTÉE
    estMotVide(mot) {
        const motsVides = [
            'dans', 'avec', 'pour', 'sans', 'sous', 'vers', 'chez', 'entre',
            'depuis', 'pendant', 'avant', 'après', 'contre', 'selon', 'très',
            'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were'
        ];

        return motsVides.includes(mot.toLowerCase()) || mot.length < 4;
    }

    // 💾 GESTION FICHIERS
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }
    
    ecrireMemoire(memory) {
        try {
            fs.writeFileSync(this.memoryFile, JSON.stringify(memory, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur écriture mémoire:`, error.message);
            return false;
        }
    }
    
    lireConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error(`❌ Erreur lecture conversations:`, error.message);
            return [];
        }
    }
}

// 🚀 EXPORT POUR UTILISATION PAR L'AGENT
module.exports = DetecteurDeclenchementMemoire;

// 🎯 DÉMARRAGE SI EXÉCUTÉ DIRECTEMENT
if (require.main === module) {
    const detecteur = new DetecteurDeclenchementMemoire();
    
    console.log(`\n🧠 DÉTECTEUR DÉCLENCHEMENT MÉMOIRE ACTIF !`);
    console.log(`🔍 Prêt à détecter les demandes de souvenirs`);
    console.log(`🚀 Déclenchement automatique de la recherche mémoire`);
    
    // 🎯 Test interface
    process.stdin.on('data', (data) => {
        const input = data.toString().trim();
        
        if (input.startsWith('test:')) {
            const texte = input.substring(5);
            console.log(`\n🔍 TEST DÉTECTION:`);
            const resultat = detecteur.detecterDeclenchementMemoire(texte);
            console.log(JSON.stringify(resultat, null, 2));
        } else if (input === 'help') {
            console.log(`\n🧠 COMMANDES DÉTECTEUR:`);
            console.log(`   test:votre phrase - Tester la détection`);
            console.log(`   help - Cette aide`);
        }
    });
}
