<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔌 Test MCP Simple</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ffff;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0,255,255,0.1);
            border: 1px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: #00ff00;
        }
        
        .test-area {
            background: rgba(0,0,0,0.5);
            border: 1px solid #00ffff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        button {
            background: rgba(0,255,0,0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
        }
        
        button:hover {
            background: rgba(0,255,0,0.3);
        }
        
        textarea {
            width: 100%;
            height: 100px;
            background: rgba(0,255,255,0.1);
            border: 1px solid #00ffff;
            color: #00ffff;
            padding: 10px;
            border-radius: 5px;
            font-family: inherit;
            margin: 10px 0;
        }
        
        .log {
            background: rgba(0,0,0,0.8);
            border: 1px solid #ffff00;
            color: #ffff00;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 Test MCP Simple</h1>
        
        <div class="test-area">
            <h3>📡 Test Serveur MCP</h3>
            <button onclick="testerServeurMCP()">🔌 Tester MCP Status</button>
            <button onclick="testerActualites()">📰 Tester Actualités</button>
            <button onclick="testerMeteo()">🌤️ Tester Météo</button>
            <div id="mcp-log" class="log"></div>
        </div>
        
        <div class="test-area">
            <h3>🚀 Test Injection Script</h3>
            <button onclick="chargerScript()">📥 Charger Script MCP</button>
            <button onclick="testerBouton()">🔍 Vérifier Bouton</button>
            <div id="script-log" class="log"></div>
        </div>
        
        <div class="test-area">
            <h3>💬 Simulation Interface</h3>
            <textarea id="test-input" placeholder="Zone de test pour simulation..."></textarea>
            <button onclick="simulerEnvoi()">🚀 Simuler Envoi</button>
            <div id="sim-log" class="log"></div>
        </div>
        
        <div class="test-area">
            <h3>📊 Status Global</h3>
            <button onclick="statusComplet()">📊 Status Complet</button>
            <div id="status-log" class="log"></div>
        </div>
    </div>

    <script>
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const time = new Date().toLocaleTimeString();
            element.innerHTML += `[${time}] ${message}<br>`;
            element.scrollTop = element.scrollHeight;
        }
        
        async function testerServeurMCP() {
            log('mcp-log', '🔌 Test serveur MCP...');
            try {
                const response = await fetch('http://127.0.0.1:8086/mcp/status');
                const data = await response.json();
                log('mcp-log', `✅ MCP OK: ${data.mcp_server} - APIs: ${data.apis_disponibles.length}`);
            } catch (error) {
                log('mcp-log', `❌ Erreur MCP: ${error.message}`);
            }
        }
        
        async function testerActualites() {
            log('mcp-log', '📰 Test actualités...');
            try {
                const response = await fetch('http://127.0.0.1:8086/mcp/news');
                const data = await response.json();
                log('mcp-log', `✅ Actualités OK: ${data.actualites.length} articles`);
                log('mcp-log', `Premier: ${data.actualites[0].titre}`);
            } catch (error) {
                log('mcp-log', `❌ Erreur actualités: ${error.message}`);
            }
        }
        
        async function testerMeteo() {
            log('mcp-log', '🌤️ Test météo...');
            try {
                const response = await fetch('http://127.0.0.1:8086/mcp/weather?city=Paris');
                const data = await response.json();
                log('mcp-log', `✅ Météo OK: ${data.ville} - ${data.donnees.temperature}°C`);
            } catch (error) {
                log('mcp-log', `❌ Erreur météo: ${error.message}`);
            }
        }
        
        async function chargerScript() {
            log('script-log', '📥 Chargement script MCP...');
            try {
                const script = document.createElement('script');
                script.src = 'http://127.0.0.1:8086/ajouter_mcp_simple.js';
                script.onload = () => {
                    log('script-log', '✅ Script MCP chargé avec succès');
                };
                script.onerror = () => {
                    log('script-log', '❌ Erreur chargement script');
                };
                document.head.appendChild(script);
            } catch (error) {
                log('script-log', `❌ Erreur: ${error.message}`);
            }
        }
        
        function testerBouton() {
            log('script-log', '🔍 Recherche bouton MCP...');
            const boutons = document.querySelectorAll('button');
            let mcpTrouve = false;
            
            boutons.forEach(btn => {
                if (btn.innerHTML.includes('🔌 MCP')) {
                    mcpTrouve = true;
                    log('script-log', '✅ Bouton MCP trouvé !');
                }
            });
            
            if (!mcpTrouve) {
                log('script-log', '❌ Bouton MCP non trouvé');
            }
        }
        
        function simulerEnvoi() {
            const input = document.getElementById('test-input');
            const message = input.value.trim();
            
            if (!message) {
                log('sim-log', '❌ Aucun message à envoyer');
                return;
            }
            
            log('sim-log', `💬 Simulation envoi: "${message}"`);
            
            // Simuler détection MCP
            if (message.toLowerCase().includes('actualité')) {
                log('sim-log', '📰 → Détection: Actualités');
            } else if (message.toLowerCase().includes('météo')) {
                log('sim-log', '🌤️ → Détection: Météo');
            } else if (message.toLowerCase().includes('recherche')) {
                log('sim-log', '🌐 → Détection: Recherche');
            } else {
                log('sim-log', '💬 → Détection: Message normal');
            }
            
            input.value = '';
        }
        
        async function statusComplet() {
            log('status-log', '📊 Vérification status complet...');
            
            // Test MCP
            try {
                const mcpResponse = await fetch('http://127.0.0.1:8086/mcp/status');
                log('status-log', mcpResponse.ok ? '✅ Serveur MCP: OK' : '❌ Serveur MCP: KO');
            } catch {
                log('status-log', '❌ Serveur MCP: Inaccessible');
            }
            
            // Test llama.cpp
            try {
                const llamaResponse = await fetch('http://127.0.0.1:8080/health');
                log('status-log', llamaResponse.ok ? '✅ llama.cpp: OK' : '❌ llama.cpp: KO');
            } catch {
                log('status-log', '❌ llama.cpp: Inaccessible');
            }
            
            // Test script
            try {
                const scriptResponse = await fetch('http://127.0.0.1:8086/ajouter_mcp_simple.js');
                log('status-log', scriptResponse.ok ? '✅ Script MCP: OK' : '❌ Script MCP: KO');
            } catch {
                log('status-log', '❌ Script MCP: Inaccessible');
            }
            
            log('status-log', '📊 Vérification terminée');
        }
        
        // Auto-test au chargement
        window.onload = function() {
            setTimeout(() => {
                log('status-log', '🚀 Auto-test au démarrage...');
                statusComplet();
            }, 1000);
        };
    </script>
</body>
</html>
