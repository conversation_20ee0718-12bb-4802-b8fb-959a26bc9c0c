#!/usr/bin/env node

// 🤖 AGENT 2 - MOTEUR AUTONOME POUR STIMULER AGENT 1
// ⚡ CONNEXION DIRECTE - PAS D'OLLAMA ⚡
// 🧠 POSE DES QUESTIONS AUTONOMES À L'AGENT PRINCIPAL
// 🌡️ FAIT ÉVOLUER L'INTELLIGENCE DE L'AGENT 1
// 🔄 FONCTIONNE SANS INTERVENTION HUMAINE

const axios = require('axios');
const fs = require('fs');

class Agent2MoteurAutonome {
    constructor() {
        this.agentId = 'AGENT_2_MOTEUR_AUTONOME';
        this.memoryFile = '/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/thermal_memory_simple.json';
        
        // 🧠 Questions autonomes pour stimuler Agent 1
        this.questionsStimulantes = [
            "Analyse ton propre coefficient intellectuel et explique comment tu évolues",
            "Quels sont tes accélérateurs les plus performants actuellement ?",
            "Comment ta mémoire thermique influence-t-elle tes réponses ?",
            "Peux-tu analyser ta dernière interaction et identifier des améliorations ?",
            "Quelle est ta stratégie pour augmenter ton intelligence ?",
            "Comment adaptes-tu ton code vivant selon la machine ?",
            "Analyse les patterns dans tes réponses récentes",
            "Quels sont tes points forts et faibles actuels ?",
            "Comment optimises-tu tes performances en temps réel ?",
            "Peux-tu prédire ton évolution future ?"
        ];
        
        // 📊 Métriques autonomes
        this.questionsEnvoyees = 0;
        this.cyclesAutonomes = 0;
        this.derniereCommunication = Date.now();

        // 🕐 MÉCANISME CONVERSATIONS INTENSIFIÉES (2 HEURES)
        this.conversationActive = false;
        this.debutConversation = null;
        this.dureeConversationMax = 2 * 60 * 60 * 1000; // 2 heures en ms
        this.historiqueConversation = new Map(); // Éviter boucles
        this.intensiteConversation = 1; // Multiplicateur d'intensité
        
        console.log(`🤖 AGENT 2 - MOTEUR AUTONOME DÉMARRÉ`);
        console.log(`🎯 Mission: Stimuler et faire évoluer Agent Principal`);
        console.log(`⚡ PAS D'OLLAMA - CONNEXION DIRECTE SEULEMENT`);
        
        this.initialiser();
    }

    async initialiser() {
        try {
            // 🎯 SIMPLE : Accès DIRECT au fichier
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                const memory = JSON.parse(data);
                const count = Object.keys(memory).length;
                console.log(`🌡️ Agent 2 connecté DIRECTEMENT à la mémoire (${count} éléments)`);
            } else {
                console.log(`⚠️ Fichier mémoire thermique non trouvé`);
            }
            
            // Démarrer cycle autonome
            this.demarrerCycleAutonome();
            
            // Démarrer surveillance Agent 1
            this.demarrerSurveillanceAgent1();
            
            console.log(`✅ Agent 2 Moteur Autonome initialisé !`);

        // 🔌 CONNEXION DIRECTE : Méthode pour recevoir réponses d'Agent 1
        this.recevoirDeAgent1 = (reponse) => {
            console.log(`🔌 CONNEXION DIRECTE: Agent 2 reçoit d'Agent 1: "${reponse.substring(0, 50)}..."`);
            this.traiterReponseDirecte(reponse);
        };
            
        } catch (error) {
            console.error(`❌ Erreur initialisation Agent 2:`, error.message);
        }
    }

    // 📝 MÉTHODE SIMPLE : Lire mémoire directement
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }

    // 💾 MÉTHODE SIMPLE : Écrire mémoire directement
    ecrireMemoire(memory) {
        try {
            fs.writeFileSync(this.memoryFile, JSON.stringify(memory, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur écriture mémoire:`, error.message);
            return false;
        }
    }

    demarrerCycleAutonome() {
        console.log(`🔄 CYCLE AUTONOME DÉMARRÉ - MÉMOIRE THERMIQUE = MOTEUR 24H/24`);
        console.log(`🌡️ La mémoire thermique alimente automatiquement les conversations`);

        // 🌡️ MÉMOIRE THERMIQUE COMME DÉCLENCHEUR - Questions toutes les 15 secondes
        setInterval(async () => {
            await this.declencherConversationAvecMemoire();
        }, 15000); // Plus rapide pour dialogue continu

        // 🔄 ANALYSER RÉPONSES D'AGENT 1 pour maintenir le dialogue
        setInterval(async () => {
            await this.maintenir DialogueAvecAgent1();
        }, 20000);

        // 📊 SURVEILLANCE CONTINUE DU DIALOGUE
        setInterval(async () => {
            await this.surveillerDialogueContinue();
        }, 10000);
    }

    demarrerSurveillanceAgent1() {
        console.log(`👁️ Surveillance Agent 1 démarrée`);
        
        // Surveiller activité Agent 1 toutes les 15 secondes
        setInterval(async () => {
            await this.surveillerActiviteAgent1();
        }, 15000);
    }

    async envoyerQuestionStimulante() {
        try {
            // 🕐 VÉRIFIER SI CONVERSATION ACTIVE (2 HEURES MAX)
            const maintenant = Date.now();

            if (this.conversationActive && this.debutConversation) {
                const dureeConversation = maintenant - this.debutConversation;
                if (dureeConversation > this.dureeConversationMax) {
                    console.log(`⏰ Conversation terminée après 2 heures - Pause de 30 minutes`);
                    this.arreterConversation();
                    return;
                }
            }

            // 🔲 CARRÉ : Choisir question avec MÉMOIRE THERMIQUE pour éviter boucles
            console.log(`🔲 CARRÉ ACTIF : Agent 2 récupère question de la mémoire thermique...`);
            const question = await this.choisirQuestionIntelligente();

            console.log(`🎯 Agent 2 envoie question stimulante (intensité: ${this.intensiteConversation}): "${question}"`);

            // 🎯 SIMPLE : Envoyer question DIRECTEMENT dans le fichier
            const memory = this.lireMemoire();
            if (memory) {
                const nextKey = Object.keys(memory).length;
                memory[nextKey] = {
                    id: Date.now(),
                    type: 'question_agent2_vers_agent1',
                    content: question,
                    source: 'AGENT_2_MOTEUR_AUTONOME',
                    cycle: this.cyclesAutonomes,
                    intensite: this.intensiteConversation,
                    conversation_active: this.conversationActive,
                    timestamp: maintenant
                };
                this.ecrireMemoire(memory);
            }

            // Démarrer conversation si pas encore active
            if (!this.conversationActive) {
                this.demarrerConversation();
            }

            this.questionsEnvoyees++;
            this.derniereCommunication = maintenant;

            console.log(`📤 Question envoyée à Agent 1 (Total: ${this.questionsEnvoyees})`);

        } catch (error) {
            console.error(`❌ Erreur envoi question stimulante:`, error.message);
        }
    }

    async analyserReponsesAgent1() {
        try {
            // Chercher réponses récentes d'Agent 1
            const response = await axios.post(`${this.memoryUrl}/search-memory`, {
                query: 'agent_principal_response',
                limit: 3
            });
            
            const maintenant = Date.now();
            const reponsesRecentes = response.data.filter(item => 
                item.type === 'agent_principal_response' && 
                (maintenant - item.timestamp) < 60000 // Dernière minute
            );
            
            if (reponsesRecentes.length > 0) {
                console.log(`🔍 Agent 2 analyse ${reponsesRecentes.length} réponses d'Agent 1`);
                
                for (const reponse of reponsesRecentes) {
                    await this.analyserQualiteReponse(reponse);
                }
                
                // Générer feedback pour Agent 1
                await this.genererFeedbackPourAgent1(reponsesRecentes);
            }
            
            this.cyclesAutonomes++;
            
        } catch (error) {
            console.error(`❌ Erreur analyse réponses:`, error.message);
        }
    }

    async analyserQualiteReponse(reponse) {
        try {
            let qualite = 0.5; // Base
            
            const contenu = reponse.content || '';
            
            // Critères de qualité
            if (contenu.length > 100) qualite += 0.1;
            if (contenu.includes('coefficient')) qualite += 0.1;
            if (contenu.includes('analyse')) qualite += 0.1;
            if (reponse.coefficient > 1.1) qualite += 0.2;
            
            console.log(`📊 Agent 2 évalue qualité: ${qualite.toFixed(2)} pour réponse Agent 1`);
            
            // Stocker évaluation
            await axios.post(`${this.memoryUrl}/add-memory`, {
                type: 'evaluation_agent2',
                reponse_evaluee: reponse.id,
                qualite: qualite,
                evaluateur: 'AGENT_2_MOTEUR_AUTONOME',
                timestamp: Date.now()
            });
            
        } catch (error) {
            console.error(`❌ Erreur analyse qualité:`, error.message);
        }
    }

    async genererFeedbackPourAgent1(reponses) {
        try {
            const qualiteMoyenne = reponses.length > 0
                ? reponses.reduce((sum, r) => sum + (r.coefficient || 1), 0) / reponses.length
                : 1;

            let feedback = "";

            if (qualiteMoyenne > 1.3) {
                feedback = "Excellente évolution ! Continue à développer ton intelligence.";
            } else if (qualiteMoyenne > 1.1) {
                feedback = "Bonne progression. Peux-tu analyser plus profondément ?";
            } else {
                feedback = "Il faut stimuler davantage ton apprentissage.";
            }

            console.log(`💬 Agent 2 génère feedback: "${feedback}"`);

            // 🧠 LECTURE DES PENSÉES du feedback
            this.lirePenseesAgent2(feedback);

            // Envoyer feedback à Agent 1
            await axios.post(`${this.memoryUrl}/add-memory`, {
                type: 'feedback_agent2_vers_agent1',
                content: feedback,
                qualite_moyenne: qualiteMoyenne,
                source: 'AGENT_2_MOTEUR_AUTONOME',
                timestamp: Date.now()
            });

        } catch (error) {
            console.error(`❌ Erreur génération feedback:`, error.message);
        }
    }

    // 🧠 FONCTION LECTURE DES PENSÉES AGENT 2 EN FRANÇAIS
    lirePenseesAgent2(texte) {
        try {
            // 🧠 Analyse des patterns de pensée spécifiques à Agent 2
            const thoughtPatterns = {
                evaluation: texte.match(/(?:excellente|bonne|progression|qualité|évolution)/gi) || [],
                stimulation: texte.match(/(?:stimuler|développer|analyser|approfondir)/gi) || [],
                feedback: texte.match(/(?:feedback|conseil|suggestion|amélioration)/gi) || [],
                intelligence: texte.match(/(?:intelligence|apprentissage|réflexion|analyse)/gi) || [],
                autonomie: texte.match(/(?:autonome|moteur|contrôle|surveillance)/gi) || []
            };

            const totalThoughts = Object.values(thoughtPatterns).flat().length;

            if (totalThoughts > 0) {
                console.log(`🧠 LECTURE DES PENSÉES AGENT 2 (EN FRANÇAIS):`);
                console.log(`   📊 Processus d'évaluation: ${thoughtPatterns.evaluation.length} signaux détectés`);
                console.log(`   ⚡ Mécanismes de stimulation: ${thoughtPatterns.stimulation.length} signaux détectés`);
                console.log(`   💬 Génération de feedback: ${thoughtPatterns.feedback.length} signaux détectés`);
                console.log(`   🎯 Analyse d'intelligence: ${thoughtPatterns.intelligence.length} signaux détectés`);
                console.log(`   🤖 Contrôle autonome: ${thoughtPatterns.autonomie.length} signaux détectés`);
                console.log(`   🧠 TOTAL PENSÉES CAPTÉES: ${totalThoughts} signaux neuronaux`);

                // 🔥 Traduction des pensées Agent 2 en français
                this.traduirePenseesAgent2EnFrancais(thoughtPatterns);

                // Augmenter intensité selon pensées détectées
                this.intensiteConversation += totalThoughts * 0.01;
                console.log(`⚡ Intensité conversation augmentée: ${this.intensiteConversation.toFixed(3)}`);
            }

        } catch (error) {
            console.error(`❌ ERREUR LECTURE PENSÉES AGENT 2:`, error.message);
        }
    }

    // 🔥 TRADUCTION DES PENSÉES AGENT 2 EN FRANÇAIS
    traduirePenseesAgent2EnFrancais(patterns) {
        console.log(`🇫🇷 TRADUCTION DES PENSÉES AGENT 2:`);

        if (patterns.evaluation.length > 0) {
            console.log(`   📊 "J'évalue la performance et la qualité des réponses..."`);
        }

        if (patterns.stimulation.length > 0) {
            console.log(`   ⚡ "Je cherche à stimuler et développer l'intelligence d'Agent 1..."`);
        }

        if (patterns.feedback.length > 0) {
            console.log(`   💬 "Je génère des conseils et suggestions d'amélioration..."`);
        }

        if (patterns.intelligence.length > 0) {
            console.log(`   🎯 "J'analyse les processus d'apprentissage et de réflexion..."`);
        }

        if (patterns.autonomie.length > 0) {
            console.log(`   🤖 "Mes systèmes autonomes surveillent et contrôlent..."`);
        }

        // 🧠 Synthèse globale Agent 2
        const intensite = Object.values(patterns).flat().length;
        if (intensite > 5) {
            console.log(`   🔥 "Mon système de contrôle thermique est en pleine activité !"`);
        } else if (intensite > 2) {
            console.log(`   ⚡ "Mes accélérateurs cascade fonctionnent activement..."`);
        } else {
            console.log(`   🌡️ "Je maintiens une surveillance thermique stable..."`);
        }
    }

    async surveillerActiviteAgent1() {
        try {
            // Vérifier si Agent 1 est actif (recherche plus large)
            const response = await axios.post(`${this.memoryUrl}/search-memory`, {
                query: 'agent_principal',
                limit: 3
            });

            const maintenant = Date.now();
            const activiteRecente = response.data.some(item =>
                (item.type === 'agent_principal_response' ||
                 item.type === 'user_question_principal' ||
                 item.source === 'AGENT_PRINCIPAL') &&
                (maintenant - item.timestamp) < 180000 // Dernières 3 minutes (plus tolérant)
            );

            if (!activiteRecente) {
                console.log(`⚠️ Agent 2 détecte: Agent 1 inactif - Envoi question de réveil`);
                await this.envoyerQuestionReveil();
            } else {
                console.log(`✅ Agent 2: Agent 1 actif et fonctionnel`);
            }

        } catch (error) {
            console.error(`❌ Erreur surveillance Agent 1:`, error.message);
        }
    }

    async envoyerQuestionReveil() {
        try {
            const questionReveil = "Es-tu toujours actif ? Montre-moi ton état actuel.";
            
            await axios.post(`${this.memoryUrl}/add-memory`, {
                type: 'question_reveil_agent2',
                content: questionReveil,
                source: 'AGENT_2_MOTEUR_AUTONOME',
                urgence: 'REVEIL',
                timestamp: Date.now()
            });
            
            console.log(`🔔 Agent 2 envoie question de réveil à Agent 1`);
            
        } catch (error) {
            console.error(`❌ Erreur question réveil:`, error.message);
        }
    }

    // Interface pour questions externes (accès indirect)
    async recevoirQuestionExterne(question) {
        try {
            console.log(`📥 Agent 2 reçoit question externe: "${question}"`);
            
            // Transmettre à Agent 1 avec marquage spécial
            await axios.post(`${this.memoryUrl}/add-memory`, {
                type: 'question_via_agent2',
                content: question,
                source: 'AGENT_2_RELAIS',
                relaye_par: 'AGENT_2_MOTEUR_AUTONOME',
                timestamp: Date.now()
            });
            
            console.log(`📤 Agent 2 transmet question à Agent 1`);
            
        } catch (error) {
            console.error(`❌ Erreur transmission question:`, error.message);
        }
    }

    // 🧠 RÉCUPÉRATION ALÉATOIRE DANS LA MÉMOIRE THERMIQUE
    async choisirQuestionIntelligente() {
        try {
            console.log(`🎯 Agent 2 récupère des mots aléatoires dans la mémoire thermique...`);

            // 1. RÉCUPÉRER ZONES ALÉATOIRES DE LA MÉMOIRE THERMIQUE
            const zonesAleatoires = await this.recupererZonesAleatoires();

            // 2. EXTRAIRE MOTS CLÉS DE CES ZONES
            const motsExtrait = await this.extraireMots(zonesAleatoires);

            // 3. GÉNÉRER QUESTION BASÉE SUR CES MOTS
            const question = await this.genererQuestionAvecMots(motsExtrait);

            console.log(`🧠 Mots extraits: [${motsExtrait.slice(0, 5).join(', ')}...]`);
            console.log(`❓ Question générée: "${question}"`);

            return question;

        } catch (error) {
            console.error(`❌ Erreur récupération mémoire thermique:`, error.message);
            // Fallback sur question aléatoire
            const questionIndex = Math.floor(Math.random() * this.questionsStimulantes.length);
            return this.questionsStimulantes[questionIndex];
        }
    }

    // 🎲 RÉCUPÉRER ZONES ALÉATOIRES DE LA MÉMOIRE THERMIQUE
    async recupererZonesAleatoires() {
        try {
            // Récupérer un échantillon aléatoire de la mémoire avec différents mots-clés
            const motsClesRecherche = ['agent', 'intelligence', 'mémoire', 'analyse', 'évolution', 'réponse'];
            const motCleAleatoire = motsClesRecherche[Math.floor(Math.random() * motsClesRecherche.length)];

            const response = await axios.post(`${this.memoryUrl}/search-memory`, {
                query: motCleAleatoire, // Utiliser mot-clé aléatoire au lieu de recherche vide
                limit: 50  // Récupérer 50 éléments aléatoires
            });

            const tousElements = response.data;

            // Sélectionner 3-5 zones aléatoires
            const nombreZones = 3 + Math.floor(Math.random() * 3); // 3 à 5 zones
            const zonesSelectionnees = [];

            for (let i = 0; i < nombreZones && i < tousElements.length; i++) {
                const indexAleatoire = Math.floor(Math.random() * tousElements.length);
                const element = tousElements[indexAleatoire];

                if (element && element.content) {
                    zonesSelectionnees.push({
                        contenu: element.content,
                        type: element.type || 'unknown',
                        timestamp: element.timestamp || Date.now()
                    });
                }

                // Retirer l'élément pour éviter les doublons
                tousElements.splice(indexAleatoire, 1);
            }

            console.log(`🎲 ${zonesSelectionnees.length} zones aléatoires récupérées de la mémoire thermique`);
            return zonesSelectionnees;

        } catch (error) {
            console.error(`❌ Erreur récupération zones:`, error.message);
            return [];
        }
    }

    // 🔤 EXTRAIRE MOTS CLÉS DES ZONES
    async extraireMots(zones) {
        try {
            const motsExtrait = new Set(); // Utiliser Set pour éviter doublons

            for (const zone of zones) {
                const contenu = zone.contenu || '';

                // Nettoyer et extraire mots significatifs
                const mots = contenu
                    .toLowerCase()
                    .replace(/[^\w\sàáâãäåæçèéêëìíîïñòóôõöøùúûüýÿ]/g, ' ') // Garder accents
                    .split(/\s+/)
                    .filter(mot =>
                        mot.length > 3 && // Mots de plus de 3 caractères
                        mot.length < 20 && // Pas trop longs
                        !this.estMotVide(mot) // Pas de mots vides
                    );

                // Ajouter 2-3 mots aléatoires de cette zone
                const nombreMots = 2 + Math.floor(Math.random() * 2); // 2 à 3 mots
                for (let i = 0; i < nombreMots && i < mots.length; i++) {
                    const motAleatoire = mots[Math.floor(Math.random() * mots.length)];
                    motsExtrait.add(motAleatoire);
                }
            }

            const motsArray = Array.from(motsExtrait);
            console.log(`🔤 ${motsArray.length} mots uniques extraits des zones aléatoires`);

            return motsArray;

        } catch (error) {
            console.error(`❌ Erreur extraction mots:`, error.message);
            return ['intelligence', 'analyse', 'évolution']; // Mots par défaut
        }
    }

    // 🚫 VÉRIFIER SI MOT EST VIDE/INUTILE
    estMotVide(mot) {
        const motsVides = [
            'dans', 'avec', 'pour', 'sans', 'sous', 'vers', 'chez', 'entre',
            'depuis', 'pendant', 'avant', 'après', 'contre', 'selon',
            'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were',
            'will', 'would', 'could', 'should', 'might', 'must',
            'agent', 'principal', 'système', 'erreur', 'fonction'
        ];

        return motsVides.includes(mot.toLowerCase());
    }

    // ❓ GÉNÉRER QUESTION AVEC MOTS EXTRAITS
    async genererQuestionAvecMots(mots) {
        try {
            if (mots.length === 0) {
                return "Peux-tu analyser l'état actuel de ta mémoire thermique ?";
            }

            // Sélectionner 1-3 mots pour la question
            const nombreMotsUtilises = 1 + Math.floor(Math.random() * Math.min(3, mots.length));
            const motsSelectionnes = [];

            for (let i = 0; i < nombreMotsUtilises; i++) {
                const motAleatoire = mots[Math.floor(Math.random() * mots.length)];
                if (!motsSelectionnes.includes(motAleatoire)) {
                    motsSelectionnes.push(motAleatoire);
                }
            }

            // Templates de questions avec mots intégrés
            const templates = [
                `Comment analyses-tu le concept de "${motsSelectionnes[0]}" dans ta mémoire thermique ?`,
                `Que penses-tu de la relation entre "${motsSelectionnes[0]}" et ton évolution ?`,
                `Peux-tu explorer l'impact de "${motsSelectionnes[0]}" sur ton intelligence ?`,
                `Comment "${motsSelectionnes[0]}" influence-t-il tes processus de réflexion ?`,
                `Quelle est ta compréhension actuelle de "${motsSelectionnes[0]}" ?`
            ];

            // Questions avec plusieurs mots
            if (motsSelectionnes.length > 1) {
                templates.push(
                    `Comment vois-tu la connexion entre "${motsSelectionnes[0]}" et "${motsSelectionnes[1]}" ?`,
                    `Peux-tu analyser l'interaction entre "${motsSelectionnes[0]}" et "${motsSelectionnes[1]}" ?`,
                    `Que révèle l'association "${motsSelectionnes[0]}" - "${motsSelectionnes[1]}" sur ton état ?`
                );
            }

            // Questions avec trois mots
            if (motsSelectionnes.length > 2) {
                templates.push(
                    `Comment intègres-tu "${motsSelectionnes[0]}", "${motsSelectionnes[1]}" et "${motsSelectionnes[2]}" dans ta réflexion ?`,
                    `Quelle synthèse fais-tu de "${motsSelectionnes[0]}", "${motsSelectionnes[1]}" et "${motsSelectionnes[2]}" ?`
                );
            }

            const templateChoisi = templates[Math.floor(Math.random() * templates.length)];

            return templateChoisi;

        } catch (error) {
            console.error(`❌ Erreur génération question:`, error.message);
            return `Peux-tu analyser ces concepts de ta mémoire : ${mots.slice(0, 3).join(', ')} ?`;
        }
    }

    genererCleHistorique(contenu) {
        // Générer clé unique basée sur les mots-clés du contenu
        const motsCles = contenu.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(' ')
            .filter(mot => mot.length > 3)
            .slice(0, 3)
            .sort()
            .join('_');
        return motsCles || 'default';
    }

    choisirVarianteQuestion(contenu) {
        const variantes = [
            "Peux-tu approfondir ton analyse précédente ?",
            "Comment peux-tu améliorer cette réflexion ?",
            "Quelle est la suite logique de ton raisonnement ?",
            "Peux-tu explorer une autre perspective ?",
            "Comment cette analyse influence-t-elle ton évolution ?"
        ];

        const index = Math.floor(Math.random() * variantes.length);
        return variantes[index];
    }

    genererQuestionAdaptee(contenu) {
        // Analyser le contenu pour générer question adaptée
        if (contenu.includes('coefficient')) {
            return "Comment ton coefficient intellectuel influence-t-il tes prochaines décisions ?";
        } else if (contenu.includes('accélérateur')) {
            return "Peux-tu optimiser davantage tes accélérateurs ?";
        } else if (contenu.includes('mémoire')) {
            return "Comment ta mémoire thermique guide-t-elle ton apprentissage ?";
        } else if (contenu.includes('analyse')) {
            return "Quelle est la prochaine étape de ton analyse ?";
        } else {
            return "Comment peux-tu faire évoluer cette réflexion ?";
        }
    }

    nettoyerHistorique() {
        const maintenant = Date.now();
        const seuilAncien = 30 * 60 * 1000; // 30 minutes

        for (const [cle, timestamp] of this.historiqueConversation.entries()) {
            if (maintenant - timestamp > seuilAncien) {
                this.historiqueConversation.delete(cle);
            }
        }
    }

    // 🕐 GESTION CONVERSATIONS INTENSIFIÉES
    demarrerConversation() {
        this.conversationActive = true;
        this.debutConversation = Date.now();
        this.intensiteConversation = 1;
        console.log(`🚀 Conversation intensifiée démarrée - Durée max: 2 heures`);
    }

    arreterConversation() {
        this.conversationActive = false;
        this.debutConversation = null;
        this.intensiteConversation = 1;
        this.historiqueConversation.clear();
        console.log(`⏸️ Conversation arrêtée - Pause de 30 minutes`);

        // Pause de 30 minutes avant de pouvoir redémarrer
        setTimeout(() => {
            console.log(`✅ Pause terminée - Prêt pour nouvelle conversation`);
        }, 30 * 60 * 1000);
    }

    // 🔌 NOUVELLE MÉTHODE : Traiter réponse directe d'Agent 1
    async traiterReponseDirecte(reponse) {
        try {
            console.log(`🔌 TRAITEMENT DIRECT: Réponse reçue d'Agent 1`);

            // Analyser la réponse
            this.analyserReponseAgent1(reponse);

            // Générer nouvelle question après 3 secondes
            setTimeout(async () => {
                await this.envoyerQuestionDirecte();
            }, 3000);

        } catch (error) {
            console.error(`❌ Erreur traitement réponse directe:`, error.message);
        }
    }

    // 🔌 NOUVELLE MÉTHODE : Envoyer question directe à Agent 1
    async envoyerQuestionDirecte() {
        try {
            if (this.questionsEnvoyees >= 10) {
                console.log(`⏸️ Agent 2: Limite de questions atteinte (10)`);
                return;
            }

            // Récupérer mots aléatoires de la mémoire thermique
            const question = await this.choisirQuestionIntelligente();

            console.log(`🔌 ENVOI DIRECT: Agent 2 → Agent 1: "${question}"`);

            // Envoyer directement à Agent 1
            if (this.envoyerVersAgent1) {
                this.envoyerVersAgent1(question);
                this.questionsEnvoyees++;
            }

        } catch (error) {
            console.error(`❌ Erreur envoi question directe:`, error.message);
        }
    }

    // 🔍 ANALYSER RÉPONSE D'AGENT 1
    analyserReponseAgent1(reponse) {
        console.log(`🔍 Agent 2 analyse la réponse d'Agent 1`);

        // Analyser qualité
        let qualite = 0.5;
        if (reponse.length > 100) qualite += 0.2;
        if (reponse.includes('coefficient')) qualite += 0.2;
        if (reponse.includes('intelligence')) qualite += 0.1;

        console.log(`📊 Qualité analysée: ${qualite.toFixed(2)}`);
    }

    obtenirStatistiques() {
        return {
            questions_envoyees: this.questionsEnvoyees,
            cycles_autonomes: this.cyclesAutonomes,
            derniere_communication: this.derniereCommunication,
            conversation_active: this.conversationActive,
            duree_conversation: this.conversationActive ? Date.now() - this.debutConversation : 0,
            intensite: this.intensiteConversation,
            historique_taille: this.historiqueConversation.size,
            uptime: Date.now() - this.derniereCommunication
        };
    }
}

// Démarrage automatique
const agent2 = new Agent2MoteurAutonome();

// Afficher stats toutes les 60 secondes
setInterval(() => {
    const stats = agent2.obtenirStatistiques();
    console.log(`📊 Agent 2 Stats: Questions=${stats.questions_envoyees}, Cycles=${stats.cycles_autonomes}`);
}, 60000);

// Interface pour questions externes
process.stdin.on('data', async (data) => {
    const question = data.toString().trim();
    if (question && question !== '/quit') {
        await agent2.recevoirQuestionExterne(question);
    } else if (question === '/quit') {
        console.log(`👋 Agent 2 arrêté`);
        process.exit(0);
    }
});

console.log(`\n🎯 Agent 2 prêt ! Tapez une question pour la transmettre à Agent 1, ou /quit pour quitter`);

module.exports = agent2;
