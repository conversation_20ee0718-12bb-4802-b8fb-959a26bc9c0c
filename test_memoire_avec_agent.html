<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mémoire avec Agent - JARVIS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .test-container {
            max-width: 1200px; margin: 0 auto;
            display: grid; grid-template-columns: 1fr 1fr; gap: 20px;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px; border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .conversation {
            background: rgba(0,0,0,0.3); padding: 15px;
            border-radius: 8px; margin: 10px 0;
            font-family: monospace; font-size: 14px;
        }
        .user { color: #4CAF50; }
        .agent { color: #2196F3; }
        .memory { color: #FF9800; }
        .error { color: #f44336; }
        .success { color: #4CAF50; }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 10px 20px; border-radius: 5px; cursor: pointer;
            margin: 5px; font-size: 14px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        #status {
            position: fixed; top: 20px; right: 20px;
            padding: 10px 20px; border-radius: 8px;
            font-weight: bold;
        }
        .online { background: rgba(76, 175, 80, 0.8); }
        .offline { background: rgba(244, 67, 54, 0.8); }
        #log {
            background: #000; padding: 15px; border-radius: 5px;
            font-family: monospace; font-size: 12px;
            height: 300px; overflow-y: auto;
            grid-column: 1 / -1; margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Mémoire Thermique avec Agent DeepSeek R1 8B</h1>
    
    <div id="status" class="offline">🔴 Vérification...</div>
    
    <div class="test-container">
        <div class="test-section">
            <h2>🔧 Test Connexion Agent</h2>
            <button onclick="testConnection()">Tester Connexion</button>
            <button onclick="testSimpleQuery()">Question Simple</button>
            <button onclick="restartServer()" id="restartBtn" disabled>Redémarrer Serveur</button>
            <div id="connectionResults"></div>
        </div>

        <div class="test-section">
            <h2>🧠 Test Mémoire Thermique</h2>
            <button onclick="testMemorySequence()">Test Séquence Mémoire</button>
            <button onclick="testMemoryTriggers()">Test Triggers</button>
            <button onclick="clearMemory()">Effacer Mémoire</button>
            <div id="memoryResults"></div>
        </div>

        <div class="test-section">
            <h2>💬 Conversation Test</h2>
            <input type="text" id="testInput" placeholder="Tapez votre message..." style="width: 100%; padding: 8px; margin-bottom: 10px; border-radius: 4px; border: none;">
            <button onclick="sendTestMessage()">Envoyer à l'Agent</button>
            <button onclick="toggleMemory()" id="memoryToggle">🧠 Mémoire OFF</button>
            <div id="conversationResults"></div>
        </div>

        <div class="test-section">
            <h2>📊 Résultats</h2>
            <div id="finalResults">
                <div>Tests en attente...</div>
            </div>
        </div>
    </div>

    <div id="log"></div>

    <script>
        // Variables globales
        let isConnected = false;
        let memoryActive = false;
        let thermalMemory = [];
        let testResults = {
            connection: false,
            simpleQuery: false,
            memorySequence: false,
            memoryTriggers: false
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#fff', success: '#4CAF50', error: '#f44336', memory: '#FF9800' };
            logDiv.innerHTML += `<span style="color: ${colors[type]}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(connected) {
            isConnected = connected;
            const status = document.getElementById('status');
            status.textContent = connected ? '🟢 Agent Connecté' : '🔴 Agent Déconnecté';
            status.className = connected ? 'online' : 'offline';
            document.getElementById('restartBtn').disabled = connected;
        }

        // Test 1: Connexion à l'agent
        async function testConnection() {
            log('🔧 Test de connexion à DeepSeek R1 8B...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/health', { 
                    method: 'GET',
                    timeout: 3000 
                });
                
                if (response.ok) {
                    updateStatus(true);
                    testResults.connection = true;
                    log('✅ Connexion réussie au serveur DeepSeek R1 8B', 'success');
                    document.getElementById('connectionResults').innerHTML = 
                        '<div class="success">✅ Serveur accessible</div>';
                } else {
                    throw new Error(`Erreur HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus(false);
                testResults.connection = false;
                log(`❌ Connexion échouée: ${error.message}`, 'error');
                document.getElementById('connectionResults').innerHTML = 
                    `<div class="error">❌ ${error.message}</div>`;
            }
            
            updateFinalResults();
        }

        // Test 2: Question simple à l'agent
        async function testSimpleQuery() {
            if (!isConnected) {
                log('❌ Pas de connexion - testez d\'abord la connexion', 'error');
                return;
            }

            log('🤖 Test question simple à l\'agent...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: 'Dis juste "Bonjour" en français.',
                        n_predict: 20,
                        temperature: 0.3,
                        stream: false
                    })
                });

                if (!response.ok) throw new Error(`Erreur ${response.status}`);

                const data = await response.json();
                const reply = (data.content || data.text || '').trim();

                if (reply && reply.length > 0) {
                    testResults.simpleQuery = true;
                    log(`✅ Réponse reçue: "${reply}"`, 'success');
                    document.getElementById('connectionResults').innerHTML += 
                        `<div class="success">✅ Agent répond: "${reply}"</div>`;
                } else {
                    throw new Error('Réponse vide');
                }
            } catch (error) {
                testResults.simpleQuery = false;
                log(`❌ Question simple échouée: ${error.message}`, 'error');
                document.getElementById('connectionResults').innerHTML += 
                    `<div class="error">❌ ${error.message}</div>`;
            }
            
            updateFinalResults();
        }

        // Test 3: Séquence mémoire complète
        async function testMemorySequence() {
            if (!isConnected) {
                log('❌ Pas de connexion pour tester la mémoire', 'error');
                return;
            }

            log('🧠 Test séquence mémoire thermique...', 'memory');
            memoryActive = true;
            thermalMemory = [];

            const sequence = [
                'Bonjour, je m\'appelle Jean-Luc',
                'Tu te souviens de mon nom ?'
            ];

            try {
                // Première question
                log('📝 Étape 1: Présentation', 'memory');
                const response1 = await askAgentWithMemory(sequence[0]);
                saveThermalMemory(sequence[0], response1);
                log(`👤 "${sequence[0]}" → 🤖 "${response1}"`, 'info');

                // Attendre un peu
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Deuxième question avec mémoire
                log('📝 Étape 2: Test rappel mémoire', 'memory');
                const response2 = await askAgentWithMemory(sequence[1]);
                saveThermalMemory(sequence[1], response2);
                log(`👤 "${sequence[1]}" → 🤖 "${response2}"`, 'info');

                // Vérifier si l'agent se souvient
                if (response2.toLowerCase().includes('jean-luc') || 
                    response2.toLowerCase().includes('jean luc')) {
                    testResults.memorySequence = true;
                    log('✅ MÉMOIRE FONCTIONNE: L\'agent se souvient du nom !', 'success');
                    document.getElementById('memoryResults').innerHTML = 
                        '<div class="success">✅ Mémoire thermique fonctionnelle</div>';
                } else {
                    testResults.memorySequence = false;
                    log('❌ MÉMOIRE DÉFAILLANTE: L\'agent ne se souvient pas', 'error');
                    document.getElementById('memoryResults').innerHTML = 
                        '<div class="error">❌ Mémoire thermique non fonctionnelle</div>';
                }

            } catch (error) {
                testResults.memorySequence = false;
                log(`❌ Test mémoire échoué: ${error.message}`, 'error');
                document.getElementById('memoryResults').innerHTML = 
                    `<div class="error">❌ ${error.message}</div>`;
            }

            updateFinalResults();
        }

        // Fonction pour interroger l'agent avec mémoire
        async function askAgentWithMemory(message) {
            let prompt = message;
            
            // Ajouter contexte mémoire si disponible
            if (memoryActive && thermalMemory.length > 0) {
                const memoryContext = searchMemory(message);
                if (memoryContext) {
                    prompt = message + '\n\nCONTEXTE:\n' + memoryContext;
                    log(`🧠 Contexte mémoire ajouté: ${memoryContext.length} caractères`, 'memory');
                }
            }

            const response = await fetch('http://localhost:8000/completion', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    prompt: prompt,
                    n_predict: 100,
                    temperature: 0.7,
                    stream: false
                })
            });

            if (!response.ok) throw new Error(`Erreur ${response.status}`);

            const data = await response.json();
            return (data.content || data.text || '').trim();
        }

        // Sauvegarde mémoire thermique
        function saveThermalMemory(userMessage, agentResponse) {
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse
            };
            
            thermalMemory.push(entry);
            localStorage.setItem('jarvis_thermal_memory_test', JSON.stringify(thermalMemory));
            log(`💾 Sauvegardé en mémoire: "${userMessage}" → "${agentResponse.substring(0, 50)}..."`, 'memory');
        }

        // Recherche mémoire
        function searchMemory(query) {
            if (thermalMemory.length === 0) return '';
            
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                const results = thermalMemory.filter(entry => 
                    entry.user?.toLowerCase().includes('nom') || 
                    entry.user?.toLowerCase().includes('appelle')
                );
                
                if (results.length > 0) {
                    return results.map(r => `Précédemment: "${r.user}" → "${r.agent}"`).join('\n');
                }
            }
            
            return '';
        }

        // Test triggers
        async function testMemoryTriggers() {
            log('🎯 Test des triggers de mémoire...', 'memory');
            
            const triggers = [
                'tu te souviens de mon nom ?',
                'rappelle-toi ce que j\'ai dit',
                'comment je m\'appelle ?'
            ];
            
            let successCount = 0;
            for (const trigger of triggers) {
                const context = searchMemory(trigger);
                if (context && context.length > 0) {
                    successCount++;
                    log(`✅ Trigger détecté: "${trigger}"`, 'success');
                } else {
                    log(`❌ Trigger raté: "${trigger}"`, 'error');
                }
            }
            
            testResults.memoryTriggers = successCount >= 2;
            const percentage = (successCount / triggers.length) * 100;
            document.getElementById('memoryResults').innerHTML += 
                `<div class="${successCount >= 2 ? 'success' : 'error'}">
                    🎯 Triggers: ${successCount}/${triggers.length} (${percentage}%)
                </div>`;
            
            updateFinalResults();
        }

        // Conversation interactive
        async function sendTestMessage() {
            const input = document.getElementById('testInput');
            const message = input.value.trim();
            if (!message || !isConnected) return;

            input.value = '';
            log(`👤 Envoi: "${message}"`, 'info');

            try {
                const response = await askAgentWithMemory(message);
                log(`🤖 Réponse: "${response}"`, 'info');
                
                if (memoryActive) {
                    saveThermalMemory(message, response);
                }
                
                document.getElementById('conversationResults').innerHTML += 
                    `<div class="conversation">
                        <div class="user">👤 ${message}</div>
                        <div class="agent">🤖 ${response}</div>
                    </div>`;
                    
            } catch (error) {
                log(`❌ Erreur conversation: ${error.message}`, 'error');
            }
        }

        // Toggle mémoire
        function toggleMemory() {
            memoryActive = !memoryActive;
            const btn = document.getElementById('memoryToggle');
            btn.textContent = memoryActive ? '🧠 Mémoire ON' : '🧠 Mémoire OFF';
            btn.style.background = memoryActive ? '#FF9800' : '#4CAF50';
            log(`🧠 Mémoire ${memoryActive ? 'activée' : 'désactivée'}`, 'memory');
        }

        // Redémarrage serveur
        async function restartServer() {
            log('🔄 Tentative de redémarrage du serveur...', 'info');
            // Cette fonction nécessiterait des droits système
            alert('Redémarrez manuellement le serveur DeepSeek R1 8B');
        }

        // Effacer mémoire
        function clearMemory() {
            thermalMemory = [];
            localStorage.removeItem('jarvis_thermal_memory_test');
            document.getElementById('memoryResults').innerHTML = '';
            document.getElementById('conversationResults').innerHTML = '';
            log('🗑️ Mémoire effacée', 'memory');
        }

        // Mise à jour résultats finaux
        function updateFinalResults() {
            const results = document.getElementById('finalResults');
            const total = Object.values(testResults).filter(Boolean).length;
            const totalTests = Object.keys(testResults).length;
            
            results.innerHTML = `
                <h3>📊 Résultats des Tests</h3>
                <div>Connexion: ${testResults.connection ? '✅' : '❌'}</div>
                <div>Question Simple: ${testResults.simpleQuery ? '✅' : '❌'}</div>
                <div>Séquence Mémoire: ${testResults.memorySequence ? '✅' : '❌'}</div>
                <div>Triggers Mémoire: ${testResults.memoryTriggers ? '✅' : '❌'}</div>
                <div><strong>Score: ${total}/${totalTests} (${Math.round(total/totalTests*100)}%)</strong></div>
                <div style="margin-top: 10px;">
                    ${total === totalTests ? 
                        '<span class="success">🎉 Tous les tests réussis !</span>' : 
                        '<span class="error">⚠️ Certains tests ont échoué</span>'}
                </div>
            `;
        }

        // Event listeners
        document.getElementById('testInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendTestMessage();
        });

        // Initialisation
        log('🚀 Tests mémoire thermique avec agent initialisés', 'info');
        testConnection();
    </script>
</body>
</html>
