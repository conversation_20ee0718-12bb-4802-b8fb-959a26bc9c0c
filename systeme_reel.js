// 🤖 SYSTÈME RÉEL : DEEPSEEK R1-0528 + MÉMOIRE THERMIQUE
const fs = require('fs');
const http = require('http');

console.log('🤖 SYSTÈME RÉEL DEEPSEEK R1-0528 + MÉMOIRE THERMIQUE');
console.log('⚡ VRAI MODÈLE + VRAIE MÉMOIRE - PAS DE SIMULATION');

// Vérifier mémoire thermique
function verifierMemoireThermique() {
    const memoryFile = './thermal_memory_persistent.json';
    
    try {
        if (fs.existsSync(memoryFile)) {
            const data = fs.readFileSync(memoryFile, 'utf8');
            const memory = JSON.parse(data);
            
            const qi = memory.neural_system?.qi_level || 0;
            const neurones = memory.neural_system?.active_neurons || 0;
            const temperature = memory.neural_system?.neural_temperature || 0;
            
            console.log('🌡️ VRAIE MÉMOIRE THERMIQUE CONNECTÉE :');
            console.log(`   QI Level: ${qi}`);
            console.log(`   Neurones actifs: ${neurones}`);
            console.log(`   Température: ${temperature}`);
            
            return true;
        } else {
            console.log('❌ Fichier mémoire thermique non trouvé');
            return false;
        }
    } catch (error) {
        console.error('❌ Erreur mémoire thermique:', error.message);
        return false;
    }
}

// Vérifier vLLM
function verifierVLLM() {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 8000,
            path: '/v1/models',
            method: 'GET',
            timeout: 3000
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('🤖 VRAI DEEPSEEK R1 CONNECTÉ VIA VLLM ✅');
                    resolve(true);
                } catch (error) {
                    console.log('❌ Erreur parsing réponse vLLM');
                    resolve(false);
                }
            });
        });
        
        req.on('error', () => {
            console.log('❌ vLLM non accessible sur localhost:8000');
            console.log('   Démarrez vLLM avec: vllm serve "deepseek-ai/DeepSeek-R1-0528"');
            resolve(false);
        });
        
        req.on('timeout', () => {
            console.log('❌ Timeout connexion vLLM');
            resolve(false);
        });
        
        req.end();
    });
}

// Test système réel
async function testerSystemeReel() {
    console.log('\n🔍 Vérification connexions réelles...');
    
    const memoireOK = verifierMemoireThermique();
    const vllmOK = await verifierVLLM();
    
    if (memoireOK && vllmOK) {
        console.log('\n✅ TOUTES LES CONNEXIONS RÉELLES SONT OK !');
        console.log('🧲 Système prêt pour connexion magnétique réelle');
        console.log('\n🎯 Le système réel peut maintenant être utilisé !');
    } else {
        console.log('\n❌ Problème de connexions - Vérifiez vos services');
        
        if (!memoireOK) {
            console.log('   → Vérifiez le fichier thermal_memory_persistent.json');
        }
        if (!vllmOK) {
            console.log('   → Démarrez vLLM: vllm serve "deepseek-ai/DeepSeek-R1-0528"');
        }
    }
}

// Démarrage
testerSystemeReel();

