#!/bin/bash

# 🚀 SCRIPT DE LANCEMENT JARVIS R1 8B COMPLET
# Créé avec amour par Claude pour <PERSON>Luc

echo "🚀 ========================================"
echo "🚀 LANCEMENT JARVIS R1 8B COMPLET"
echo "🚀 MCP + Mémoire Thermique + Interface"
echo "🚀 ========================================"

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé"
    echo "📥 Installez Node.js depuis https://nodejs.org/"
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé"
    exit 1
fi

echo "✅ Node.js et npm détectés"

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    echo "📦 Installation des dépendances..."
    npm install
fi

echo "🔧 Vérification des dépendances..."
npm list --depth=0 2>/dev/null || {
    echo "📦 Mise à jour des dépendances..."
    npm install
}

echo "🚀 Lancement de JARVIS R1 8B COMPLET..."
echo ""
echo "🌐 Interfaces disponibles:"
echo "   • Application Electron: Se lance automatiquement"
echo "   • Interface MCP: http://localhost:3000/interface-mcp"
echo "   • API JARVIS: http://localhost:3000/api/"
echo "   • Serveur MCP: http://localhost:8086/"
echo ""
echo "🔌 Fonctionnalités:"
echo "   • 🧠 Mémoire Thermique Continue"
echo "   • 🌐 Accès Internet via MCP"
echo "   • 📰 Actualités 2025"
echo "   • 🌤️ Météo temps réel"
echo "   • 🔍 Recherche Web"
echo "   • 💬 Gestion Conversations"
echo "   • ⚙️ Configuration avancée"
echo ""
echo "🎯 Pour arrêter: Ctrl+C ou fermer la fenêtre"
echo ""

# Lancer JARVIS
npm start
