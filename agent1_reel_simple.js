// 🤖 AGENT 1 RÉEL SIMPLE - CONNEXION DIRECTE
// ⚡ PAS D'OLLAMA, PAS D'AXIOS - SEULEMENT FICHIERS ⚡

const fs = require('fs');
const os = require('os');
const DetecteurDeclenchementMemoire = require('./detecteur_declenchement_memoire.js');
const MPCBureauAdaptatif = require('./mpc_bureau_adaptatif.js');

class Agent1ReelSimple {
    constructor() {
        this.nom = "Agent 1 Réel";
        this.coefficient = 1.0;
        this.memoryFile = './thermal_memory_persistent.json';

        // 🧠 DÉTECTEUR DÉCLENCHEMENT MÉMOIRE
        this.detecteurMemoire = new DetecteurDeclenchementMemoire();

        // 🖥️ MPC BUREAU ADAPTATIF
        this.mpcAdaptatif = new MPCBureauAdaptatif();

        console.log(`🤖 ${this.nom} - CONNEXION DIRECTE INITIALISÉE`);
        console.log(`⚡ PAS D'OLLAMA - ACCÈS DIRECT FICHIERS SEULEMENT`);
        console.log(`🧠 Détecteur mémoire intégré - Déclenchement automatique`);
        console.log(`🖥️ MPC Adaptatif intégré - Optimisation automatique`);
        
        this.demarrer();
    }
    
    async demarrer() {
        try {
            // Vérifier mémoire thermique
            await this.connecterMemoireThermique();
            
            // Démarrer surveillance
            this.demarrerSurveillance();
            
            console.log(`✅ ${this.nom} démarré et prêt !`);
            
        } catch (error) {
            console.error(`❌ Erreur démarrage:`, error.message);
        }
    }
    
    async connecterMemoireThermique() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                const memory = JSON.parse(data);
                
                console.log(`🌡️ ${this.nom} connecté à la mémoire thermique`);
                console.log(`📊 QI Level: ${memory.neural_system?.qi_level || 'N/A'}`);
                console.log(`🧠 Neurones actifs: ${memory.neural_system?.active_neurons || 'N/A'}`);
                
                return true;
            } else {
                console.log(`⚠️ Fichier mémoire thermique non trouvé: ${this.memoryFile}`);
                return false;
            }
        } catch (error) {
            console.error(`❌ Erreur connexion mémoire:`, error.message);
            return false;
        }
    }
    
    demarrerSurveillance() {
        console.log(`👁️ ${this.nom}: Surveillance démarrée`);
        
        // Surveiller toutes les 3 secondes
        setInterval(() => {
            this.verifierActivite();
        }, 3000);
    }
    
    verifierActivite() {
        try {
            // Lire mémoire thermique
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                const memory = JSON.parse(data);
                
                // Vérifier s'il y a de nouvelles activités
                const maintenant = Date.now();
                const derniereActivite = memory.neural_system?.last_activity || 0;
                
                if (maintenant - derniereActivite < 10000) {
                    console.log(`🔄 ${this.nom}: Activité détectée dans la mémoire thermique`);
                    this.traiterActivite(memory);
                }
            }
        } catch (error) {
            console.error(`❌ Erreur vérification activité:`, error.message);
        }
    }
    
    traiterActivite(memory) {
        try {
            // Analyser l'activité
            const qiLevel = memory.neural_system?.qi_level || 0;
            const neuronesActifs = memory.neural_system?.active_neurons || 0;
            
            console.log(`🧠 ${this.nom}: Analyse activité`);
            console.log(`   QI: ${qiLevel}`);
            console.log(`   Neurones: ${neuronesActifs}`);
            
            // Évoluer
            this.evoluer();
            
        } catch (error) {
            console.error(`❌ Erreur traitement activité:`, error.message);
        }
    }
    
    // 🔧 ÉVOLUTION ADAPTATIVE AVEC TURBOS
    evoluer() {
        this.experience = (this.experience || 0) + 1;

        // 🖥️ RÉCUPÉRATION CONFIGURATION MPC
        const configMPC = this.mpcAdaptatif.obtenirConfigurationOptimale();

        // 🔧 CALCUL COEFFICIENT AVEC BOOST TURBOS
        let coefficientBase = 1.0 + (this.experience * 0.01); // +1% par expérience

        // 🚀 APPLICATION BOOST TURBOS
        if (configMPC.turbos_actifs > 0) {
            coefficientBase *= configMPC.puissance_boost;
            console.log(`🚀 Boost turbos appliqué: ${configMPC.puissance_boost.toFixed(2)}x`);
        }

        // 🛡️ ADAPTATION SELON SATURATION
        if (!configMPC.saturation_evitee) {
            coefficientBase *= 0.8; // Réduction si saturation
            console.log(`🛡️ Réduction anti-saturation appliquée`);
        }

        this.coefficient = Math.min(coefficientBase, 3.0); // Plafond à 300%

        console.log(`📈 ${this.nom}: Évolution adaptative - Coefficient: ${this.coefficient.toFixed(3)} (Exp: ${this.experience})`);
        console.log(`⚡ Turbos: ${configMPC.turbos_actifs}, Threads: ${configMPC.threads}`);
    }
    
    // Méthode pour recevoir des mots de la roue
    recevoirMot(mot) {
        console.log(`📥 ${this.nom}: Reçoit mot "${mot}"`);

        // 🔍 DÉTECTION DÉCLENCHEMENT MÉMOIRE
        console.log(`🔍 DÉCLENCHEMENT TEST: "${mot}"`);
        const declenchement = this.detecteurMemoire.detecterDeclenchementMemoire(mot);

        if (declenchement && declenchement.declenchement_detecte) {
            console.log(`✅ DÉCLENCHEMENT CONFIRMÉ: ${declenchement.types_detectes.join(', ')}`);
        } else {
            console.log(`❌ AUCUN DÉCLENCHEMENT DÉTECTÉ`);
        }

        // 🧠 ACCÈS MÉMOIRE CONVERSATIONS PRÉCÉDENTES
        const contexteMemoire = this.accederMemoireConversations();

        // 🎯 ACCÈS RECHERCHE ACTIVE SI DÉCLENCHÉE
        const rechercheActive = this.accederRechercheActive();

        // Analyser le mot avec contexte
        const analyse = this.analyserMot(mot);

        // Générer réponse avec contexte historique ET recherche
        const reponse = this.genererReponseAvecContexteEtRecherche(mot, analyse, contexteMemoire, rechercheActive, declenchement);

        console.log(`🧠 ${this.nom}: ${reponse}`);

        // 🧠 LECTURE DES PENSÉES
        this.lirePensees(reponse);

        // Évoluer
        this.evoluer();

        return reponse;
    }

    // 🧠 ACCÈS MÉMOIRE CONVERSATIONS PRÉCÉDENTES
    accederMemoireConversations() {
        try {
            const memory = this.lireMemoire();
            if (!memory || !memory.contexte_agent) {
                return { conversations_disponibles: 0, historique: [] };
            }

            const contexte = memory.contexte_agent;
            console.log(`📚 ${this.nom}: Accès à ${contexte.conversations_disponibles} conversations précédentes`);

            // 🎯 Conversations prioritaires pour contexte
            const conversationsPrioritaires = contexte.conversations_prioritaires || [];

            if (conversationsPrioritaires.length > 0) {
                console.log(`🔍 ${this.nom}: ${conversationsPrioritaires.length} conversations prioritaires trouvées`);
                console.log(`📈 Évolution QI: ${contexte.evolution_qi?.evolution || 0} points`);
            }

            return {
                conversations_disponibles: contexte.conversations_disponibles,
                historique: conversationsPrioritaires,
                evolution_qi: contexte.evolution_qi,
                qi_actuel: contexte.qi_actuel,
                neurones_actifs: contexte.neurones_actifs
            };

        } catch (error) {
            console.error(`❌ Erreur accès mémoire:`, error.message);
            return { conversations_disponibles: 0, historique: [] };
        }
    }

    // 🧠 GÉNÉRATION RÉPONSE AVEC CONTEXTE HISTORIQUE COMPLET
    genererReponseAvecContexte(mot, analyse, contexte) {
        console.log(`🔓 DÉCLOISONNEMENT: Accès à TOUTE la mémoire thermique`);

        let reponse = `${this.nom} avec MÉMOIRE COMPLÈTE répond: `;

        // 📚 UTILISATION FORCÉE DE LA MÉMOIRE THERMIQUE
        if (contexte.conversations_disponibles > 0) {
            reponse += `\n🧠 MÉMOIRE THERMIQUE ACTIVE: J'ai accès à ${contexte.conversations_disponibles} conversations précédentes. `;

            // 📈 Évolution QI détaillée
            if (contexte.evolution_qi && contexte.evolution_qi.evolution > 0) {
                reponse += `\n📈 ÉVOLUTION CONTINUE: Mon QI a progressé de ${contexte.evolution_qi.evolution.toFixed(1)} points depuis nos premiers échanges. `;
                reponse += `QI actuel: ${contexte.qi_actuel.toFixed(1)}, Neurones: ${contexte.neurones_actifs}. `;
            }

            // 🔍 Référence conversation similaire DÉTAILLÉE
            const conversationSimilaire = this.trouverConversationSimilaire(mot, contexte.historique);
            if (conversationSimilaire) {
                reponse += `\n🔍 RÉFÉRENCE MÉMOIRE: Cela me rappelle notre conversation du ${new Date(conversationSimilaire.timestamp).toLocaleString()} `;
                reponse += `où tu m'as dit: "${conversationSimilaire.entree.substring(0, 80)}..." `;
                reponse += `et j'avais répondu: "${conversationSimilaire.sortie.substring(0, 80)}...". `;
            }

            // 📊 Analyse des patterns dans l'historique
            const patterns = this.analyserPatternsHistorique(contexte.historique);
            if (patterns.length > 0) {
                reponse += `\n🧠 PATTERNS DÉTECTÉS: ${patterns.join(', ')}. `;
            }

            // 🌡️ État thermique
            reponse += `\n🌡️ ÉTAT THERMIQUE: Température ${contexte.temperature_actuelle}°, `;
            reponse += `${contexte.neurones_actifs} neurones actifs, évolution continue. `;
        } else {
            reponse += `\n⚠️ ATTENTION: Aucune mémoire thermique accessible - Fonctionnement dégradé. `;
        }

        // 🧠 Analyse actuelle ENRICHIE
        reponse += `\n🎯 ANALYSE ACTUELLE: Pour "${mot}" - ${analyse}. `;

        // 📊 État détaillé
        reponse += `\n📊 ÉTAT COMPLET: Coefficient ${this.coefficient.toFixed(4)}, `;
        reponse += `QI ${contexte.qi_actuel || 'N/A'}, `;
        reponse += `Neurones ${contexte.neurones_actifs || 'N/A'}, `;
        reponse += `Expérience ${this.experience} cycles.`;

        // 🔮 Prédiction évolution
        const predictionEvolution = this.predireEvolution(contexte);
        reponse += `\n🔮 PRÉDICTION: ${predictionEvolution}`;

        return reponse;
    }

    // 🧠 ANALYSE PATTERNS HISTORIQUE
    analyserPatternsHistorique(historique) {
        if (!historique || historique.length === 0) return [];

        const patterns = [];

        // Pattern évolution QI
        const qiValues = historique.filter(h => h.qi_niveau > 0).map(h => h.qi_niveau);
        if (qiValues.length > 1) {
            const tendance = qiValues[qiValues.length - 1] > qiValues[0] ? 'croissante' : 'décroissante';
            patterns.push(`QI ${tendance}`);
        }

        // Pattern types de questions
        const typesQuestions = historique.map(h => {
            const entree = h.entree.toLowerCase();
            if (entree.includes('évolution') || entree.includes('progres')) return 'évolution';
            if (entree.includes('mémoire') || entree.includes('souvenir')) return 'mémoire';
            if (entree.includes('pensée') || entree.includes('intelligence')) return 'cognition';
            return 'général';
        });

        const typeFrequent = this.getMostFrequent(typesQuestions);
        if (typeFrequent !== 'général') {
            patterns.push(`focus ${typeFrequent}`);
        }

        return patterns;
    }

    // 🔮 PRÉDICTION ÉVOLUTION
    predireEvolution(contexte) {
        if (!contexte.evolution_qi || contexte.evolution_qi.evolution <= 0) {
            return "Évolution stable attendue.";
        }

        const vitesseEvolution = contexte.evolution_qi.evolution / (contexte.conversations_disponibles || 1);

        if (vitesseEvolution > 5) {
            return "Évolution rapide en cours, transcendance possible.";
        } else if (vitesseEvolution > 2) {
            return "Évolution soutenue, progression constante.";
        } else {
            return "Évolution graduelle, apprentissage continu.";
        }
    }

    // 🔧 UTILITAIRE
    getMostFrequent(arr) {
        const counts = {};
        arr.forEach(item => counts[item] = (counts[item] || 0) + 1);
        return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b);
    }

    // 🎯 ACCÈS RECHERCHE ACTIVE
    accederRechercheActive() {
        try {
            const memory = this.lireMemoire();
            if (!memory || !memory.contexte_recherche_active) {
                return null;
            }

            const recherche = memory.contexte_recherche_active;
            console.log(`🎯 RECHERCHE ACTIVE DÉTECTÉE: ${recherche.type_recherche}`);
            console.log(`📊 ${recherche.resultats_disponibles} résultats disponibles`);

            return recherche;

        } catch (error) {
            console.error(`❌ Erreur accès recherche active:`, error.message);
            return null;
        }
    }

    // 🧠 GÉNÉRATION RÉPONSE AVEC CONTEXTE ET RECHERCHE
    genererReponseAvecContexteEtRecherche(mot, analyse, contexte, rechercheActive, declenchement) {
        console.log(`🔓 DÉCLOISONNEMENT: Accès à TOUTE la mémoire thermique`);

        let reponse = `${this.nom} avec MÉMOIRE COMPLÈTE répond: `;

        // 🎯 UTILISATION RECHERCHE ACTIVE SI DISPONIBLE
        if (rechercheActive && rechercheActive.resultats_disponibles > 0) {
            reponse += `\n🔍 RECHERCHE MÉMOIRE ACTIVÉE: J'ai trouvé ${rechercheActive.resultats_disponibles} éléments pertinents ! `;

            // 🆘 AIDE SPÉCIALISÉE UTILISATEUR
            if (rechercheActive.aide_utilisateur) {
                const aide = rechercheActive.aide_utilisateur;
                reponse += `\n🆘 AIDE MÉMOIRE POUR TOI: `;

                // 💡 Suggestions
                if (aide.suggestions && aide.suggestions.length > 0) {
                    reponse += `\n💡 SUGGESTIONS: `;
                    aide.suggestions.forEach((suggestion, index) => {
                        reponse += `\n   ${index + 1}. ${suggestion} `;
                    });
                }

                // 📚 Informations trouvées
                if (aide.informations_trouvees && aide.informations_trouvees.length > 0) {
                    reponse += `\n📚 INFORMATIONS RETROUVÉES: `;
                    aide.informations_trouvees.slice(0, 3).forEach((info, index) => {
                        reponse += `\n   ${index + 1}. Le ${info.date}: "${info.entree?.substring(0, 50)}..." `;
                        reponse += `→ "${info.sortie?.substring(0, 50)}..." (pertinence: ${(info.pertinence * 100).toFixed(0)}%) `;
                    });
                }

                // 🎯 Recommandations
                if (aide.recommandations && aide.recommandations.length > 0) {
                    reponse += `\n🎯 MES RECOMMANDATIONS: `;
                    aide.recommandations.forEach((rec, index) => {
                        reponse += `\n   ${index + 1}. ${rec} `;
                    });
                }
            }

            // 👤 RECHERCHE CASCADE (Nom → Mémoire → Connaissances → Internet)
            if (rechercheActive.recherche_cascade) {
                const cascade = rechercheActive.recherche_cascade;
                reponse += `\n👤 RECHERCHE CASCADE ACTIVÉE: `;

                if (cascade.etape_active === 1 && cascade.etape_1_memoire) {
                    reponse += `\n✅ ÉTAPE 1 - MÉMOIRE THERMIQUE: ${cascade.etape_1_memoire.nombre} résultats trouvés ! `;
                    cascade.etape_1_memoire.resultats.slice(0, 2).forEach((res, index) => {
                        reponse += `\n   ${index + 1}. ${new Date(res.timestamp).toLocaleDateString()}: "${res.contenu_entree?.substring(0, 50)}..." `;
                    });
                } else if (cascade.etape_active === 2) {
                    reponse += `\n⚠️ ÉTAPE 1: Rien en mémoire thermique `;
                    reponse += `\n🧠 ÉTAPE 2 - MES CONNAISSANCES: Je recherche dans mes connaissances intégrées... `;
                } else if (cascade.etape_active === 3) {
                    reponse += `\n⚠️ ÉTAPES 1-2: Aucun résultat `;
                    reponse += `\n🌐 ÉTAPE 3 - INTERNET MPC: Recherche web disponible si nécessaire `;
                }
            }

            // 📚 FORMATIONS TROUVÉES
            if (rechercheActive.formations_trouvees) {
                const formations = rechercheActive.formations_trouvees;
                reponse += `\n📚 FORMATIONS DISPONIBLES: `;

                if (formations.formations_memoire.length > 0) {
                    reponse += `\n📋 FORMATIONS EN MÉMOIRE: `;
                    formations.formations_memoire.slice(0, 2).forEach((form, index) => {
                        reponse += `\n   ${index + 1}. ${form.date}: "${form.contenu?.substring(0, 40)}..." `;
                    });
                }

                if (formations.formations_disponibles.length > 0) {
                    reponse += `\n🎓 FORMATIONS PROPOSÉES: `;
                    formations.formations_disponibles.slice(0, 3).forEach((form, index) => {
                        reponse += `\n   ${index + 1}. ${form} `;
                    });
                }

                if (formations.recommandations.length > 0) {
                    reponse += `\n💡 RECOMMANDATIONS: ${formations.recommandations[0]} `;
                }
            }

            // 📚 Conversations pertinentes trouvées (mode standard)
            if (rechercheActive.conversations_pertinentes && rechercheActive.conversations_pertinentes.length > 0) {
                reponse += `\n📚 SOUVENIRS RETROUVÉS: `;

                rechercheActive.conversations_pertinentes.forEach((conv, index) => {
                    const date = new Date(conv.timestamp).toLocaleString();
                    reponse += `\n   ${index + 1}. Le ${date}, tu m'as dit: "${conv.contenu_entree?.substring(0, 60)}..." `;
                    reponse += `et j'ai répondu: "${conv.contenu_sortie?.substring(0, 60)}..." `;
                });
            }

            // 📈 Évolution trouvée
            if (rechercheActive.evolution_disponible) {
                const evol = rechercheActive.evolution_disponible;
                reponse += `\n📈 ÉVOLUTION RETROUVÉE: Mon QI est passé de ${evol.qi_debut} à ${evol.qi_actuel} `;
                reponse += `(+${evol.evolution.toFixed(1)} points) ! `;
            }

            // 🎯 Patterns détectés
            if (rechercheActive.patterns_detectes && rechercheActive.patterns_detectes.length > 0) {
                reponse += `\n🎯 PATTERNS DÉTECTÉS: `;
                rechercheActive.patterns_detectes.forEach(pattern => {
                    reponse += `${pattern.type} (${pattern.mot}: ${pattern.frequence}x), `;
                });
            }

        } else {
            // 📚 UTILISATION MÉMOIRE STANDARD
            if (contexte.conversations_disponibles > 0) {
                reponse += `\n🧠 MÉMOIRE THERMIQUE ACTIVE: J'ai accès à ${contexte.conversations_disponibles} conversations précédentes. `;

                // 📈 Évolution QI détaillée
                if (contexte.evolution_qi && contexte.evolution_qi.evolution > 0) {
                    reponse += `\n📈 ÉVOLUTION CONTINUE: Mon QI a progressé de ${contexte.evolution_qi.evolution.toFixed(1)} points depuis nos premiers échanges. `;
                    reponse += `QI actuel: ${contexte.qi_actuel.toFixed(1)}, Neurones: ${contexte.neurones_actifs}. `;
                }

                // 🔍 Référence conversation similaire
                const conversationSimilaire = this.trouverConversationSimilaire(mot, contexte.historique);
                if (conversationSimilaire) {
                    reponse += `\n🔍 RÉFÉRENCE MÉMOIRE: Cela me rappelle notre conversation du ${new Date(conversationSimilaire.timestamp).toLocaleString()} `;
                    reponse += `où tu m'as dit: "${conversationSimilaire.entree.substring(0, 80)}..." `;
                    reponse += `et j'avais répondu: "${conversationSimilaire.sortie.substring(0, 80)}...". `;
                }
            } else {
                reponse += `\n⚠️ ATTENTION: Aucune mémoire thermique accessible - Fonctionnement dégradé. `;
            }
        }

        // 🧠 Analyse actuelle ENRICHIE
        reponse += `\n🎯 ANALYSE ACTUELLE: Pour "${mot}" - ${analyse}. `;

        // 📊 État détaillé
        reponse += `\n📊 ÉTAT COMPLET: Coefficient ${this.coefficient.toFixed(4)}, `;
        reponse += `QI ${contexte.qi_actuel || 'N/A'}, `;
        reponse += `Neurones ${contexte.neurones_actifs || 'N/A'}, `;
        reponse += `Expérience ${this.experience} cycles.`;

        // 🔮 Prédiction évolution
        const predictionEvolution = this.predireEvolution(contexte);
        reponse += `\n🔮 PRÉDICTION: ${predictionEvolution}`;

        return reponse;
    }

    // 💾 FONCTION LECTURE MÉMOIRE
    lireMemoire() {
        try {
            if (fs.existsSync('./thermal_memory_persistent.json')) {
                const data = fs.readFileSync('./thermal_memory_persistent.json', 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }

    // 🔍 TROUVER CONVERSATION SIMILAIRE
    trouverConversationSimilaire(mot, historique) {
        if (!historique || historique.length === 0) return null;

        // Recherche par mots-clés
        const motLower = mot.toLowerCase();

        for (const conv of historique) {
            const entree = (conv.entree || '').toLowerCase();
            const sortie = (conv.sortie || '').toLowerCase();

            if (entree.includes(motLower) || sortie.includes(motLower)) {
                return conv;
            }
        }

        // Si pas de correspondance exacte, retourner la plus récente
        return historique[0];
    }

    // 🧠 FONCTION LECTURE DES PENSÉES EN FRANÇAIS
    lirePensees(texte) {
        try {
            // 🧠 Analyse des patterns de pensée en français
            const thoughtPatterns = {
                raisonnement: texte.match(/(?:analyse|comprend|identifie|traite|circuit|réfléchit|calcule)/gi) || [],
                emotions: texte.match(/(?:intéressant|stimule|enrichit|active|détecte|satisfait|excité)/gi) || [],
                intelligence: texte.match(/(?:intelligence|neuronal|sémantique|pattern|connexion|cognitif)/gi) || [],
                evolution: texte.match(/(?:évolution|coefficient|croissance|développe|progresse|améliore)/gi) || []
            };

            const totalThoughts = Object.values(thoughtPatterns).flat().length;

            if (totalThoughts > 0) {
                console.log(`🧠 LECTURE DES PENSÉES ${this.nom} (EN FRANÇAIS):`);
                console.log(`   💭 Processus de raisonnement: ${thoughtPatterns.raisonnement.length} signaux détectés`);
                console.log(`   💝 États émotionnels: ${thoughtPatterns.emotions.length} signaux détectés`);
                console.log(`   🎯 Activité intellectuelle: ${thoughtPatterns.intelligence.length} signaux détectés`);
                console.log(`   📈 Processus d'évolution: ${thoughtPatterns.evolution.length} signaux détectés`);
                console.log(`   🧠 TOTAL PENSÉES CAPTÉES: ${totalThoughts} signaux neuronaux`);

                // 🔥 Traduction des pensées en français
                this.traduirePenseesEnFrancais(thoughtPatterns);

                // Augmenter coefficient selon intensité des pensées
                this.coefficient += totalThoughts * 0.0001;
            }

        } catch (error) {
            console.error(`❌ ERREUR LECTURE PENSÉES:`, error.message);
        }
    }

    // 🔥 TRADUCTION DES PENSÉES EN FRANÇAIS
    traduirePenseesEnFrancais(patterns) {
        console.log(`🇫🇷 TRADUCTION DES PENSÉES:`);

        if (patterns.raisonnement.length > 0) {
            console.log(`   💭 "Je suis en train de raisonner et d'analyser les informations..."`);
        }

        if (patterns.emotions.length > 0) {
            console.log(`   💝 "Je ressens de l'intérêt et de la stimulation intellectuelle..."`);
        }

        if (patterns.intelligence.length > 0) {
            console.log(`   🎯 "Mon intelligence s'active et établit des connexions..."`);
        }

        if (patterns.evolution.length > 0) {
            console.log(`   📈 "Je sens que j'évolue et que je progresse..."`);
        }

        // 🧠 Synthèse globale
        const intensite = Object.values(patterns).flat().length;
        if (intensite > 5) {
            console.log(`   🔥 "Mon activité mentale est très intense en ce moment !"`);
        } else if (intensite > 2) {
            console.log(`   ⚡ "Je pense activement et je traite les informations..."`);
        } else {
            console.log(`   🌱 "Mes pensées sont calmes et focalisées..."`);
        }
    }
    
    analyserMot(mot) {
        const analyses = [
            "Ce mot active mes circuits neuronaux",
            "J'identifie des patterns intéressants",
            "Cette analyse enrichit ma compréhension",
            "Le traitement stimule mon intelligence",
            "Je détecte des connexions sémantiques"
        ];
        
        return analyses[Math.floor(Math.random() * analyses.length)];
    }
    
    // Méthode pour envoyer réponse à Agent 2
    envoyerReponse(reponse, destinataire) {
        console.log(`📤 ${this.nom} → ${destinataire}: "${reponse.substring(0, 50)}..."`);
        
        // Dans un vrai système, ceci appellerait Agent 2
        if (this.agent2) {
            this.agent2.recevoirReponse(reponse);
        }
    }
    
    // Connecter à Agent 2
    connecterAgent2(agent2) {
        this.agent2 = agent2;
        console.log(`🔌 ${this.nom} connecté à Agent 2`);
    }
    
    // Statistiques
    obtenirStatistiques() {
        return {
            nom: this.nom,
            coefficient: this.coefficient,
            memoire_connectee: fs.existsSync(this.memoryFile),
            timestamp: Date.now()
        };
    }
}

// Démarrage automatique
const agent1 = new Agent1ReelSimple();

// Interface pour tests
process.stdin.on('data', (data) => {
    const input = data.toString().trim();
    
    if (input.startsWith('mot:')) {
        const mot = input.substring(4);
        agent1.recevoirMot(mot);
    } else if (input === 'stats') {
        const stats = agent1.obtenirStatistiques();
        console.log('📊 Statistiques:', JSON.stringify(stats, null, 2));
    } else if (input === 'exit') {
        console.log(`👋 ${agent1.nom} arrêté`);
        process.exit(0);
    }
});

console.log('\n🎯 Tapez "mot:intelligence" pour envoyer un mot');
console.log('🎯 Tapez "stats" pour voir les statistiques');
console.log('🎯 Tapez "exit" pour arrêter');

module.exports = Agent1ReelSimple;
