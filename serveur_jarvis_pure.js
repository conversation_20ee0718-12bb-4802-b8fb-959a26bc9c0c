// 🧠 SERVEUR JARVIS INTERFACE PURE
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🧠 SERVEUR JARVIS PURE - DÉMARRAGE');

const server = http.createServer((req, res) => {
    console.log(`📡 Requête: ${req.method} ${req.url}`);
    
    if (req.method === 'GET' && req.url === '/') {
        // 🏠 SERVIR INTERFACE PRINCIPALE
        try {
            const html = fs.readFileSync('./jarvis_interface_pure.html', 'utf8');
            
            res.writeHead(200, {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache'
            });
            res.end(html);
            
            console.log('✅ Interface JARVIS Pure servie');
            
        } catch (error) {
            console.error('❌ Erreur lecture interface:', error.message);
            res.writeHead(500);
            res.end('Erreur serveur');
        }
        
    } else if (req.method === 'POST' && req.url === '/api/llama') {
        // 🔗 PROXY VERS LLAMA.CPP
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                console.log('🔗 Redirection vers llama.cpp:', data.prompt?.substring(0, 50) + '...');
                
                // Redirection vers llama.cpp
                const options = {
                    hostname: '127.0.0.1',
                    port: 8080,
                    path: '/completion',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                const proxyReq = http.request(options, (proxyRes) => {
                    let responseData = '';
                    
                    proxyRes.on('data', chunk => {
                        responseData += chunk;
                    });
                    
                    proxyRes.on('end', () => {
                        res.writeHead(proxyRes.statusCode, {
                            'Content-Type': 'application/json',
                            'Access-Control-Allow-Origin': '*'
                        });
                        res.end(responseData);
                        console.log('✅ Réponse llama.cpp transmise');
                    });
                });
                
                proxyReq.on('error', (error) => {
                    console.error('❌ Erreur llama.cpp:', error.message);
                    res.writeHead(500, {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    });
                    res.end(JSON.stringify({
                        error: 'Connexion llama.cpp échouée',
                        content: 'Serveur llama.cpp non accessible'
                    }));
                });
                
                proxyReq.write(body);
                proxyReq.end();
                
            } catch (error) {
                console.error('❌ Erreur parsing JSON:', error.message);
                res.writeHead(400);
                res.end('Données invalides');
            }
        });
        
    } else if (req.method === 'OPTIONS') {
        // 🌐 CORS PREFLIGHT
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        });
        res.end();
        
    } else {
        // 🚫 404
        res.writeHead(404);
        res.end('Page non trouvée');
    }
});

// 🚀 DÉMARRAGE SERVEUR
const PORT = 8086;
server.listen(PORT, () => {
    console.log(`✅ SERVEUR JARVIS PURE ACTIF sur http://127.0.0.1:${PORT}`);
    console.log('🧠 Interface Pure - Aucune dépendance externe');
    console.log('🔗 Connexion llama.cpp disponible via API');
});

// 🛡️ GESTION ERREURS
server.on('error', (error) => {
    console.error('❌ Erreur serveur:', error.message);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt SERVEUR JARVIS PURE');
    server.close();
    process.exit(0);
});
