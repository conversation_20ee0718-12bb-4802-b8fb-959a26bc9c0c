// 🔌 CONNEXION DIRECTE SIMPLE ENTRE TES AGENTS
// ⚡ PAS D'OLLAMA - CONNEXION DIRECTE SEULEMENT ⚡

const fs = require('fs');

console.log('🔌 CONNEXION DIRECTE SIMPLE');
console.log('📤 UTILISATEUR → AGENT 2 → AGENT 1 → RÉPONSE → CONTINUE');

// Fichier de communication
const canalCommunication = './canal_agents.json';

// Initialiser canal
function initialiserCanal() {
    const canal = {
        messages: [],
        derniere_activite: Date.now(),
        agent1_actif: false,
        agent2_actif: false
    };
    
    fs.writeFileSync(canalCommunication, JSON.stringify(canal, null, 2));
    console.log('📁 Canal de communication initialisé');
}

// AGENT 2 SIMPLE
class Agent2Simple {
    constructor() {
        this.nom = "Agent 2";
        this.actif = true;
        console.log(`🤖 ${this.nom} initialisé`);
        this.demarrerSurveillance();
    }
    
    // Recevoir mot de l'utilisateur
    recevoirMot(mot) {
        console.log(`📥 ${this.nom}: Reçoit mot "${mot}" de l'utilisateur`);
        
        // Traiter et envoyer à Agent 1
        const messageAgent1 = `Agent 1, analyse ce mot: "${mot}"`;
        this.envoyerVersAgent1(messageAgent1);
    }
    
    // Envoyer vers Agent 1
    envoyerVersAgent1(message) {
        try {
            const canal = JSON.parse(fs.readFileSync(canalCommunication, 'utf8'));
            
            canal.messages.push({
                id: Date.now(),
                de: 'AGENT_2',
                vers: 'AGENT_1',
                message: message,
                timestamp: Date.now(),
                traite: false
            });
            
            canal.derniere_activite = Date.now();
            fs.writeFileSync(canalCommunication, JSON.stringify(canal, null, 2));
            
            console.log(`📤 ${this.nom} → Agent 1: "${message}"`);
            
        } catch (error) {
            console.error(`❌ Erreur envoi:`, error.message);
        }
    }
    
    // Recevoir réponse d'Agent 1
    recevoirReponseAgent1(reponse) {
        console.log(`📥 ${this.nom}: Reçoit réponse d'Agent 1`);
        console.log(`🔄 ${this.nom}: "${reponse.substring(0, 50)}..."`);
        
        // Continuer le dialogue
        setTimeout(() => {
            this.continuerDialogue();
        }, 3000);
    }
    
    // Continuer dialogue automatiquement
    continuerDialogue() {
        const questions = [
            "Peux-tu approfondir ton analyse ?",
            "Comment ton intelligence évolue-t-elle ?",
            "Que penses-tu de cette interaction ?",
            "Analyse ton coefficient actuel"
        ];
        
        const question = questions[Math.floor(Math.random() * questions.length)];
        console.log(`🔄 ${this.nom}: Continue le dialogue`);
        this.envoyerVersAgent1(question);
    }
    
    // Surveiller réponses d'Agent 1
    demarrerSurveillance() {
        setInterval(() => {
            this.verifierReponses();
        }, 2000);
    }
    
    verifierReponses() {
        try {
            if (!fs.existsSync(canalCommunication)) return;
            
            const canal = JSON.parse(fs.readFileSync(canalCommunication, 'utf8'));
            
            // Chercher réponses d'Agent 1 non traitées
            const reponsesAgent1 = canal.messages.filter(msg => 
                msg.de === 'AGENT_1' && 
                msg.vers === 'AGENT_2' && 
                !msg.traite
            );
            
            for (const reponse of reponsesAgent1) {
                this.recevoirReponseAgent1(reponse.message);
                
                // Marquer comme traité
                reponse.traite = true;
            }
            
            if (reponsesAgent1.length > 0) {
                fs.writeFileSync(canalCommunication, JSON.stringify(canal, null, 2));
            }
            
        } catch (error) {
            // Erreur silencieuse
        }
    }
}

// AGENT 1 SIMPLE
class Agent1Simple {
    constructor() {
        this.nom = "Agent 1";
        this.coefficient = 1.0;
        this.actif = true;
        console.log(`🤖 ${this.nom} initialisé`);
        this.demarrerSurveillance();
    }
    
    // Surveiller messages d'Agent 2
    demarrerSurveillance() {
        setInterval(() => {
            this.verifierMessages();
        }, 1500);
    }
    
    verifierMessages() {
        try {
            if (!fs.existsSync(canalCommunication)) return;
            
            const canal = JSON.parse(fs.readFileSync(canalCommunication, 'utf8'));
            
            // Chercher messages d'Agent 2 non traités
            const messagesAgent2 = canal.messages.filter(msg => 
                msg.de === 'AGENT_2' && 
                msg.vers === 'AGENT_1' && 
                !msg.traite
            );
            
            for (const message of messagesAgent2) {
                this.traiterMessage(message.message);
                
                // Marquer comme traité
                message.traite = true;
            }
            
            if (messagesAgent2.length > 0) {
                fs.writeFileSync(canalCommunication, JSON.stringify(canal, null, 2));
            }
            
        } catch (error) {
            // Erreur silencieuse
        }
    }
    
    // Traiter message d'Agent 2
    traiterMessage(message) {
        console.log(`📥 ${this.nom}: Reçoit "${message}"`);
        
        // Analyser et générer réponse
        this.coefficient += 0.01;
        
        const reponse = `${this.nom} répond: J'ai analysé votre demande. Mon coefficient est maintenant ${this.coefficient.toFixed(3)}. Intelligence en évolution continue.`;
        
        console.log(`🧠 ${this.nom}: Traitement terminé`);
        
        // Envoyer réponse à Agent 2
        this.envoyerVersAgent2(reponse);
    }
    
    // Envoyer vers Agent 2
    envoyerVersAgent2(reponse) {
        try {
            const canal = JSON.parse(fs.readFileSync(canalCommunication, 'utf8'));
            
            canal.messages.push({
                id: Date.now(),
                de: 'AGENT_1',
                vers: 'AGENT_2',
                message: reponse,
                timestamp: Date.now(),
                traite: false
            });
            
            canal.derniere_activite = Date.now();
            fs.writeFileSync(canalCommunication, JSON.stringify(canal, null, 2));
            
            console.log(`📤 ${this.nom} → Agent 2: Réponse envoyée`);
            
        } catch (error) {
            console.error(`❌ Erreur envoi:`, error.message);
        }
    }
}

// DÉMARRAGE
console.log('🚀 Initialisation connexion directe...');

// Initialiser canal
initialiserCanal();

// Créer agents
const agent2 = new Agent2Simple();
const agent1 = new Agent1Simple();

console.log('\n✅ AGENTS CONNECTÉS EN DIRECT !');
console.log('\n🎯 Tapez un mot pour démarrer le test:');
console.log('🎯 Exemple: "intelligence"');
console.log('🎯 Tapez "stop" pour arrêter');

// Interface utilisateur
process.stdin.on('data', (data) => {
    const input = data.toString().trim();
    
    if (input.toLowerCase() === 'stop') {
        console.log('⏸️ Arrêt de la connexion directe');
        process.exit(0);
    } else if (input.length > 0) {
        console.log(`\n👤 UTILISATEUR envoie: "${input}"`);
        agent2.recevoirMot(input);
    }
});

module.exports = { Agent1Simple, Agent2Simple };
