<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 TEST RÉEL - Mémoire Thermique JARVIS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 1000px; margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px; border-radius: 15px;
        }
        .test-result {
            padding: 15px; margin: 10px 0;
            border-radius: 8px; border-left: 4px solid;
            font-family: monospace;
        }
        .success { border-color: #4CAF50; background: rgba(76, 175, 80, 0.2); }
        .error { border-color: #f44336; background: rgba(244, 67, 54, 0.2); }
        .info { border-color: #2196F3; background: rgba(33, 150, 243, 0.2); }
        .warning { border-color: #FF9800; background: rgba(255, 152, 0, 0.2); }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 15px 30px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 18px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .verdict {
            text-align: center; font-size: 1.8em;
            padding: 30px; margin: 20px 0;
            border-radius: 15px; font-weight: bold;
        }
        .progress {
            background: rgba(255,255,255,0.2); border-radius: 10px;
            height: 20px; margin: 20px 0; overflow: hidden;
        }
        .progress-bar {
            background: #4CAF50; height: 100%; width: 0%;
            transition: width 0.5s ease;
        }
        #log {
            background: #000; color: #0f0; padding: 20px;
            border-radius: 10px; font-family: monospace;
            font-size: 14px; height: 400px; overflow-y: auto;
            margin: 20px 0; border: 2px solid #0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 TEST RÉEL - Mémoire Thermique JARVIS</h1>
        <p><strong>Objectif :</strong> Vérifier que la mémoire thermique fonctionne VRAIMENT avec DeepSeek R1 8B</p>

        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div style="text-align: center; margin: 10px 0;" id="progressText">Prêt pour le test</div>

        <button onclick="runRealTest()" id="testBtn">🚀 LANCER TEST RÉEL</button>
        <button onclick="clearResults()">🗑️ Effacer</button>

        <div id="log">[PRÊT] Cliquez sur "LANCER TEST RÉEL" pour commencer le test avec le vrai agent DeepSeek R1 8B</div>

        <div id="results"></div>
        <div id="verdict"></div>
    </div>

    <script>
        let thermalMemory = [];
        let testResults = [];
        let testInProgress = false;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#0f0', error: '#f44', success: '#4f4', warning: '#fa0' };
            logDiv.innerHTML += `<div style="color: ${colors[type]}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage, text) {
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }

        function addResult(title, message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${title}</strong><br>${message}`;
            resultsDiv.appendChild(resultDiv);
            testResults.push({ title, message, type, success: type === 'success' });
        }

        async function testServerConnection() {
            log('🔧 Test connexion serveur DeepSeek R1 8B...', 'info');
            updateProgress(10, 'Test connexion serveur...');
            
            try {
                const response = await fetch('http://localhost:8000/health', { 
                    method: 'GET',
                    timeout: 5000 
                });
                
                if (response.ok) {
                    log('✅ Serveur DeepSeek R1 8B accessible', 'success');
                    addResult('✅ Connexion Serveur', 'DeepSeek R1 8B est accessible et répond', 'success');
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Connexion échouée: ${error.message}`, 'error');
                addResult('❌ Connexion Serveur', `Erreur: ${error.message}`, 'error');
                return false;
            }
        }

        function testMemoryFunctions() {
            log('🧠 Test fonctions mémoire thermique...', 'info');
            updateProgress(30, 'Test fonctions mémoire...');
            
            try {
                // Reset mémoire
                thermalMemory = [];
                
                // Test sauvegarde
                saveThermalMemory('Agent1', 'Bonjour, je m\'appelle Jean-Luc', 'Bonjour Jean-Luc ! Ravi de vous rencontrer.');
                
                if (thermalMemory.length === 1) {
                    log('✅ Fonction saveThermalMemory: OK', 'success');
                } else {
                    throw new Error(`Attendu 1 entrée, trouvé ${thermalMemory.length}`);
                }
                
                // Test recherche
                const searchResult = searchMemory('tu te souviens de mon nom ?');
                if (searchResult && searchResult.includes('Jean-Luc')) {
                    log('✅ Fonction searchMemory: OK - Nom trouvé', 'success');
                    addResult('✅ Fonctions Mémoire', 'Sauvegarde et recherche fonctionnent correctement', 'success');
                    return true;
                } else {
                    throw new Error('Nom non trouvé dans la recherche');
                }
                
            } catch (error) {
                log(`❌ Fonctions mémoire: ${error.message}`, 'error');
                addResult('❌ Fonctions Mémoire', `Erreur: ${error.message}`, 'error');
                return false;
            }
        }

        async function testRealAgentMemory() {
            log('🤖 Test mémoire avec agent DeepSeek R1 8B RÉEL...', 'info');
            updateProgress(50, 'Test avec agent réel...');
            
            try {
                // Reset mémoire pour test propre
                thermalMemory = [];
                
                // Étape 1: Présentation
                log('📝 Étape 1: Présentation à l\'agent', 'info');
                const message1 = 'Bonjour, je m\'appelle Jean-Luc';
                
                updateProgress(60, 'Envoi présentation...');
                const response1 = await callRealAgent(message1);
                saveThermalMemory('Agent1', message1, response1);
                
                log(`👤 User: "${message1}"`, 'info');
                log(`🤖 Agent: "${response1}"`, 'info');
                
                // Attendre un peu
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Étape 2: Question avec mémoire
                log('📝 Étape 2: Test rappel mémoire', 'info');
                const message2 = 'Tu te souviens de mon nom ?';
                const memoryContext = searchMemory(message2);
                
                if (memoryContext && memoryContext.length > 0) {
                    log(`🧠 Contexte mémoire trouvé: ${memoryContext.length} caractères`, 'success');
                } else {
                    log('⚠️ Aucun contexte mémoire trouvé', 'warning');
                }
                
                const promptWithMemory = message2 + memoryContext;
                
                updateProgress(80, 'Test rappel mémoire...');
                const response2 = await callRealAgent(promptWithMemory);
                saveThermalMemory('Agent1', message2, response2);
                
                log(`👤 User: "${message2}"`, 'info');
                log(`🤖 Agent: "${response2}"`, 'info');
                
                // Vérification critique
                const remembersName = response2.toLowerCase().includes('jean-luc') || 
                                     response2.toLowerCase().includes('jean luc');
                
                if (remembersName) {
                    log('🎉 SUCCÈS TOTAL: L\'agent se souvient du nom !', 'success');
                    addResult('🎉 Mémoire Agent RÉEL', 
                             `L'agent se souvient parfaitement: "${response2.substring(0, 100)}..."`, 
                             'success');
                    return true;
                } else {
                    log('❌ ÉCHEC: L\'agent ne se souvient pas du nom', 'error');
                    addResult('❌ Mémoire Agent RÉEL', 
                             `L'agent ne se souvient pas: "${response2.substring(0, 100)}..."`, 
                             'error');
                    return false;
                }
                
            } catch (error) {
                log(`❌ Test agent réel échoué: ${error.message}`, 'error');
                addResult('❌ Test Agent RÉEL', `Erreur: ${error.message}`, 'error');
                return false;
            }
        }

        async function callRealAgent(prompt) {
            log(`📤 Envoi à DeepSeek R1 8B: "${prompt.substring(0, 50)}..."`, 'info');
            
            const response = await fetch('http://localhost:8000/completion', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    prompt: prompt,
                    n_predict: 150,
                    temperature: 0.7,
                    stream: false
                })
            });

            if (!response.ok) throw new Error(`Erreur serveur ${response.status}`);

            const data = await response.json();
            const reply = (data.content || data.text || '').trim();
            
            if (!reply) throw new Error('Réponse vide du serveur');
            
            log(`📥 Réponse reçue: "${reply.substring(0, 50)}..."`, 'success');
            return reply;
        }

        function saveThermalMemory(agent, userMessage, agentResponse) {
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                agent: agent,
                user: userMessage,
                response: agentResponse
            };
            
            thermalMemory.push(entry);
            log(`💾 Sauvegardé [${agent}]: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`, 'info');
        }

        function searchMemory(query) {
            if (thermalMemory.length === 0) return '';
            
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom', 'comment je', 'qui suis'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                const results = thermalMemory.filter(entry => 
                    entry.user?.toLowerCase().includes('appelle') || 
                    entry.user?.toLowerCase().includes('nom') ||
                    entry.user?.toLowerCase().includes('suis')
                );
                
                if (results.length > 0) {
                    return '\n\nCONTEXTE MÉMOIRE THERMIQUE:\n' + 
                           results.map(r => `- Précédemment: "${r.user}" → "${r.response}"`).join('\n') + 
                           '\nUtilise ce contexte pour répondre de manière cohérente.\n';
                }
            }
            
            return '';
        }

        async function runRealTest() {
            if (testInProgress) return;
            testInProgress = true;
            
            document.getElementById('testBtn').disabled = true;
            document.getElementById('results').innerHTML = '';
            document.getElementById('verdict').innerHTML = '';
            testResults = [];
            
            log('🚀 DÉBUT TEST RÉEL MÉMOIRE THERMIQUE AVEC DEEPSEEK R1 8B', 'success');
            log('=' .repeat(60), 'info');
            
            const startTime = Date.now();
            
            try {
                // Test 1: Connexion
                const connectionOK = await testServerConnection();
                
                // Test 2: Fonctions mémoire
                const functionsOK = testMemoryFunctions();
                
                // Test 3: Agent réel (seulement si connexion OK)
                let agentMemoryOK = false;
                if (connectionOK) {
                    agentMemoryOK = await testRealAgentMemory();
                } else {
                    addResult('⚠️ Test Agent RÉEL', 'Ignoré - serveur inaccessible', 'warning');
                }
                
                // Résultats finaux
                updateProgress(100, 'Test terminé !');
                const endTime = Date.now();
                const duration = (endTime - startTime) / 1000;
                
                log('=' .repeat(60), 'info');
                log(`🏁 TEST TERMINÉ en ${duration.toFixed(1)}s`, 'success');
                
                const successCount = testResults.filter(r => r.success).length;
                const totalTests = testResults.length;
                
                showFinalVerdict(connectionOK, functionsOK, agentMemoryOK, duration);
                
            } catch (error) {
                log(`❌ Test général échoué: ${error.message}`, 'error');
                updateProgress(100, 'Test échoué !');
                showFinalVerdict(false, false, false, 0);
            } finally {
                testInProgress = false;
                document.getElementById('testBtn').disabled = false;
            }
        }

        function showFinalVerdict(connection, functions, agentMemory, duration) {
            const verdictDiv = document.getElementById('verdict');
            
            let verdict = '';
            let className = '';
            
            if (connection && functions && agentMemory) {
                verdict = '🎉 PARFAIT ! Mémoire thermique 100% fonctionnelle avec DeepSeek R1 8B !';
                className = 'success';
                log('🎉 VERDICT: MÉMOIRE THERMIQUE PARFAITEMENT FONCTIONNELLE !', 'success');
            } else if (functions && connection) {
                verdict = '⚠️ Fonctions mémoire OK, mais problème avec l\'agent';
                className = 'warning';
                log('⚠️ VERDICT: Fonctions OK, problème agent', 'warning');
            } else if (functions) {
                verdict = '✅ Fonctions mémoire OK, problème de connexion serveur';
                className = 'warning';
                log('✅ VERDICT: Fonctions OK, serveur inaccessible', 'warning');
            } else {
                verdict = '❌ ÉCHEC ! Problèmes détectés dans la mémoire thermique';
                className = 'error';
                log('❌ VERDICT: MÉMOIRE THERMIQUE DÉFAILLANTE', 'error');
            }
            
            verdictDiv.innerHTML = `
                <div class="verdict ${className}">
                    ${verdict}
                    <div style="font-size: 0.6em; margin-top: 15px;">
                        Durée: ${duration.toFixed(1)}s | Tests: ${testResults.length}
                    </div>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('verdict').innerHTML = '';
            document.getElementById('log').innerHTML = '[EFFACÉ] Prêt pour un nouveau test';
            updateProgress(0, 'Prêt pour le test');
            testResults = [];
            thermalMemory = [];
        }

        // Initialisation
        log('🧪 Test RÉEL de mémoire thermique prêt', 'success');
        log('💡 Ce test utilise le VRAI agent DeepSeek R1 8B', 'info');
        log('🎯 Objectif: Vérifier que l\'agent se souvient vraiment', 'info');
    </script>
</body>
</html>
