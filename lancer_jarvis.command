#!/bin/bash

# 🤖 LANCEMENT JARVIS - Agent DeepSeek R1 8B
# <PERSON><PERSON><PERSON> - Raccourci bureau pour démarrer l'agent

echo "🚀 DÉMARRAGE JARVIS - Agent DeepSeek R1 8B (macOS)"
echo "📍 Répertoire: /Volumes/seagate/Louna_Electron_Latest"

# Aller dans le bon répertoire
cd "/Volumes/seagate/Louna_Electron_Latest"

# Vérifier que les fichiers existent
if [ ! -f "agent1_reel_simple.js" ]; then
    echo "❌ Erreur: agent1_reel_simple.js non trouvé"
    exit 1
fi

if [ ! -f "detecteur_declenchement_memoire.js" ]; then
    echo "❌ Erreur: detecteur_declenchement_memoire.js non trouvé"
    exit 1
fi

if [ ! -f "mpc_bureau_adaptatif.js" ]; then
    echo "❌ Erreur: mpc_bureau_adaptatif.js non trouvé"
    exit 1
fi

echo "✅ Fichiers trouvés"
echo "🍎 Adaptation macOS en cours..."
echo "🤖 Lancement de JARVIS avec MPC Adaptatif..."

# Lancer l'agent avec adaptation
node agent1_reel_simple.js
