// 🎯 SCRIPT POUR CORRIGER L'AFFICHAGE DU NOM DE L'AGENT
// À injecter dans l'interface llama.cpp

console.log('🎯 Correction du nom de l\'agent...');

function corrigerNomAgent() {
    // Chercher tous les éléments contenant "Send a message to start"
    const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    const nodesToReplace = [];
    let node;

    while (node = walker.nextNode()) {
        if (node.textContent.includes('Send a message to start')) {
            nodesToReplace.push(node);
        }
    }

    // Remplacer le texte
    nodesToReplace.forEach(node => {
        node.textContent = node.textContent.replace('Send a message to start', 'DeepSeek R1 8B Agent');
        console.log('✅ Nom agent corrigé');
    });
}

// Observer pour les changements dynamiques
const observer = new MutationObserver(() => {
    corrigerNomAgent();
});

observer.observe(document.body, { 
    childList: true, 
    subtree: true, 
    characterData: true 
});

// Correction immédiate
setTimeout(corrigerNomAgent, 500);
setTimeout(corrigerNomAgent, 1000);
setTimeout(corrigerNomAgent, 2000);

console.log('✅ Script de correction du nom agent activé');
