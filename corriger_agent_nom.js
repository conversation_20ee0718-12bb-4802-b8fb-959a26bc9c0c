// 🎯 SCRIPT POUR CORRIGER L'AFFICHAGE DU NOM DE L'AGENT
// À injecter dans l'interface llama.cpp interface_8080_reelle.html

console.log('🎯 Correction du nom de l\'agent pour interface_8080_reelle.html...');

function corrigerNomAgent() {
    let correctionEffectuee = false;

    // Méthode 1: Chercher tous les éléments texte
    const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let node;
    while (node = walker.nextNode()) {
        if (node.textContent && node.textContent.includes('Send a message to start')) {
            node.textContent = node.textContent.replace('Send a message to start', 'DeepSeek R1 8B Agent');
            console.log('✅ Nom agent corrigé (méthode 1)');
            correctionEffectuee = true;
        }
    }

    // Méthode 2: Chercher dans tous les éléments
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
        if (element.textContent && element.textContent.includes('Send a message to start') && element.children.length === 0) {
            element.textContent = element.textContent.replace('Send a message to start', 'DeepSeek R1 8B Agent');
            console.log('✅ Nom agent corrigé (méthode 2)');
            correctionEffectuee = true;
        }
    });

    // Méthode 3: Chercher spécifiquement les placeholders et messages
    const placeholders = document.querySelectorAll('[placeholder*="message"], [placeholder*="start"], .placeholder, .welcome, .start-message');
    placeholders.forEach(el => {
        if (el.textContent && el.textContent.includes('Send a message to start')) {
            el.textContent = 'DeepSeek R1 8B Agent';
            console.log('✅ Nom agent corrigé (méthode 3)');
            correctionEffectuee = true;
        }
        if (el.placeholder && el.placeholder.includes('Send a message to start')) {
            el.placeholder = 'DeepSeek R1 8B Agent';
            console.log('✅ Placeholder agent corrigé');
            correctionEffectuee = true;
        }
    });

    return correctionEffectuee;
}

// Observer pour les changements dynamiques
const observer = new MutationObserver(() => {
    corrigerNomAgent();
});

observer.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: true
});

// Corrections répétées pour s'assurer que ça marche
let tentatives = 0;
const maxTentatives = 10;

function correctionRecursive() {
    const correctionEffectuee = corrigerNomAgent();
    tentatives++;

    if (!correctionEffectuee && tentatives < maxTentatives) {
        setTimeout(correctionRecursive, 1000);
    } else if (correctionEffectuee) {
        console.log(`✅ Correction réussie après ${tentatives} tentative(s)`);
    } else {
        console.log(`⚠️ Aucune correction après ${maxTentatives} tentatives`);
    }
}

// Démarrer les corrections
setTimeout(correctionRecursive, 500);

console.log('✅ Script de correction du nom agent activé pour interface_8080_reelle.html');
