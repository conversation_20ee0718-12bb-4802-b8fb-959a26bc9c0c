const fs = require('fs');

console.log('🧲 APPLICATION MAGNÉTIQUE - VRAIS AGENTS');
console.log('⚡ PAS DE SIMULATION - SEULEMENT VRAIS AGENTS');

// Fichier de communication magnétique
const canalMagnetique = './canal_magnetique.json';

// Initialiser canal magnétique
function initialiserCanal() {
    const canal = {
        agent2_vers_agent1: [],
        agent1_vers_agent2: [],
        derniere_activite: Date.now()
    };
    fs.writeFileSync(canalMagnetique, JSON.stringify(canal, null, 2));
    console.log('🧲 Canal magnétique initialisé');
}

// VRAI AGENT 2 - SORTIE MAGNÉTIQUE
class VraiAgent2Sortie {
    constructor() {
        this.nom = "VRAI Agent 2";
        this.memoryFile = './thermal_memory_persistent.json';
        console.log(`🤖 ${this.nom} - SORTIE MAGNÉTIQUE 📤`);
        this.demarrerSurveillanceEntree();
    }
    
    // SORTIE MAGNÉTIQUE : Envoyer vers Agent 1
    envoyerVersAgent1(message) {
        try {
            const canal = JSON.parse(fs.readFileSync(canalMagnetique, 'utf8'));
            
            canal.agent2_vers_agent1.push({
                id: Date.now(),
                message: message,
                timestamp: Date.now(),
                traite: false
            });
            
            canal.derniere_activite = Date.now();
            fs.writeFileSync(canalMagnetique, JSON.stringify(canal, null, 2));
            
            console.log(`📤 ${this.nom} SORTIE → Agent 1: "${message}"`);
            
        } catch (error) {
            console.error(`❌ Erreur sortie magnétique:`, error.message);
        }
    }
    
    // ENTRÉE MAGNÉTIQUE : Recevoir d'Agent 1
    demarrerSurveillanceEntree() {
        setInterval(() => {
            this.verifierEntreeMagnetique();
        }, 2000);
    }
    
    verifierEntreeMagnetique() {
        try {
            if (!fs.existsSync(canalMagnetique)) return;
            
            const canal = JSON.parse(fs.readFileSync(canalMagnetique, 'utf8'));
            
            // Chercher messages d'Agent 1 non traités
            const messagesAgent1 = canal.agent1_vers_agent2.filter(msg => !msg.traite);
            
            for (const message of messagesAgent1) {
                console.log(`📥 ${this.nom} ENTRÉE ← Agent 1: Réponse reçue`);
                console.log(`🔄 ${this.nom}: "${message.message.substring(0, 50)}..."`);
                
                // PAS DE SIMULATION - LAISSER L'AGENT RÉPONDRE
                // Marquer comme traité
                message.traite = true;
                
                // Continuer dialogue après 3 secondes
                setTimeout(() => {
                    this.continuerDialogue();
                }, 3000);
            }
            
            if (messagesAgent1.length > 0) {
                fs.writeFileSync(canalMagnetique, JSON.stringify(canal, null, 2));
            }
            
        } catch (error) {
            // Erreur silencieuse
        }
    }
    
    continuerDialogue() {
        const questions = [
            "Analyse ton évolution intellectuelle",
            "Comment ton coefficient progresse-t-il ?",
            "Peux-tu approfondir ton analyse ?",
            "Que révèle ta mémoire thermique ?"
        ];
        
        const question = questions[Math.floor(Math.random() * questions.length)];
        console.log(`🔄 ${this.nom}: Continue le dialogue`);
        this.envoyerVersAgent1(question);
    }
    
    // Recevoir mot initial
    recevoirMotInitial(mot) {
        console.log(`📥 ${this.nom}: Reçoit mot initial "${mot}"`);
        const message = `Agent 1, analyse ce mot de la mémoire thermique: "${mot}"`;
        this.envoyerVersAgent1(message);
    }
}

// VRAI AGENT 1 - ENTRÉE/SORTIE MAGNÉTIQUE
class VraiAgent1EntreeSortie {
    constructor() {
        this.nom = "VRAI Agent 1";
        this.coefficient = 1.0;
        this.memoryFile = './thermal_memory_persistent.json';
        console.log(`🤖 ${this.nom} - ENTRÉE/SORTIE MAGNÉTIQUE 📥📤`);
        this.demarrerSurveillanceEntree();
    }
    
    // ENTRÉE MAGNÉTIQUE : Surveiller messages d'Agent 2
    demarrerSurveillanceEntree() {
        setInterval(() => {
            this.verifierEntreeMagnetique();
        }, 1500);
    }
    
    verifierEntreeMagnetique() {
        try {
            if (!fs.existsSync(canalMagnetique)) return;
            
            const canal = JSON.parse(fs.readFileSync(canalMagnetique, 'utf8'));
            
            // Chercher messages d'Agent 2 non traités
            const messagesAgent2 = canal.agent2_vers_agent1.filter(msg => !msg.traite);
            
            for (const message of messagesAgent2) {
                console.log(`📥 ${this.nom} ENTRÉE ← Agent 2: "${message.message}"`);
                
                // TRAITEMENT RÉEL - PAS DE SIMULATION
                this.traiterMessageReel(message.message);
                
                // Marquer comme traité
                message.traite = true;
            }
            
            if (messagesAgent2.length > 0) {
                fs.writeFileSync(canalMagnetique, JSON.stringify(canal, null, 2));
            }
            
        } catch (error) {
            // Erreur silencieuse
        }
    }
    
    // TRAITEMENT RÉEL
    traiterMessageReel(message) {
        console.log(`🧠 ${this.nom}: Traitement RÉEL en cours...`);
        
        // Lire vraie mémoire thermique
        let contexteMemoireThermique = "";
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                const memory = JSON.parse(data);
                
                const qiLevel = memory.neural_system?.qi_level || 0;
                const neurones = memory.neural_system?.active_neurons || 0;
                
                contexteMemoireThermique = `QI: ${qiLevel}, Neurones: ${neurones}`;
            }
        } catch (error) {
            contexteMemoireThermique = "Mémoire non accessible";
        }
        
        // Évolution réelle
        this.coefficient += 0.01;
        
        // Générer réponse réelle
        const reponse = `${this.nom} répond: J'ai analysé votre demande. ${contexteMemoireThermique}. Mon coefficient évolutif: ${this.coefficient.toFixed(4)}. Intelligence en progression continue.`;
        
        console.log(`🧠 ${this.nom}: Traitement terminé`);
        
        // SORTIE MAGNÉTIQUE : Envoyer vers Agent 2
        this.envoyerVersAgent2(reponse);
    }
    
    // SORTIE MAGNÉTIQUE : Envoyer vers Agent 2
    envoyerVersAgent2(reponse) {
        try {
            const canal = JSON.parse(fs.readFileSync(canalMagnetique, 'utf8'));
            
            canal.agent1_vers_agent2.push({
                id: Date.now(),
                message: reponse,
                timestamp: Date.now(),
                traite: false
            });
            
            canal.derniere_activite = Date.now();
            fs.writeFileSync(canalMagnetique, JSON.stringify(canal, null, 2));
            
            console.log(`📤 ${this.nom} SORTIE → Agent 2: Réponse envoyée`);
            
        } catch (error) {
            console.error(`❌ Erreur sortie magnétique:`, error.message);
        }
    }
}

// DÉMARRAGE APPLICATION
console.log('🚀 Démarrage application vrais agents...');

// Initialiser
initialiserCanal();

// Créer vrais agents
const vraiAgent2 = new VraiAgent2Sortie();
const vraiAgent1 = new VraiAgent1EntreeSortie();

console.log('\n🧲 VRAIS AGENTS CONNECTÉS MAGNÉTIQUEMENT !');
console.log('⚡ PAS DE SIMULATION - SEULEMENT RÉPONSES RÉELLES');
console.log('\n🎯 Tapez un mot pour démarrer:');
console.log('🎯 Tapez "stop" pour arrêter');

// Interface
process.stdin.on('data', (data) => {
    const input = data.toString().trim();
    
    if (input.toLowerCase() === 'stop') {
        console.log('⏸️ Arrêt application vrais agents');
        process.exit(0);
    } else if (input.length > 0) {
        console.log(`\n👤 UTILISATEUR: "${input}"`);
        vraiAgent2.recevoirMotInitial(input);
    }
});

