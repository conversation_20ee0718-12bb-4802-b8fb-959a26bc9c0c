const http = require('http');
const zlib = require('zlib');

class ProxyInterfaceSimple {
    constructor() {
        this.portProxy = 8080;
        this.portLlama = 8082;
        this.nom = "PROXY INTERFACE JARVIS";
    }

    demarrer() {
        const server = http.createServer((req, res) => {
            if (req.method === 'GET' && req.url === '/') {
                // 🌐 SERVIR INTERFACE AMÉLIORÉE
                this.servirInterfaceAmelioree(req, res);
            } else {
                // 🔄 REDIRIGER AUTRES REQUÊTES
                this.redirigerVersLlama(req, res);
            }
        });

        server.listen(this.portProxy, () => {
            console.log(`✅ ${this.nom} actif sur port ${this.portProxy}`);
            console.log(`🌐 Interface améliorée: http://127.0.0.1:${this.portProxy}`);
        });
    }

    async servirInterfaceAmelioree(req, res) {
        try {
            console.log(`🌐 Récupération interface llama.cpp...`);
            
            const options = {
                hostname: '127.0.0.1',
                port: this.portLlama,
                path: '/',
                method: 'GET',
                headers: {
                    'Accept-Encoding': 'gzip, deflate, identity',
                    'User-Agent': 'Mozilla/5.0 (compatible; JARVIS-Proxy/1.0)'
                }
            };

            const proxyReq = http.request(options, (proxyRes) => {
                let chunks = [];
                
                proxyRes.on('data', chunk => {
                    chunks.push(chunk);
                });
                
                proxyRes.on('end', () => {
                    let buffer = Buffer.concat(chunks);
                    let htmlContent = '';
                    
                    // 🔧 GÉRER COMPRESSION
                    const encoding = proxyRes.headers['content-encoding'];
                    
                    if (encoding === 'gzip') {
                        zlib.gunzip(buffer, (err, decompressed) => {
                            if (err) {
                                console.error('❌ Erreur décompression gzip:', err);
                                res.writeHead(500);
                                res.end('Erreur décompression');
                                return;
                            }
                            htmlContent = decompressed.toString();
                            this.envoyerInterfaceAmelioree(htmlContent, res);
                        });
                    } else if (encoding === 'deflate') {
                        zlib.inflate(buffer, (err, decompressed) => {
                            if (err) {
                                console.error('❌ Erreur décompression deflate:', err);
                                res.writeHead(500);
                                res.end('Erreur décompression');
                                return;
                            }
                            htmlContent = decompressed.toString();
                            this.envoyerInterfaceAmelioree(htmlContent, res);
                        });
                    } else {
                        // Pas de compression
                        htmlContent = buffer.toString();
                        this.envoyerInterfaceAmelioree(htmlContent, res);
                    }
                });
            });

            proxyReq.on('error', (error) => {
                console.error(`❌ Erreur récupération interface:`, error.message);
                res.writeHead(500);
                res.end('Erreur récupération interface');
            });

            proxyReq.end();

        } catch (error) {
            console.error(`❌ Erreur interface:`, error.message);
            res.writeHead(500);
            res.end('Erreur interface');
        }
    }

    envoyerInterfaceAmelioree(htmlOriginal, res) {
        // 🔧 AJOUTER AMÉLIORATIONS
        const htmlAmeliore = this.ajouterPanneauCognitif(htmlOriginal);
        
        res.writeHead(200, {
            'Content-Type': 'text/html; charset=utf-8',
            'Content-Length': Buffer.byteLength(htmlAmeliore, 'utf8'),
            'Cache-Control': 'no-cache'
        });
        res.end(htmlAmeliore);
        
        console.log(`✅ Interface JARVIS améliorée servie !`);
    }

    ajouterPanneauCognitif(html) {
        // 🧠 PANNEAU COGNITIF HORIZONTAL AMÉLIORÉ
        const panneau = `
        <!-- JARVIS COGNITIF -->
        <div id="jarvis-panel" style="position:fixed;top:10px;right:10px;width:400px;background:rgba(0,0,0,0.95);border:2px solid #00ffff;border-radius:10px;padding:15px;color:#00ffff;font-family:monospace;z-index:9999;box-shadow:0 0 20px rgba(0,255,255,0.6);">
            <div style="text-align:center;margin-bottom:12px;">
                <h3 style="margin:0;color:#00ffff;text-shadow:0 0 10px #00ffff;">🧠 <span id="agent-name">JARVIS</span></h3>
                <input type="text" id="agent-name-input" placeholder="Nom de votre agent" style="background:rgba(0,255,255,0.1);border:1px solid #00ffff;color:#00ffff;padding:4px;border-radius:4px;font-size:11px;text-align:center;margin-top:5px;width:150px;" value="JARVIS">
            </div>

            <!-- BOUTONS HORIZONTAUX -->
            <div style="display:flex;gap:8px;margin-bottom:12px;justify-content:space-between;">
                <button onclick="toggleMic()" id="mic-btn" style="background:linear-gradient(45deg,#1a1a2e,#16213e);border:1px solid #00ffff;color:#00ffff;padding:8px 12px;border-radius:6px;cursor:pointer;font-size:11px;flex:1;transition:all 0.3s;">🎤 Micro</button>
                <button onclick="toggleSpeaker()" id="speaker-btn" style="background:linear-gradient(45deg,#1a1a2e,#16213e);border:1px solid #00ffff;color:#00ffff;padding:8px 12px;border-radius:6px;cursor:pointer;font-size:11px;flex:1;transition:all 0.3s;">🔊 Audio</button>
                <button onclick="toggleCamera()" id="camera-btn" style="background:linear-gradient(45deg,#1a1a2e,#16213e);border:1px solid #00ffff;color:#00ffff;padding:8px 12px;border-radius:6px;cursor:pointer;font-size:11px;flex:1;transition:all 0.3s;">📹 Caméra</button>
                <button onclick="evolve()" style="background:linear-gradient(45deg,#1a1a2e,#16213e);border:1px solid #00ffff;color:#00ffff;padding:8px 12px;border-radius:6px;cursor:pointer;font-size:11px;flex:1;transition:all 0.3s;">🧬 Évoluer</button>
            </div>

            <!-- BOUTONS MÉMOIRE HORIZONTAUX -->
            <div style="display:flex;gap:6px;margin-bottom:12px;">
                <button onclick="triggerMemory()" style="background:linear-gradient(45deg,#2e1a1a,#3e1621);border:1px solid #ff6600;color:#ff6600;padding:6px 10px;border-radius:5px;cursor:pointer;font-size:10px;flex:1;">🧠 Mémoire</button>
                <button onclick="triggerEvolution()" style="background:linear-gradient(45deg,#1a2e1a,#213e16);border:1px solid #00ff00;color:#00ff00;padding:6px 10px;border-radius:5px;cursor:pointer;font-size:10px;flex:1;">🚀 Évolution</button>
                <button onclick="triggerAnalysis()" style="background:linear-gradient(45deg,#2e2e1a,#3e3e21);border:1px solid #ffff00;color:#ffff00;padding:6px 10px;border-radius:5px;cursor:pointer;font-size:10px;flex:1;">🔍 Analyse</button>
            </div>

            <div style="background:rgba(0,255,255,0.15);border-radius:6px;padding:10px;text-align:center;">
                <div style="font-size:13px;margin-bottom:6px;font-weight:bold;">QI Agent: <span id="qi-display">341.0</span></div>
                <div style="font-size:10px;color:#88ffff;">🧠 Mémoire Thermique Active</div>
                <div style="font-size:9px;color:#66cccc;margin-top:3px;">Évolution continue en arrière-plan</div>
            </div>
        </div>`;

        // 🎯 SCRIPT FONCTIONNEL
        const script = `
        <script>
        let jarvis = {
            micActive: false,
            speakerActive: false,
            cameraActive: false,
            qi: 341.0,
            recognition: null,
            synthesis: window.speechSynthesis,
            stream: null
        };

        function toggleMic() {
            const btn = document.getElementById('mic-btn');
            if (!jarvis.micActive && 'webkitSpeechRecognition' in window) {
                jarvis.recognition = new webkitSpeechRecognition();
                jarvis.recognition.continuous = true;
                jarvis.recognition.lang = 'fr-FR';
                jarvis.recognition.onresult = function(event) {
                    let transcript = '';
                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        if (event.results[i].isFinal) {
                            transcript += event.results[i][0].transcript;
                        }
                    }
                    if (transcript) {
                        const input = document.querySelector('textarea, input[type="text"]');
                        if (input) {
                            input.value = transcript;
                            const sendBtn = document.querySelector('button[type="submit"], .send-button');
                            if (sendBtn) sendBtn.click();
                        }
                    }
                };
                jarvis.recognition.start();
                jarvis.micActive = true;
                btn.style.background = '#00ffff';
                btn.style.color = '#000';
                console.log('🎤 Micro activé');
            } else if (jarvis.recognition) {
                jarvis.recognition.stop();
                jarvis.micActive = false;
                btn.style.background = '#1a1a2e';
                btn.style.color = '#00ffff';
                console.log('🎤 Micro désactivé');
            }
        }

        function toggleSpeaker() {
            const btn = document.getElementById('speaker-btn');
            jarvis.speakerActive = !jarvis.speakerActive;
            if (jarvis.speakerActive) {
                btn.style.background = '#00ffff';
                btn.style.color = '#000';
                jarvis.synthesis.speak(new SpeechSynthesisUtterance('Audio activé'));
                console.log('🔊 Audio activé');
            } else {
                btn.style.background = '#1a1a2e';
                btn.style.color = '#00ffff';
                jarvis.synthesis.cancel();
                console.log('🔊 Audio désactivé');
            }
        }

        async function toggleCamera() {
            const btn = document.getElementById('camera-btn');
            if (!jarvis.cameraActive) {
                try {
                    jarvis.stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    jarvis.cameraActive = true;
                    btn.style.background = '#00ffff';
                    btn.style.color = '#000';
                    console.log('📹 Caméra activée');
                    
                    const video = document.createElement('video');
                    video.srcObject = jarvis.stream;
                    video.autoplay = true;
                    video.muted = true;
                    video.style.cssText = 'position:fixed;bottom:10px;right:10px;width:120px;height:80px;border:1px solid #00ffff;border-radius:4px;z-index:10000;';
                    document.body.appendChild(video);
                } catch (error) {
                    console.error('❌ Erreur caméra:', error);
                }
            } else {
                if (jarvis.stream) {
                    jarvis.stream.getTracks().forEach(track => track.stop());
                    const video = document.querySelector('video[style*="position:fixed"]');
                    if (video) video.remove();
                }
                jarvis.cameraActive = false;
                btn.style.background = '#1a1a2e';
                btn.style.color = '#00ffff';
                console.log('📹 Caméra désactivée');
            }
        }

        function evolve() {
            jarvis.qi += Math.random() * 2 + 0.5;
            document.getElementById('qi-display').textContent = jarvis.qi.toFixed(1);
            console.log('🧬 Évolution - Nouveau QI:', jarvis.qi.toFixed(1));
        }

        // 🧠 DÉCLENCHEMENT MÉMOIRE THERMIQUE
        function triggerMemory() {
            const agentName = document.getElementById('agent-name-input').value || 'JARVIS';
            const prompt = agentName + ', te souviens-tu de nos conversations précédentes ? Utilise ta mémoire thermique pour me rappeler ce dont nous avons parlé.';
            sendPromptToAgent(prompt);
            console.log('🧠 Déclenchement mémoire thermique');
        }

        // 🚀 DÉCLENCHEMENT ÉVOLUTION
        function triggerEvolution() {
            const agentName = document.getElementById('agent-name-input').value || 'JARVIS';
            const prompt = agentName + ', comment évolue ton QI avec tes agents autonomes ? Montre-moi tes nouvelles capacités et connexions neuronales.';
            sendPromptToAgent(prompt);
            console.log('🚀 Déclenchement évolution cognitive');
        }

        // 🔍 DÉCLENCHEMENT ANALYSE
        function triggerAnalysis() {
            const agentName = document.getElementById('agent-name-input').value || 'JARVIS';
            const prompt = agentName + ', analyse tes pensées récentes et explique-moi ton processus de réflexion. Que penses-tu de notre interaction actuelle ?';
            sendPromptToAgent(prompt);
            console.log('🔍 Déclenchement analyse cognitive');
        }

        // 📤 ENVOYER PROMPT À L'AGENT
        function sendPromptToAgent(prompt) {
            const input = document.querySelector('textarea, input[type="text"]');
            if (input) {
                input.value = prompt;
                input.focus();

                // Déclencher l'envoi automatique
                setTimeout(() => {
                    const sendBtn = document.querySelector('button[type="submit"], .send-button, button:contains("Send")');
                    if (sendBtn) {
                        sendBtn.click();
                    } else {
                        // Essayer avec Enter
                        const event = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true
                        });
                        input.dispatchEvent(event);
                    }
                }, 100);
            }
        }

        // 🏷️ CHANGER NOM AGENT
        document.getElementById('agent-name-input').addEventListener('input', function() {
            const newName = this.value || 'JARVIS';
            document.getElementById('agent-name').textContent = newName;

            // Changer le titre de la page
            document.title = newName + ' - Interface Cognitive';

            console.log('🏷️ Nom agent changé:', newName);
        });

        // 🔄 CHANGER TITRE INTERFACE
        function changeInterfaceTitle() {
            const agentName = document.getElementById('agent-name-input').value || 'JARVIS';

            // Changer le titre principal de l'interface
            const mainTitle = document.querySelector('h1, .title, .header h1, .header h2');
            if (mainTitle) {
                mainTitle.textContent = agentName;
            }

            // Changer tous les "llama.cpp" en nom de l'agent
            const elements = document.querySelectorAll('*');
            elements.forEach(el => {
                if (el.textContent && el.textContent.includes('llama.cpp')) {
                    el.textContent = el.textContent.replace(/llama\\.cpp/g, agentName);
                }
            });
        }

        // Évolution automatique
        setInterval(() => {
            jarvis.qi += Math.random() * 0.1;
            document.getElementById('qi-display').textContent = jarvis.qi.toFixed(1);
        }, 10000);

        // Initialisation
        setTimeout(() => {
            changeInterfaceTitle();
            console.log('🧠 ' + (document.getElementById('agent-name-input').value || 'JARVIS') + ' Cognitif Prêt !');
        }, 1000);

        console.log('🧠 JARVIS Cognitif Prêt !');
        </script>`;

        // 🔧 INJECTER DANS HTML
        let htmlModifie = html;

        // Changer le titre de la page
        htmlModifie = htmlModifie.replace(/<title>.*?<\/title>/i, '<title>JARVIS - Interface Cognitive</title>');

        // Remplacer "llama.cpp" par "JARVIS" dans l'interface
        htmlModifie = htmlModifie.replace(/llama\.cpp/g, 'JARVIS');
        htmlModifie = htmlModifie.replace(/llama-cpp/g, 'JARVIS');

        // Ajouter le panneau et script
        htmlModifie = htmlModifie.replace('</body>', panneau + script + '</body>');

        return htmlModifie;
    }

    redirigerVersLlama(req, res) {
        const options = {
            hostname: '127.0.0.1',
            port: this.portLlama,
            path: req.url,
            method: req.method,
            headers: req.headers
        };

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (error) => {
            console.error(`❌ Erreur proxy:`, error.message);
            res.writeHead(500);
            res.end('Erreur proxy');
        });

        req.pipe(proxyReq);
    }
}

// 🚀 DÉMARRAGE
const proxy = new ProxyInterfaceSimple();
proxy.demarrer();
