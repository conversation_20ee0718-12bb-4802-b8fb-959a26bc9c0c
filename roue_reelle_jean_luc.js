const fs = require('fs');

console.log('🎡 ROUE RÉELLE POUR JEAN-LUC');
console.log('🔄 Mémoire thermique → Mot → Agent 1 RÉEL');

let positionRoue = 0;
const etages = ['HAUT 🔴', 'MILIEU 🟡', 'BAS 🔵'];
let tours = 0;
let actif = false;

// Mouvements irréguliers
const mouvements = [
    { de: 0, vers: 1, vitesse: 2000 },
    { de: 0, vers: 2, vitesse: 3500 },
    { de: 1, vers: 0, vitesse: 1500 },
    { de: 1, vers: 2, vitesse: 2500 },
    { de: 2, vers: 0, vitesse: 4000 },
    { de: 2, vers: 1, vitesse: 1800 }
];

// Prendre mot de la mémoire thermique RÉELLE
function prendreMotReel() {
    try {
        const memoryFile = './thermal_memory_persistent.json';
        
        if (!fs.existsSync(memoryFile)) {
            console.log('❌ Mémoire thermique non trouvée');
            return null;
        }
        
        const data = fs.readFileSync(memoryFile, 'utf8');
        const memory = JSON.parse(data);
        
        // Extraire mots des zones thermiques
        const mots = new Set();
        
        // Parcourir toutes les zones
        Object.values(memory.thermal_zones || {}).forEach(zone => {
            if (zone.entries) {
                zone.entries.forEach(entry => {
                    if (entry.content) {
                        const motsContenu = entry.content
                            .toLowerCase()
                            .replace(/[^\w\s]/g, ' ')
                            .split(/\s+/)
                            .filter(mot => mot.length > 3 && mot.length < 15);
                        
                        motsContenu.forEach(mot => mots.add(mot));
                    }
                });
            }
        });
        
        const motsArray = Array.from(mots);
        if (motsArray.length === 0) {
            return 'intelligence'; // Mot par défaut
        }
        
        return motsArray[Math.floor(Math.random() * motsArray.length)];
        
    } catch (error) {
        console.error('❌ Erreur extraction mot:', error.message);
        return 'analyse'; // Mot par défaut
    }
}

// Tourner la roue
function tournerRoue() {
    if (!actif) return;
    
    tours++;
    console.log(`\n🎡 === ROTATION ${tours} ===`);
    
    // Mouvement irrégulier
    const mouvementsPossibles = mouvements.filter(m => m.de === positionRoue);
    const mouvement = mouvementsPossibles[Math.floor(Math.random() * mouvementsPossibles.length)];
    
    console.log(`🔄 ${mouvement.description || etages[positionRoue] + ' → ' + etages[mouvement.vers]} (${mouvement.vitesse}ms)`);
    
    // Prendre mot RÉEL
    const mot = prendreMotReel();
    console.log(`🎯 ROUE ${etages[positionRoue]} prend: "${mot}"`);
    
    // Simuler envoi à Agent 1
    console.log(`📤 ROUE → AGENT 1: "${mot}"`);
    console.log(`🧠 AGENT 1: Analyse "${mot}" - Intelligence en évolution`);
    
    // Changer position
    positionRoue = mouvement.vers;
    
    // Continuer
    setTimeout(tournerRoue, mouvement.vitesse);
}

// Interface
console.log('\n🎯 Tapez "start" pour démarrer la roue');
console.log('🎯 Tapez "stop" pour arrêter');

process.stdin.on('data', (data) => {
    const cmd = data.toString().trim().toLowerCase();
    
    if (cmd === 'start') {
        if (!actif) {
            actif = true;
            console.log('🚀 ROUE RÉELLE DÉMARRÉE');
            tournerRoue();
        }
    } else if (cmd === 'stop') {
        actif = false;
        console.log('⏸️ ROUE ARRÊTÉE');
    } else if (cmd === 'exit') {
        process.exit(0);
    }
});

