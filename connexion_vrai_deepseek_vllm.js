// 🤖 CONNEXION VRAIE AGENTS DEEPSEEK R1 VIA VLLM
// ⚡ UTILISE TON VRAI MODÈLE: deepseek-ai/DeepSeek-R1-0528

const http = require('http');

class VraiAgentDeepSeekVLLM {
    constructor(nom, port = 8000) {
        this.nom = nom;
        this.vllmUrl = `http://localhost:${port}`;
        this.modele = "deepseek-ai/DeepSeek-R1-0528";
        
        console.log(`🤖 ${this.nom} - CONNEXION VRAI DEEPSEEK R1 VIA VLLM`);
        console.log(`🔗 URL VLLM: ${this.vllmUrl}`);
        console.log(`🧠 Modèle: ${this.modele}`);
    }
    
    // 🧠 VRAIE MÉTHODE : Appeler ton vrai DeepSeek R1 via VLLM
    async appellerVraiDeepSeek(prompt) {
        return new Promise((resolve, reject) => {
            const data = JSON.stringify({
                model: this.modele,
                prompt: prompt,
                max_tokens: 500,
                temperature: 0.7,
                stream: false
            });
            
            const options = {
                hostname: 'localhost',
                port: 8000,
                path: '/v1/completions',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': data.length
                }
            };
            
            console.log(`🧠 ${this.nom}: Appel VRAI DeepSeek R1...`);
            console.log(`📤 Prompt: "${prompt}"`);
            
            const req = http.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(responseData);
                        const reponse = response.choices[0].text.trim();
                        
                        console.log(`📥 ${this.nom}: VRAIE réponse DeepSeek R1 reçue`);
                        console.log(`🧠 Réponse: "${reponse.substring(0, 100)}..."`);
                        
                        resolve(reponse);
                    } catch (error) {
                        console.error(`❌ Erreur parsing réponse:`, error.message);
                        reject(error);
                    }
                });
            });
            
            req.on('error', (error) => {
                console.error(`❌ ${this.nom}: Erreur connexion VLLM:`, error.message);
                reject(error);
            });
            
            req.write(data);
            req.end();
        });
    }
    
    // 📥 ENTRÉE MAGNÉTIQUE
    async entree(message) {
        console.log(`📥 ${this.nom} ENTRÉE: "${message}"`);
        
        try {
            // Appeler ton VRAI DeepSeek R1
            const reponse = await this.appellerVraiDeepSeek(message);
            
            // SORTIE MAGNÉTIQUE
            if (this.sortie) {
                console.log(`📤 ${this.nom} SORTIE: Envoi réponse`);
                this.sortie(reponse);
            }
            
            return reponse;
            
        } catch (error) {
            console.error(`❌ ${this.nom}: Erreur traitement:`, error.message);
            const erreur = `Erreur ${this.nom}: ${error.message}`;
            
            if (this.sortie) {
                this.sortie(erreur);
            }
            
            return erreur;
        }
    }
    
    // 🔌 CONNEXION MAGNÉTIQUE
    connecterSortie(fonctionSortie) {
        this.sortie = fonctionSortie;
        console.log(`🧲 ${this.nom}: SORTIE connectée magnétiquement`);
    }
}

// 🧪 TEST CONNEXION VRAI DEEPSEEK
async function testerVraiDeepSeek() {
    console.log('🧪 TEST CONNEXION VRAI DEEPSEEK R1 VIA VLLM');
    
    // Créer vrais agents
    const vraiAgent1 = new VraiAgentDeepSeekVLLM("VRAI Agent 1");
    const vraiAgent2 = new VraiAgentDeepSeekVLLM("VRAI Agent 2");
    
    // 🧲 CONNEXION MAGNÉTIQUE
    vraiAgent2.connecterSortie((reponse) => {
        console.log(`🔄 Agent 2 → Agent 1: Transmission réponse`);
        vraiAgent1.entree(`Agent 1, voici la réponse d'Agent 2: ${reponse}`);
    });
    
    vraiAgent1.connecterSortie((reponse) => {
        console.log(`🔄 Agent 1 → Agent 2: Transmission réponse`);
        console.log(`✅ CYCLE MAGNÉTIQUE COMPLET AVEC VRAIS AGENTS !`);
    });
    
    console.log('\n🧲 AGENTS CONNECTÉS MAGNÉTIQUEMENT');
    console.log('🎯 Tapez une question pour tester:');
    
    // Interface
    process.stdin.on('data', async (data) => {
        const question = data.toString().trim();
        
        if (question === 'exit') {
            process.exit(0);
        } else if (question.length > 0) {
            console.log(`\n👤 UTILISATEUR: "${question}"`);
            console.log(`📤 Envoi vers VRAI Agent 2...`);
            
            try {
                await vraiAgent2.entree(question);
            } catch (error) {
                console.error(`❌ Erreur test:`, error.message);
            }
        }
    });
}

// 🚀 DÉMARRAGE
console.log('🚀 Connexion aux VRAIS agents DeepSeek R1 via VLLM...');
console.log('⚠️  IMPORTANT: Assure-toi que VLLM tourne avec:');
console.log('   vllm serve "deepseek-ai/DeepSeek-R1-0528"');
console.log('');

testerVraiDeepSeek();

module.exports = VraiAgentDeepSeekVLLM;
