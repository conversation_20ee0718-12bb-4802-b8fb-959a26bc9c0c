{"timestamp": 1750207907850, "machine": {"platform": "darwin", "arch": "arm64", "cpus": 10, "totalMemory": 16, "freeMemory": 0, "hostname": "minideje<PERSON><PERSON><PERSON>", "type": "<PERSON>", "release": "24.3.0", "profil": "haute_performance", "puissance_theorique": 16000}, "adaptation": {"cpu_utilisation": 0.238037109375, "memory_utilisation": 1, "optimal_threads": 1, "turbo_recommande": 3, "saturation_evitee": false, "memory_limit": 0.8}, "turbos": {"actifs": [], "disponibles": [{"nom": "TurboMemoire", "type": "memoire", "boost": 1.5, "cout_cpu": 0.1, "cout_memory": 0.05, "description": "Optimisation accès mémoire thermique"}, {"nom": "TurboCPU", "type": "processeur", "boost": 1.3, "cout_cpu": 0.2, "cout_memory": 0.02, "description": "Parallélisation calculs neuronaux"}, {"nom": "TurboCache", "type": "cache", "boost": 1.4, "cout_cpu": 0.05, "cout_memory": 0.1, "description": "Cache intelligent conversations"}, {"nom": "TurboReseau", "type": "<PERSON><PERSON>", "boost": 1.2, "cout_cpu": 0.08, "cout_memory": 0.03, "description": "Optimisation connexions réseau"}, {"nom": "TurboIA", "type": "intelligence", "boost": 1.6, "cout_cpu": 0.15, "cout_memory": 0.08, "description": "Accélération traitement IA"}], "cascade_niveau": 3, "puissance_totale": -4.440892098500626e-16}}