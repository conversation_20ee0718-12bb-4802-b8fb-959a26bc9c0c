<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 TEST RÉEL - Mémoire Thermique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px; border-radius: 15px;
        }
        .test-log {
            background: #000; color: #0f0; padding: 20px;
            border-radius: 10px; font-family: monospace;
            font-size: 14px; height: 400px; overflow-y: auto;
            margin: 20px 0; border: 2px solid #0f0;
        }
        .conversation {
            background: rgba(255,255,255,0.1);
            padding: 15px; margin: 10px 0;
            border-radius: 8px; border-left: 4px solid #4CAF50;
        }
        .memory-entry {
            background: rgba(255, 152, 0, 0.2);
            padding: 10px; margin: 5px 0;
            border-radius: 5px; font-size: 12px;
            border-left: 3px solid #FF9800;
        }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 15px 30px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 16px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .status {
            padding: 10px; border-radius: 5px; margin: 10px 0;
            font-weight: bold; text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .warning { background: rgba(255, 152, 0, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 TEST RÉEL - Mémoire Thermique JARVIS</h1>
        <p>Test avec le VRAI serveur DeepSeek R1 8B - Pas de simulation !</p>

        <button onclick="testRealMemory()" id="testBtn">🚀 TEST RÉEL COMPLET</button>
        <button onclick="testFunctionsOnly()">🔧 Test Fonctions Seules</button>
        <button onclick="clearAll()">🗑️ Effacer</button>

        <div id="status"></div>
        <div class="test-log" id="testLog">[PRÊT] Cliquez sur "TEST RÉEL COMPLET" pour commencer</div>
        <div id="results"></div>
    </div>

    <script>
        let thermalMemory = [];
        let testLog = document.getElementById('testLog');
        let results = document.getElementById('results');
        let status = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#0f0', error: '#f44', success: '#4f4', warning: '#fa0' };
            testLog.innerHTML += `<div style="color: ${colors[type]}">[${timestamp}] ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function showStatus(message, type) {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // FONCTIONS MÉMOIRE RÉELLES (pas de simulation)
        function saveThermalMemory(userMessage, agentResponse) {
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse,
                keywords: extractKeywords(userMessage + ' ' + agentResponse)
            };
            
            thermalMemory.push(entry);
            localStorage.setItem('jarvis_test_memory', JSON.stringify(thermalMemory));
            
            log(`💾 SAUVEGARDÉ: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`, 'success');
            log(`📊 Mots-clés: ${entry.keywords.join(', ')}`, 'info');
            log(`📈 Total mémoire: ${thermalMemory.length} entrée(s)`, 'info');
            
            return entry;
        }

        function searchMemory(query) {
            log(`🔍 RECHERCHE: "${query}"`, 'info');
            log(`🧠 Base de données: ${thermalMemory.length} entrée(s)`, 'info');
            
            if (thermalMemory.length === 0) {
                log('❌ Mémoire vide - aucun contexte', 'error');
                return '';
            }
            
            // Afficher le contenu de la mémoire
            thermalMemory.forEach((entry, i) => {
                log(`📋 Entrée ${i+1}: "${entry.user}" → "${entry.agent.substring(0, 30)}..."`, 'info');
            });
            
            const queryLower = query.toLowerCase();
            const triggers = ['souviens', 'rappelle', 'nom', 'appelle'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                log('🎯 Trigger détecté - recherche active', 'success');
                
                const results = thermalMemory.filter(entry => 
                    entry.user.toLowerCase().includes('appelle') ||
                    entry.user.toLowerCase().includes('nom') ||
                    entry.agent.toLowerCase().includes('jean-luc') ||
                    entry.keywords.includes('jean-luc')
                );
                
                log(`📁 Résultats trouvés: ${results.length}`, results.length > 0 ? 'success' : 'warning');
                
                if (results.length > 0) {
                    const context = `\n\nCONTEXTE MÉMOIRE:\n${results.map(r => `- "${r.user}" → "${r.agent}"`).join('\n')}\nUtilise ce contexte.\n`;
                    log(`🧠 Contexte généré: ${context.length} caractères`, 'success');
                    return context;
                }
            } else {
                log('⚠️ Aucun trigger détecté', 'warning');
            }
            
            return '';
        }

        function extractKeywords(text) {
            const textLower = text.toLowerCase();
            const keywords = [];

            if (textLower.includes('jean-luc') || (textLower.includes('jean') && textLower.includes('luc'))) {
                keywords.push('jean-luc');
            }

            if (textLower.includes('appelle')) keywords.push('appelle');
            if (textLower.includes('nom')) keywords.push('nom');
            if (textLower.includes('bonjour')) keywords.push('bonjour');
            if (textLower.includes('souviens')) keywords.push('souviens');
            if (textLower.includes('rappelle')) keywords.push('rappelle');

            return keywords;
        }

        // APPEL RÉEL AU SERVEUR (pas de simulation)
        async function callRealServer(prompt) {
            log(`📤 ENVOI AU SERVEUR: "${prompt.substring(0, 50)}..."`, 'info');
            
            try {
                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        n_predict: 50,
                        temperature: 0.3,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`Erreur HTTP ${response.status}`);
                }

                const data = await response.json();
                const reply = (data.content || '').trim();
                
                if (!reply) {
                    throw new Error('Réponse vide du serveur');
                }
                
                log(`📥 REÇU DU SERVEUR: "${reply.substring(0, 50)}..."`, 'success');
                return reply;
                
            } catch (error) {
                log(`❌ ERREUR SERVEUR: ${error.message}`, 'error');
                throw error;
            }
        }

        // TEST RÉEL COMPLET
        async function testRealMemory() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            
            try {
                log('🚀 DÉBUT TEST RÉEL AVEC DEEPSEEK R1 8B', 'success');
                log('=' .repeat(50), 'info');
                
                showStatus('🔧 Test connexion serveur...', 'warning');
                
                // Test connexion
                const healthResponse = await fetch('http://localhost:8000/health');
                if (!healthResponse.ok) {
                    throw new Error('Serveur inaccessible');
                }
                log('✅ Serveur DeepSeek R1 8B accessible', 'success');
                showStatus('✅ Serveur accessible', 'success');
                
                // Reset mémoire
                thermalMemory = [];
                log('🗑️ Mémoire réinitialisée', 'info');
                
                // ÉTAPE 1: Présentation RÉELLE
                showStatus('📝 Étape 1: Présentation...', 'warning');
                log('📝 ÉTAPE 1: Présentation à l\'agent', 'info');
                
                const message1 = 'Bonjour, je m\'appelle Jean-Luc';
                const response1 = await callRealServer(message1);
                
                // Sauvegarder en mémoire
                saveThermalMemory(message1, response1);
                
                // Afficher conversation
                const conv1 = document.createElement('div');
                conv1.className = 'conversation';
                conv1.innerHTML = `<strong>👤 User:</strong> ${message1}<br><strong>🤖 Agent:</strong> ${response1}`;
                results.appendChild(conv1);
                
                // Attendre
                log('⏳ Attente 3 secondes...', 'info');
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // ÉTAPE 2: Test mémoire RÉEL
                showStatus('📝 Étape 2: Test mémoire...', 'warning');
                log('📝 ÉTAPE 2: Test rappel mémoire', 'info');
                
                const message2 = 'Tu te souviens de mon nom ?';
                const memoryContext = searchMemory(message2);
                
                if (memoryContext) {
                    log('🧠 CONTEXTE TROUVÉ !', 'success');
                    
                    const memoryDiv = document.createElement('div');
                    memoryDiv.className = 'memory-entry';
                    memoryDiv.innerHTML = `<strong>🧠 CONTEXTE MÉMOIRE UTILISÉ:</strong><br><pre>${memoryContext}</pre>`;
                    results.appendChild(memoryDiv);
                } else {
                    log('❌ AUCUN CONTEXTE TROUVÉ', 'error');
                }
                
                const promptWithMemory = message2 + memoryContext;
                const response2 = await callRealServer(promptWithMemory);
                
                // Afficher conversation
                const conv2 = document.createElement('div');
                conv2.className = 'conversation';
                conv2.innerHTML = `<strong>👤 User:</strong> ${message2}<br><strong>🤖 Agent:</strong> ${response2}`;
                results.appendChild(conv2);
                
                // VÉRIFICATION FINALE
                showStatus('🔍 Vérification résultat...', 'warning');
                log('🔍 VÉRIFICATION FINALE', 'info');
                
                const remembersName = response2.toLowerCase().includes('jean-luc') || 
                                     response2.toLowerCase().includes('jean luc');
                
                log('=' .repeat(50), 'info');
                if (remembersName) {
                    log('🎉 SUCCÈS ! L\'agent se souvient du nom "Jean-Luc"', 'success');
                    log('✅ MÉMOIRE THERMIQUE FONCTIONNE !', 'success');
                    showStatus('🎉 SUCCÈS ! Mémoire thermique fonctionnelle', 'success');
                } else {
                    log('❌ ÉCHEC ! L\'agent ne se souvient pas', 'error');
                    log('❌ PROBLÈME MÉMOIRE THERMIQUE', 'error');
                    showStatus('❌ ÉCHEC ! Problème mémoire thermique', 'error');
                }
                log('=' .repeat(50), 'info');
                
            } catch (error) {
                log(`❌ TEST ÉCHOUÉ: ${error.message}`, 'error');
                showStatus(`❌ Test échoué: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
            }
        }

        // Test des fonctions seules (sans serveur)
        function testFunctionsOnly() {
            log('🔧 TEST FONCTIONS MÉMOIRE SEULES', 'success');
            
            // Reset
            thermalMemory = [];
            
            // Test sauvegarde
            log('📝 Test sauvegarde...', 'info');
            saveThermalMemory('Bonjour, je m\'appelle Jean-Luc', 'Bonjour Jean-Luc ! Ravi de vous rencontrer.');
            
            if (thermalMemory.length === 1) {
                log('✅ Sauvegarde OK', 'success');
            } else {
                log('❌ Sauvegarde ÉCHEC', 'error');
                return;
            }
            
            // Test recherche
            log('📝 Test recherche...', 'info');
            const context = searchMemory('Tu te souviens de mon nom ?');
            
            if (context && context.includes('Jean-Luc')) {
                log('✅ Recherche OK - "Jean-Luc" trouvé', 'success');
                log('🎉 FONCTIONS MÉMOIRE PARFAITES !', 'success');
                showStatus('✅ Fonctions mémoire parfaites', 'success');
            } else {
                log('❌ Recherche ÉCHEC - "Jean-Luc" non trouvé', 'error');
                showStatus('❌ Problème recherche mémoire', 'error');
            }
        }

        function clearAll() {
            testLog.innerHTML = '[EFFACÉ] Prêt pour un nouveau test';
            results.innerHTML = '';
            status.innerHTML = '';
            thermalMemory = [];
            localStorage.removeItem('jarvis_test_memory');
        }

        // Chargement initial
        const saved = localStorage.getItem('jarvis_test_memory');
        if (saved) {
            thermalMemory = JSON.parse(saved);
            log(`📁 Mémoire chargée: ${thermalMemory.length} entrée(s)`, 'success');
        } else {
            log('📁 Aucune mémoire sauvegardée', 'info');
        }
        
        log('🧪 Test réel prêt - Serveur DeepSeek R1 8B requis', 'success');
    </script>
</body>
</html>
