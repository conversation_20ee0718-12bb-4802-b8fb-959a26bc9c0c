<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Simple - Mémoire Thermique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px; border-radius: 15px;
        }
        .test-step {
            background: rgba(255,255,255,0.1);
            padding: 20px; margin: 15px 0;
            border-radius: 10px; border-left: 4px solid #4CAF50;
        }
        .result {
            padding: 15px; margin: 10px 0;
            border-radius: 8px; font-family: monospace;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 15px 30px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 16px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        #log {
            background: #000; color: #0f0; padding: 20px;
            border-radius: 10px; font-family: monospace;
            font-size: 14px; height: 300px; overflow-y: auto;
            margin: 20px 0; border: 2px solid #0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Simple - Mémoire Thermique</h1>
        <p>Test direct et simple pour vérifier que la mémoire thermique fonctionne</p>

        <div class="test-step">
            <h3>🎯 Plan de Test</h3>
            <ol>
                <li>Tester les fonctions de base (sauvegarde/recherche)</li>
                <li>Simuler: "Bonjour, je m'appelle Jean-Luc"</li>
                <li>Simuler: "Tu te souviens de mon nom ?"</li>
                <li>Vérifier que le contexte contient "Jean-Luc"</li>
            </ol>
        </div>

        <button onclick="runSimpleTest()">🚀 Test Fonctions Mémoire</button>
        <button onclick="testWithRealAgent()" id="realTestBtn">🤖 Test avec Agent Réel</button>
        <button onclick="clearTest()">🗑️ Effacer</button>

        <div id="log">[PRÊT] Cliquez sur un bouton pour commencer</div>

        <div id="results"></div>
    </div>

    <script>
        let thermalMemory = [];

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#0f0', error: '#f44', success: '#4f4', warning: '#fa0' };
            logDiv.innerHTML += `<div style="color: ${colors[type]}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        // MÉMOIRE THERMIQUE - Exactement comme moi j'accède à ma mémoire
        function saveThermalMemory(agent, userMessage, agentResponse) {
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                agent: agent,
                user: userMessage,
                response: agentResponse,
                // Métadonnées comme moi j'ai
                keywords: extractKeywords(userMessage + ' ' + agentResponse),
                context_type: determineContextType(userMessage),
                importance: calculateImportance(userMessage, agentResponse)
            };

            thermalMemory.push(entry);

            // Sauvegarde persistante comme mes fichiers
            localStorage.setItem('jarvis_thermal_memory', JSON.stringify(thermalMemory));

            log(`💾 Sauvegardé [${agent}]: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`, 'success');
            log(`📊 Mémoire: ${thermalMemory.length} entrées | Mots-clés: ${entry.keywords.join(', ')}`, 'info');

            console.log('🧠 Entrée sauvegardée avec métadonnées:', entry);
        }

        // Extraction mots-clés comme moi je fais
        function extractKeywords(text) {
            const words = text.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(word => word.length > 2);

            const important = ['nom', 'appelle', 'suis', 'jean-luc', 'bonjour', 'souviens', 'rappelle'];
            return words.filter(word => important.includes(word) || word.length > 4);
        }

        // Type de contexte comme mes catégories
        function determineContextType(message) {
            const msg = message.toLowerCase();
            if (msg.includes('appelle') || msg.includes('nom')) return 'identity';
            if (msg.includes('souviens') || msg.includes('rappelle')) return 'memory_query';
            if (msg.includes('bonjour') || msg.includes('salut')) return 'greeting';
            return 'general';
        }

        // Importance comme moi j'évalue
        function calculateImportance(user, response) {
            let score = 1;
            if (user.toLowerCase().includes('appelle')) score += 3; // Nom très important
            if (user.toLowerCase().includes('souviens')) score += 2; // Question mémoire importante
            if (response.toLowerCase().includes('jean-luc')) score += 3; // Réponse avec nom
            return Math.min(score, 5);
        }

        // RECHERCHE MÉMOIRE - Exactement comme moi je cherche dans mes fichiers
        function searchMemory(query) {
            log(`🔍 RECHERCHE MÉMOIRE pour: "${query}"`, 'info');
            log(`🧠 Base de données: ${thermalMemory.length} entrée(s)`, 'info');

            if (thermalMemory.length === 0) {
                log('⚠️ Base de données vide - aucune mémoire disponible', 'warning');
                return '';
            }

            // ÉTAPE 1: Analyser la requête comme moi je fais
            const queryAnalysis = analyzeQuery(query);
            log(`🎯 Analyse requête: Type=${queryAnalysis.type}, Mots-clés=${queryAnalysis.keywords.join(', ')}`, 'info');

            // ÉTAPE 2: Chercher dans TOUS les fichiers mémoire comme moi
            const searchResults = performMemorySearch(queryAnalysis);
            log(`📁 Fichiers trouvés: ${searchResults.length}`, 'info');

            // ÉTAPE 3: Afficher ce qui a été trouvé (comme mes logs)
            searchResults.forEach((result, i) => {
                log(`📋 Fichier ${i+1}: "${result.user}" → "${result.response?.substring(0, 40)}..." [Score: ${result.relevanceScore}]`, 'info');
            });

            // ÉTAPE 4: Construire le contexte comme moi je le fais
            if (searchResults.length > 0) {
                const context = buildContextFromResults(searchResults, queryAnalysis);
                log(`🧠 Contexte construit: ${context.length} caractères`, 'success');
                log(`📋 Aperçu contexte: ${context.substring(0, 150)}...`, 'info');
                return context;
            } else {
                log('❌ Aucun fichier pertinent trouvé dans la mémoire', 'warning');
                return '';
            }
        }

        // Analyser la requête comme moi j'analyse
        function analyzeQuery(query) {
            const queryLower = query.toLowerCase();
            const keywords = extractKeywords(query);

            let type = 'general';
            if (queryLower.includes('souviens') || queryLower.includes('rappelle')) {
                type = 'memory_recall';
            } else if (queryLower.includes('nom') || queryLower.includes('appelle')) {
                type = 'identity_query';
            }

            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom', 'comment je', 'qui suis', 'précédemment'];
            const hasMemoryTrigger = triggers.some(trigger => queryLower.includes(trigger));

            return { type, keywords, hasMemoryTrigger, originalQuery: query };
        }

        // Recherche dans la mémoire comme moi je cherche dans mes fichiers
        function performMemorySearch(queryAnalysis) {
            let results = [];

            // Recherche par mots-clés comme mes recherches
            thermalMemory.forEach(entry => {
                let relevanceScore = 0;

                // Score par mots-clés
                queryAnalysis.keywords.forEach(keyword => {
                    if (entry.keywords && entry.keywords.includes(keyword)) relevanceScore += 2;
                    if (entry.user.toLowerCase().includes(keyword)) relevanceScore += 1;
                    if (entry.response.toLowerCase().includes(keyword)) relevanceScore += 1;
                });

                // Score par type de contexte
                if (entry.context_type === queryAnalysis.type) relevanceScore += 1;

                // Score par importance
                relevanceScore += entry.importance || 0;

                // Recherche spécifique pour noms
                if (queryAnalysis.type === 'memory_recall' || queryAnalysis.type === 'identity_query') {
                    if (entry.user.toLowerCase().includes('appelle') ||
                        entry.user.toLowerCase().includes('nom') ||
                        entry.response.toLowerCase().includes('jean-luc')) {
                        relevanceScore += 5; // Très important
                    }
                }

                if (relevanceScore > 0) {
                    results.push({ ...entry, relevanceScore });
                }
            });

            // Trier par pertinence comme moi je trie mes résultats
            return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
        }

        // Construire le contexte comme moi je construis mes réponses
        function buildContextFromResults(results, queryAnalysis) {
            const topResults = results.slice(0, 3); // Les 3 plus pertinents

            let context = '\n\n=== CONTEXTE MÉMOIRE THERMIQUE ===\n';
            context += `Recherche pour: "${queryAnalysis.originalQuery}"\n`;
            context += `Fichiers trouvés: ${results.length}, Utilisés: ${topResults.length}\n\n`;

            topResults.forEach((result, i) => {
                context += `FICHIER ${i+1} [Score: ${result.relevanceScore}]:\n`;
                context += `👤 Utilisateur: "${result.user}"\n`;
                context += `🤖 Assistant: "${result.response}"\n`;
                context += `📅 Date: ${new Date(result.timestamp).toLocaleString()}\n`;
                if (result.keywords) context += `🏷️ Mots-clés: ${result.keywords.join(', ')}\n`;
                context += '\n';
            });

            context += '=== INSTRUCTION ===\n';
            context += 'Utilise ce contexte pour répondre de manière cohérente et personnalisée.\n';
            context += 'Si tu trouves des informations sur l\'utilisateur, utilise-les dans ta réponse.\n\n';

            return context;
        }

        // Test simple des fonctions
        function runSimpleTest() {
            log('🚀 DÉBUT TEST SIMPLE MÉMOIRE THERMIQUE', 'success');
            log('=' .repeat(50), 'info');
            
            // Reset
            thermalMemory = [];
            document.getElementById('results').innerHTML = '';
            
            try {
                // Test 1: Sauvegarde
                log('📝 Test 1: Sauvegarde mémoire', 'info');
                saveThermalMemory('Agent1', 'Bonjour, je m\'appelle Jean-Luc', 'Bonjour Jean-Luc ! Ravi de vous rencontrer.');
                
                if (thermalMemory.length === 1) {
                    addResult('✅ Test 1 RÉUSSI: Sauvegarde fonctionne', 'success');
                } else {
                    throw new Error(`Attendu 1 entrée, trouvé ${thermalMemory.length}`);
                }
                
                // Test 2: Recherche
                log('📝 Test 2: Recherche mémoire', 'info');
                const searchResult = searchMemory('Tu te souviens de mon nom ?');
                
                if (searchResult && searchResult.includes('Jean-Luc')) {
                    addResult('✅ Test 2 RÉUSSI: Recherche trouve "Jean-Luc"', 'success');
                    log('🎉 SUCCÈS: La mémoire thermique fonctionne parfaitement !', 'success');
                } else {
                    throw new Error('Nom "Jean-Luc" non trouvé dans le contexte');
                }
                
                // Test 3: Triggers
                log('📝 Test 3: Détection triggers', 'info');
                const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom'];
                let triggerCount = 0;
                
                triggers.forEach(trigger => {
                    const result = searchMemory(trigger + ' de quelque chose');
                    if (result.length > 0) triggerCount++;
                });
                
                if (triggerCount === triggers.length) {
                    addResult('✅ Test 3 RÉUSSI: Tous les triggers détectés', 'success');
                } else {
                    addResult(`⚠️ Test 3 PARTIEL: ${triggerCount}/${triggers.length} triggers`, 'warning');
                }
                
                log('=' .repeat(50), 'info');
                log('🏁 TEST SIMPLE TERMINÉ', 'success');
                
                if (thermalMemory.length === 1 && searchResult.includes('Jean-Luc')) {
                    addResult('🎉 VERDICT: MÉMOIRE THERMIQUE 100% FONCTIONNELLE !', 'success');
                } else {
                    addResult('❌ VERDICT: Problèmes détectés', 'error');
                }
                
            } catch (error) {
                log(`❌ Test échoué: ${error.message}`, 'error');
                addResult(`❌ ÉCHEC: ${error.message}`, 'error');
            }
        }

        // Test avec agent réel
        async function testWithRealAgent() {
            log('🤖 TEST AVEC AGENT DEEPSEEK R1 8B RÉEL', 'success');
            
            const realTestBtn = document.getElementById('realTestBtn');
            realTestBtn.disabled = true;
            
            try {
                // Test connexion
                log('🔧 Test connexion serveur...', 'info');
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);
                
                const healthResponse = await fetch('http://localhost:8000/health', { 
                    signal: controller.signal 
                });
                clearTimeout(timeoutId);
                
                if (!healthResponse.ok) throw new Error('Serveur inaccessible');
                
                log('✅ Serveur accessible', 'success');
                addResult('✅ Connexion serveur OK', 'success');
                
                // Reset mémoire
                thermalMemory = [];
                
                // Test 1: Présentation
                log('📝 Envoi présentation à l\'agent...', 'info');
                const message1 = 'Bonjour, je m\'appelle Jean-Luc';
                const response1 = await callAgent(message1);
                saveThermalMemory('Agent1', message1, response1);
                
                log(`👤 "${message1}"`, 'info');
                log(`🤖 "${response1}"`, 'info');
                
                // Test 2: Question mémoire
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                log('📝 Test rappel mémoire...', 'info');
                const message2 = 'Tu te souviens de mon nom ?';
                const memoryContext = searchMemory(message2);
                const promptWithMemory = message2 + memoryContext;
                
                const response2 = await callAgent(promptWithMemory);
                
                log(`👤 "${message2}"`, 'info');
                log(`🤖 "${response2}"`, 'info');
                
                // Vérification
                const remembers = response2.toLowerCase().includes('jean-luc') || 
                                 response2.toLowerCase().includes('jean luc');
                
                if (remembers) {
                    log('🎉 SUCCÈS: L\'agent se souvient !', 'success');
                    addResult('🎉 AGENT SE SOUVIENT DE "JEAN-LUC" !', 'success');
                } else {
                    log('❌ Échec: L\'agent ne se souvient pas', 'error');
                    addResult('❌ Agent ne se souvient pas', 'error');
                }
                
            } catch (error) {
                log(`❌ Test agent échoué: ${error.message}`, 'error');
                addResult(`❌ Erreur: ${error.message}`, 'error');
            } finally {
                realTestBtn.disabled = false;
            }
        }

        async function callAgent(prompt) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);
            
            const response = await fetch('http://localhost:8000/completion', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                signal: controller.signal,
                body: JSON.stringify({
                    prompt: prompt,
                    n_predict: 100,
                    temperature: 0.7,
                    stream: false
                })
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) throw new Error(`Erreur ${response.status}`);
            
            const data = await response.json();
            return (data.content || data.text || '').trim();
        }

        function clearTest() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('log').innerHTML = '[EFFACÉ] Prêt pour un nouveau test';
            thermalMemory = [];
        }

        // Chargement mémoire au démarrage comme moi je charge mes fichiers
        function loadThermalMemory() {
            try {
                const saved = localStorage.getItem('jarvis_thermal_memory');
                if (saved) {
                    thermalMemory = JSON.parse(saved);
                    log(`📁 Mémoire chargée: ${thermalMemory.length} entrée(s) récupérées`, 'success');

                    // Afficher un résumé comme mes logs de démarrage
                    const types = {};
                    thermalMemory.forEach(entry => {
                        types[entry.context_type] = (types[entry.context_type] || 0) + 1;
                    });

                    log(`📊 Types: ${Object.entries(types).map(([k,v]) => `${k}:${v}`).join(', ')}`, 'info');
                } else {
                    log('📁 Aucune mémoire sauvegardée trouvée', 'info');
                }
            } catch (error) {
                log(`❌ Erreur chargement mémoire: ${error.message}`, 'error');
                thermalMemory = [];
            }
        }

        // Initialisation
        loadThermalMemory(); // Charger la mémoire comme moi au démarrage
        log('🧪 Test simple mémoire thermique prêt', 'success');
        log('💡 Cliquez "Test Fonctions Mémoire" pour commencer', 'info');
    </script>
</body>
</html>
