<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Simple - Mémoire Thermique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px; border-radius: 15px;
        }
        .test-step {
            background: rgba(255,255,255,0.1);
            padding: 20px; margin: 15px 0;
            border-radius: 10px; border-left: 4px solid #4CAF50;
        }
        .result {
            padding: 15px; margin: 10px 0;
            border-radius: 8px; font-family: monospace;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 15px 30px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 16px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        #log {
            background: #000; color: #0f0; padding: 20px;
            border-radius: 10px; font-family: monospace;
            font-size: 14px; height: 300px; overflow-y: auto;
            margin: 20px 0; border: 2px solid #0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Simple - Mémoire Thermique</h1>
        <p>Test direct et simple pour vérifier que la mémoire thermique fonctionne</p>

        <div class="test-step">
            <h3>🎯 Plan de Test</h3>
            <ol>
                <li>Tester les fonctions de base (sauvegarde/recherche)</li>
                <li>Simuler: "Bonjour, je m'appelle Jean-Luc"</li>
                <li>Simuler: "Tu te souviens de mon nom ?"</li>
                <li>Vérifier que le contexte contient "Jean-Luc"</li>
            </ol>
        </div>

        <button onclick="runSimpleTest()">🚀 Test Fonctions Mémoire</button>
        <button onclick="testWithRealAgent()" id="realTestBtn">🤖 Test avec Agent Réel</button>
        <button onclick="clearTest()">🗑️ Effacer</button>

        <div id="log">[PRÊT] Cliquez sur un bouton pour commencer</div>

        <div id="results"></div>
    </div>

    <script>
        let thermalMemory = [];

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#0f0', error: '#f44', success: '#4f4', warning: '#fa0' };
            logDiv.innerHTML += `<div style="color: ${colors[type]}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        // Fonctions mémoire thermique (exactes de l'interface)
        function saveThermalMemory(agent, userMessage, agentResponse) {
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                agent: agent,
                user: userMessage,
                response: agentResponse
            };
            
            thermalMemory.push(entry);
            log(`💾 Sauvegardé [${agent}]: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`, 'success');
            log(`📊 Mémoire contient maintenant ${thermalMemory.length} entrée(s)`, 'info');
            
            // Debug complet
            console.log('🧠 Entrée sauvegardée:', entry);
            console.log('🧠 Mémoire complète:', thermalMemory);
        }

        function searchMemory(query) {
            log(`🔍 Recherche pour: "${query}"`, 'info');
            log(`🧠 Mémoire contient ${thermalMemory.length} entrée(s)`, 'info');
            
            if (thermalMemory.length === 0) {
                log('⚠️ Mémoire vide', 'warning');
                return '';
            }
            
            // Afficher le contenu pour debug
            thermalMemory.forEach((entry, i) => {
                log(`📋 Entrée ${i+1}: "${entry.user}" → "${entry.response?.substring(0, 30)}..."`, 'info');
            });
            
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom', 'comment je', 'qui suis'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                log('🎯 Trigger détecté - recherche active', 'success');
                
                const results = thermalMemory.filter(entry => 
                    entry.user?.toLowerCase().includes('appelle') || 
                    entry.user?.toLowerCase().includes('nom') ||
                    entry.user?.toLowerCase().includes('suis') ||
                    entry.response?.toLowerCase().includes('jean-luc') ||
                    entry.response?.toLowerCase().includes('bonjour')
                );
                
                log(`🔍 Résultats trouvés: ${results.length}`, results.length > 0 ? 'success' : 'warning');
                
                if (results.length > 0) {
                    const context = '\n\nCONTEXTE MÉMOIRE THERMIQUE:\n' + 
                           results.map(r => `- Précédemment: "${r.user}" → "${r.response}"`).join('\n') + 
                           '\nUtilise ce contexte pour répondre de manière cohérente.\n';
                    
                    log(`🧠 Contexte généré: ${context.length} caractères`, 'success');
                    log(`📋 Contexte: ${context.substring(0, 100)}...`, 'info');
                    return context;
                }
            } else {
                log('⚠️ Aucun trigger détecté', 'warning');
            }
            
            return '';
        }

        // Test simple des fonctions
        function runSimpleTest() {
            log('🚀 DÉBUT TEST SIMPLE MÉMOIRE THERMIQUE', 'success');
            log('=' .repeat(50), 'info');
            
            // Reset
            thermalMemory = [];
            document.getElementById('results').innerHTML = '';
            
            try {
                // Test 1: Sauvegarde
                log('📝 Test 1: Sauvegarde mémoire', 'info');
                saveThermalMemory('Agent1', 'Bonjour, je m\'appelle Jean-Luc', 'Bonjour Jean-Luc ! Ravi de vous rencontrer.');
                
                if (thermalMemory.length === 1) {
                    addResult('✅ Test 1 RÉUSSI: Sauvegarde fonctionne', 'success');
                } else {
                    throw new Error(`Attendu 1 entrée, trouvé ${thermalMemory.length}`);
                }
                
                // Test 2: Recherche
                log('📝 Test 2: Recherche mémoire', 'info');
                const searchResult = searchMemory('Tu te souviens de mon nom ?');
                
                if (searchResult && searchResult.includes('Jean-Luc')) {
                    addResult('✅ Test 2 RÉUSSI: Recherche trouve "Jean-Luc"', 'success');
                    log('🎉 SUCCÈS: La mémoire thermique fonctionne parfaitement !', 'success');
                } else {
                    throw new Error('Nom "Jean-Luc" non trouvé dans le contexte');
                }
                
                // Test 3: Triggers
                log('📝 Test 3: Détection triggers', 'info');
                const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom'];
                let triggerCount = 0;
                
                triggers.forEach(trigger => {
                    const result = searchMemory(trigger + ' de quelque chose');
                    if (result.length > 0) triggerCount++;
                });
                
                if (triggerCount === triggers.length) {
                    addResult('✅ Test 3 RÉUSSI: Tous les triggers détectés', 'success');
                } else {
                    addResult(`⚠️ Test 3 PARTIEL: ${triggerCount}/${triggers.length} triggers`, 'warning');
                }
                
                log('=' .repeat(50), 'info');
                log('🏁 TEST SIMPLE TERMINÉ', 'success');
                
                if (thermalMemory.length === 1 && searchResult.includes('Jean-Luc')) {
                    addResult('🎉 VERDICT: MÉMOIRE THERMIQUE 100% FONCTIONNELLE !', 'success');
                } else {
                    addResult('❌ VERDICT: Problèmes détectés', 'error');
                }
                
            } catch (error) {
                log(`❌ Test échoué: ${error.message}`, 'error');
                addResult(`❌ ÉCHEC: ${error.message}`, 'error');
            }
        }

        // Test avec agent réel
        async function testWithRealAgent() {
            log('🤖 TEST AVEC AGENT DEEPSEEK R1 8B RÉEL', 'success');
            
            const realTestBtn = document.getElementById('realTestBtn');
            realTestBtn.disabled = true;
            
            try {
                // Test connexion
                log('🔧 Test connexion serveur...', 'info');
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);
                
                const healthResponse = await fetch('http://localhost:8000/health', { 
                    signal: controller.signal 
                });
                clearTimeout(timeoutId);
                
                if (!healthResponse.ok) throw new Error('Serveur inaccessible');
                
                log('✅ Serveur accessible', 'success');
                addResult('✅ Connexion serveur OK', 'success');
                
                // Reset mémoire
                thermalMemory = [];
                
                // Test 1: Présentation
                log('📝 Envoi présentation à l\'agent...', 'info');
                const message1 = 'Bonjour, je m\'appelle Jean-Luc';
                const response1 = await callAgent(message1);
                saveThermalMemory('Agent1', message1, response1);
                
                log(`👤 "${message1}"`, 'info');
                log(`🤖 "${response1}"`, 'info');
                
                // Test 2: Question mémoire
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                log('📝 Test rappel mémoire...', 'info');
                const message2 = 'Tu te souviens de mon nom ?';
                const memoryContext = searchMemory(message2);
                const promptWithMemory = message2 + memoryContext;
                
                const response2 = await callAgent(promptWithMemory);
                
                log(`👤 "${message2}"`, 'info');
                log(`🤖 "${response2}"`, 'info');
                
                // Vérification
                const remembers = response2.toLowerCase().includes('jean-luc') || 
                                 response2.toLowerCase().includes('jean luc');
                
                if (remembers) {
                    log('🎉 SUCCÈS: L\'agent se souvient !', 'success');
                    addResult('🎉 AGENT SE SOUVIENT DE "JEAN-LUC" !', 'success');
                } else {
                    log('❌ Échec: L\'agent ne se souvient pas', 'error');
                    addResult('❌ Agent ne se souvient pas', 'error');
                }
                
            } catch (error) {
                log(`❌ Test agent échoué: ${error.message}`, 'error');
                addResult(`❌ Erreur: ${error.message}`, 'error');
            } finally {
                realTestBtn.disabled = false;
            }
        }

        async function callAgent(prompt) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);
            
            const response = await fetch('http://localhost:8000/completion', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                signal: controller.signal,
                body: JSON.stringify({
                    prompt: prompt,
                    n_predict: 100,
                    temperature: 0.7,
                    stream: false
                })
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) throw new Error(`Erreur ${response.status}`);
            
            const data = await response.json();
            return (data.content || data.text || '').trim();
        }

        function clearTest() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('log').innerHTML = '[EFFACÉ] Prêt pour un nouveau test';
            thermalMemory = [];
        }

        // Initialisation
        log('🧪 Test simple mémoire thermique prêt', 'success');
        log('💡 Cliquez "Test Fonctions Mémoire" pour commencer', 'info');
    </script>
</body>
</html>
