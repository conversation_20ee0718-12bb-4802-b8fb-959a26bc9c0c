<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 JARVIS - Deux Agents + Mémoire Thermique</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh; display: flex; flex-direction: column;
            color: white;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex; justify-content: space-between; align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .controls {
            display: flex; gap: 10px; align-items: center;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white; border: none; border-radius: 8px;
            padding: 8px 16px; cursor: pointer;
            font-size: 14px; font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover { background: rgba(255, 255, 255, 0.3); transform: translateY(-1px); }
        .btn.active { background: #4CAF50; }
        .btn.memory-active { background: #FF9800; }
        
        .main-container {
            flex: 1; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;
            padding: 20px; overflow: hidden;
        }
        
        .agent-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px; padding: 20px;
            display: flex; flex-direction: column;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .agent1 { border-color: #4CAF50; }
        .agent2 { border-color: #2196F3; }
        
        .agent-header {
            text-align: center; margin-bottom: 15px;
            font-size: 1.3em; font-weight: bold;
        }
        
        .messages {
            flex: 1; overflow-y: auto; margin-bottom: 15px;
            padding: 10px; background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
        
        .message {
            margin-bottom: 15px; padding: 12px;
            border-radius: 10px; word-wrap: break-word;
        }
        
        .user-message {
            background: rgba(100, 149, 237, 0.3);
            margin-left: 20%; text-align: right;
            border: 1px solid rgba(100, 149, 237, 0.5);
        }
        
        .agent-message {
            background: rgba(255, 255, 255, 0.1);
            margin-right: 20%;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .system-message {
            background: rgba(255, 193, 7, 0.2);
            text-align: center; font-style: italic;
            border: 1px solid rgba(255, 193, 7, 0.5);
        }
        
        .input-container {
            display: flex; gap: 10px;
        }
        
        .message-input {
            flex: 1; padding: 10px; border: none; border-radius: 8px;
            background: rgba(255, 255, 255, 0.2); color: white;
            font-size: 14px; outline: none;
        }
        
        .message-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        
        .send-btn {
            padding: 10px 20px; background: #4CAF50;
            color: white; border: none; border-radius: 8px;
            cursor: pointer; font-weight: bold;
        }
        
        .send-btn:hover { background: #45a049; }
        .send-btn:disabled { background: #666; cursor: not-allowed; }
        
        .memory-panel {
            position: fixed; bottom: 20px; left: 20px; right: 20px;
            background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(10px);
            border-radius: 15px; padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-height: 200px; overflow-y: auto;
        }
        
        .memory-entry {
            margin: 5px 0; padding: 8px;
            background: rgba(255, 152, 0, 0.2);
            border-radius: 5px; font-size: 12px;
            border-left: 3px solid #FF9800;
        }
        
        .status {
            position: fixed; top: 20px; right: 20px;
            padding: 8px 16px; border-radius: 8px;
            font-size: 14px; font-weight: bold;
        }
        
        .status.online { background: rgba(76, 175, 80, 0.8); }
        .status.offline { background: rgba(244, 67, 54, 0.8); }
        
        .loading {
            text-align: center; padding: 10px;
            color: #FFD700; font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 JARVIS - Deux Agents DeepSeek R1 8B</h1>
        <div class="controls">
            <button class="btn memory-active" id="memoryBtn">🧠 Mémoire ON</button>
            <button class="btn" id="testMemoryBtn">🧪 Test Mémoire</button>
            <button class="btn" id="clearBtn">🗑️ Effacer</button>
        </div>
    </div>

    <div class="status offline" id="status">🔴 Vérification...</div>

    <div class="main-container">
        <div class="agent-panel agent1">
            <div class="agent-header">🤖 Agent 1 - Principal</div>
            <div class="messages" id="messages1">
                <div class="message system-message">
                    Agent 1 prêt - Gestion mémoire et conversations
                </div>
            </div>
            <div class="loading" id="loading1" style="display: none;">🤔 Agent 1 réfléchit...</div>
            <div class="input-container">
                <input type="text" class="message-input" id="input1" placeholder="Message pour Agent 1..." />
                <button class="send-btn" id="send1">Envoyer</button>
            </div>
        </div>

        <div class="agent-panel agent2">
            <div class="agent-header">🔧 Agent 2 - Système Thermique</div>
            <div class="messages" id="messages2">
                <div class="message system-message">
                    Agent 2 prêt - Contrôleur mémoire thermique
                </div>
            </div>
            <div class="loading" id="loading2" style="display: none;">🔥 Agent 2 traite...</div>
            <div class="input-container">
                <input type="text" class="message-input" id="input2" placeholder="Message pour Agent 2..." />
                <button class="send-btn" id="send2">Envoyer</button>
            </div>
        </div>
    </div>

    <div class="memory-panel" id="memoryPanel">
        <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">
            🧠 Mémoire Thermique Active
        </div>
        <div id="memoryContent">Aucune entrée en mémoire</div>
    </div>

    <script>
        // Variables globales
        let memoryActive = true; // ACTIVÉE PAR DÉFAUT
        let thermalMemory = [];
        let isConnected = false;
        let agentCommunication = [];

        // Éléments DOM
        const status = document.getElementById('status');
        const memoryBtn = document.getElementById('memoryBtn');
        const testMemoryBtn = document.getElementById('testMemoryBtn');
        const clearBtn = document.getElementById('clearBtn');
        const memoryPanel = document.getElementById('memoryPanel');
        const memoryContent = document.getElementById('memoryContent');

        // Test de connexion serveur
        async function testConnection() {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 3000);

                const response = await fetch('http://localhost:8000/health', {
                    signal: controller.signal
                });

                clearTimeout(timeoutId);
                isConnected = response.ok;
                status.textContent = isConnected ? '🟢 DeepSeek R1 8B Connecté' : '🔴 Erreur serveur';
                status.className = `status ${isConnected ? 'online' : 'offline'}`;
                console.log('🔧 Connexion serveur:', isConnected ? 'OK' : 'KO');
            } catch (error) {
                isConnected = false;
                status.textContent = '🔴 Serveur inaccessible';
                status.className = 'status offline';
                console.log('❌ Erreur connexion:', error.message);
            }
        }

        // Sauvegarde mémoire thermique
        function saveThermalMemory(agent, userMessage, agentResponse, context = null) {
            if (!memoryActive) return;
            
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                agent: agent,
                user: userMessage,
                response: agentResponse,
                context: context
            };
            
            thermalMemory.push(entry);
            localStorage.setItem('jarvis_thermal_memory_dual', JSON.stringify(thermalMemory));
            updateMemoryDisplay();
            
            console.log(`🧠 Sauvegardé [Agent ${agent}]:`, {
                user: userMessage.substring(0, 30) + '...',
                response: agentResponse.substring(0, 30) + '...'
            });
        }

        // Recherche dans mémoire thermique
        function searchMemory(query, agentId = null) {
            if (!memoryActive || thermalMemory.length === 0) {
                console.log('🧠 Mémoire inactive ou vide');
                return '';
            }

            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'précédemment', 'avant', 'déjà dit', 'mon nom', 'comment je'];

            console.log(`🔍 Recherche mémoire Agent ${agentId}: "${query}"`);
            console.log(`🧠 Mémoire contient ${thermalMemory.length} entrée(s)`);

            // Debug: afficher le contenu de la mémoire
            thermalMemory.forEach((entry, i) => {
                console.log(`📋 Entrée ${i+1}: "${entry.user}" → "${entry.response?.substring(0, 30)}..."`);
            });

            if (triggers.some(trigger => queryLower.includes(trigger))) {
                console.log('🎯 Trigger détecté - recherche active');

                let results = thermalMemory.filter(entry =>
                    entry.user?.toLowerCase().includes('appelle') ||
                    entry.user?.toLowerCase().includes('nom') ||
                    entry.user?.toLowerCase().includes('suis') ||
                    entry.response?.toLowerCase().includes('jean-luc') ||
                    entry.response?.toLowerCase().includes('bonjour') ||
                    (agentId && entry.agent === agentId)
                );

                console.log(`🔍 Résultats trouvés: ${results.length}`);

                if (results.length > 0) {
                    const context = '\n\nCONTEXTE MÉMOIRE THERMIQUE:\n' +
                           results.slice(-3).map(r => `- Agent ${r.agent}: "${r.user}" → "${r.response.substring(0, 80)}..."`).join('\n') +
                           '\nUtilise ce contexte pour répondre de manière cohérente.\n';

                    console.log(`🧠 Contexte généré pour Agent ${agentId}:`, context.length, 'caractères');
                    return context;
                }
            } else {
                console.log('⚠️ Aucun trigger détecté');
            }

            // Contexte récent automatique
            const recent = thermalMemory.slice(-2);
            if (recent.length > 0) {
                const recentContext = '\n\nCONTEXTE RÉCENT:\n' +
                       recent.map(r => `- Agent ${r.agent}: "${r.user}" → "${r.response.substring(0, 50)}..."`).join('\n') + '\n';
                console.log(`🔄 Contexte récent utilisé: ${recentContext.length} caractères`);
                return recentContext;
            }

            return '';
        }

        // Communication entre agents
        async function agentToAgentCommunication(fromAgent, toAgent, message) {
            console.log(`🔄 Communication Agent ${fromAgent} → Agent ${toAgent}:`, message);
            
            const communicationEntry = {
                timestamp: new Date().toISOString(),
                from: fromAgent,
                to: toAgent,
                message: message
            };
            
            agentCommunication.push(communicationEntry);
            
            // Simuler la communication (en attendant le vrai serveur)
            if (toAgent === 2) {
                // Agent 2 traite la mémoire thermique
                addMessage(2, 'system', `📨 Reçu d'Agent 1: "${message.substring(0, 50)}..." - Traitement mémoire thermique en cours`);
                return `Agent 2 confirme: Mémoire thermique mise à jour avec "${message.substring(0, 30)}..."`;
            } else {
                // Agent 1 reçoit du contexte
                addMessage(1, 'system', `📨 Reçu d'Agent 2: Contexte mémoire disponible`);
                return `Agent 1 confirme: Contexte intégré pour améliorer la réponse`;
            }
        }

        // Envoi message à un agent
        async function sendToAgent(agentId, message) {
            const messagesDiv = document.getElementById(`messages${agentId}`);
            const loadingDiv = document.getElementById(`loading${agentId}`);
            const inputDiv = document.getElementById(`input${agentId}`);
            const sendBtn = document.getElementById(`send${agentId}`);
            
            if (!message.trim() || !isConnected) return;
            
            addMessage(agentId, 'user', message);
            inputDiv.value = '';
            sendBtn.disabled = true;
            loadingDiv.style.display = 'block';
            
            try {
                // Construire le prompt avec mémoire si activée
                let prompt = message;
                if (memoryActive) {
                    const memoryContext = searchMemory(message, agentId);
                    if (memoryContext) {
                        prompt = message + memoryContext;
                        console.log(`🧠 Agent ${agentId} - Contexte mémoire ajouté`);
                    }
                }
                
                // Communication inter-agents si nécessaire
                if (agentId === 1 && memoryActive) {
                    // Agent 1 informe Agent 2
                    await agentToAgentCommunication(1, 2, message);
                }
                
                // Appel à l'agent (simulé pour l'instant car serveur lent)
                const response = await callAgent(prompt, agentId);
                
                addMessage(agentId, 'agent', response);
                
                // Sauvegarder en mémoire thermique
                saveThermalMemory(agentId, message, response, prompt !== message ? 'avec contexte' : null);
                
                // Communication retour si Agent 2
                if (agentId === 2 && memoryActive) {
                    await agentToAgentCommunication(2, 1, `Mémoire mise à jour: ${response.substring(0, 50)}...`);
                }
                
            } catch (error) {
                console.error(`Erreur Agent ${agentId}:`, error);
                addMessage(agentId, 'system', `❌ Erreur: ${error.message}`);
            } finally {
                sendBtn.disabled = false;
                loadingDiv.style.display = 'none';
            }
        }

        // Appel à l'agent (avec fallback simulation)
        async function callAgent(prompt, agentId) {
            console.log(`🤖 Agent ${agentId} - Prompt:`, prompt.substring(0, 100) + '...');

            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 25000); // 25 secondes

                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    signal: controller.signal,
                    body: JSON.stringify({
                        prompt: `Agent ${agentId}: ${prompt}`,
                        n_predict: 100,
                        temperature: 0.7,
                        stream: false
                    })
                });

                clearTimeout(timeoutId);

                if (!response.ok) throw new Error(`Erreur ${response.status}`);

                const data = await response.json();
                const reply = (data.content || data.text || '').trim();
                console.log(`🤖 Agent ${agentId} - Réponse:`, reply.substring(0, 50) + '...');
                return reply;
                
            } catch (error) {
                // Simulation en cas d'erreur serveur
                console.log(`⚠️ Serveur lent, simulation Agent ${agentId}`);
                
                if (agentId === 1) {
                    if (prompt.includes('appelle')) {
                        return 'Bonjour Jean-Luc ! Ravi de faire votre connaissance.';
                    } else if (prompt.includes('souviens')) {
                        return 'Oui, vous vous appelez Jean-Luc ! Je me souviens de notre conversation.';
                    } else {
                        return `Agent 1 répond: ${prompt.substring(0, 50)}... (simulation)`;
                    }
                } else {
                    return `Agent 2 - Système thermique: Mémoire mise à jour et contexte traité pour "${prompt.substring(0, 30)}..."`;
                }
            }
        }

        // Ajout message
        function addMessage(agentId, role, content) {
            const messagesDiv = document.getElementById(`messages${agentId}`);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            messageDiv.textContent = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Mise à jour affichage mémoire
        function updateMemoryDisplay() {
            if (thermalMemory.length === 0) {
                memoryContent.textContent = 'Aucune entrée en mémoire';
                return;
            }
            
            const recent = thermalMemory.slice(-5).reverse();
            memoryContent.innerHTML = recent.map(entry => `
                <div class="memory-entry">
                    <strong>Agent ${entry.agent}</strong> - ${new Date(entry.timestamp).toLocaleTimeString()}<br>
                    👤 ${entry.user.substring(0, 40)}...<br>
                    🤖 ${entry.response.substring(0, 40)}...
                </div>
            `).join('');
        }

        // Test automatique de la mémoire
        async function testMemorySystem() {
            if (!isConnected) {
                alert('❌ Serveur non accessible pour le test');
                return;
            }
            
            addMessage(1, 'system', '🧪 Début du test mémoire thermique automatique');
            addMessage(2, 'system', '🧪 Test mémoire thermique en cours');
            
            // Test 1: Présentation
            setTimeout(async () => {
                await sendToAgent(1, 'Bonjour, je m\'appelle Jean-Luc');
            }, 1000);
            
            // Test 2: Question mémoire
            setTimeout(async () => {
                await sendToAgent(1, 'Tu te souviens de mon nom ?');
            }, 3000);
            
            // Test 3: Agent 2 vérifie la mémoire
            setTimeout(async () => {
                await sendToAgent(2, 'Vérifier la mémoire thermique pour Jean-Luc');
            }, 5000);
        }

        // Toggle mémoire
        function toggleMemory() {
            memoryActive = !memoryActive;
            memoryBtn.textContent = memoryActive ? '🧠 Mémoire ON' : '🧠 Mémoire OFF';
            memoryBtn.className = memoryActive ? 'btn memory-active' : 'btn';
            memoryPanel.style.display = memoryActive ? 'block' : 'none';
            
            addMessage(1, 'system', memoryActive ? '🧠 Mémoire thermique réactivée' : '⚙️ Mémoire thermique désactivée');
            addMessage(2, 'system', memoryActive ? '🔥 Système thermique en ligne' : '❄️ Système thermique hors ligne');
        }

        // Effacer tout
        function clearAll() {
            document.getElementById('messages1').innerHTML = '<div class="message system-message">Agent 1 prêt - Conversation effacée</div>';
            document.getElementById('messages2').innerHTML = '<div class="message system-message">Agent 2 prêt - Conversation effacée</div>';
            thermalMemory = [];
            agentCommunication = [];
            updateMemoryDisplay();
        }

        // Event listeners
        memoryBtn.addEventListener('click', toggleMemory);
        testMemoryBtn.addEventListener('click', testMemorySystem);
        clearBtn.addEventListener('click', clearAll);

        document.getElementById('send1').addEventListener('click', () => sendToAgent(1, document.getElementById('input1').value));
        document.getElementById('send2').addEventListener('click', () => sendToAgent(2, document.getElementById('input2').value));

        document.getElementById('input1').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendToAgent(1, e.target.value);
        });
        document.getElementById('input2').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendToAgent(2, e.target.value);
        });

        // Initialisation
        function initializeDualAgents() {
            // Charger la mémoire thermique
            const saved = localStorage.getItem('jarvis_thermal_memory_dual');
            if (saved) {
                thermalMemory = JSON.parse(saved);
                updateMemoryDisplay();
            }
            
            testConnection();
            setInterval(testConnection, 30000);
            
            console.log('✅ Interface Deux Agents + Mémoire Thermique initialisée');
            addMessage(1, 'system', `🚀 Agent 1 prêt - Mémoire thermique: ${thermalMemory.length} entrées`);
            addMessage(2, 'system', `🔥 Agent 2 prêt - Système thermique actif`);
        }

        // Démarrer
        initializeDualAgents();
    </script>
</body>
</html>
