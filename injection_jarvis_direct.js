// 🧠 INJECTION JARVIS DIRECTE DANS L'INTERFACE LLAMA.CPP
// Pas de proxy - injection directe via script

console.log('🧠 JARVIS - Injection directe démarrée');

// 🎯 ATTENDRE QUE LA PAGE SOIT CHARGÉE
function attendreChargement() {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialiserJarvis);
    } else {
        setTimeout(initialiserJarvis, 1000);
    }
}

function initialiserJarvis() {
    console.log('🧠 JARVIS - Initialisation');
    
    // 🏷️ CHANGER TITRE
    document.title = '🧠 JARVIS - Interface Cognitive';
    
    // 🎨 AJOUTER STYLES
    ajouterStyles();
    
    // 🧠 CRÉER PANNEAU
    creerPanneauJarvis();
    
    // 🚀 DÉMARRER SYSTÈME
    demarrerSystemeJarvis();
    
    console.log('✅ JARVIS - Système actif');
}

function ajouterStyles() {
    const style = document.createElement('style');
    style.textContent = `
        #jarvis-panel {
            position: fixed !important;
            top: 10px !important;
            right: 10px !important;
            width: 350px !important;
            background: rgba(0,0,0,0.95) !important;
            border: 2px solid #00ffff !important;
            border-radius: 10px !important;
            padding: 15px !important;
            color: #00ffff !important;
            font-family: 'Courier New', monospace !important;
            z-index: 999999 !important;
            box-shadow: 0 0 20px rgba(0,255,255,0.5) !important;
            font-size: 12px !important;
        }
        
        #jarvis-header {
            text-align: center !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #00ffff !important;
            padding-bottom: 8px !important;
        }
        
        #jarvis-buttons {
            display: flex !important;
            gap: 5px !important;
            margin-bottom: 8px !important;
            flex-wrap: wrap !important;
        }
        
        .jarvis-btn {
            flex: 1 !important;
            padding: 6px !important;
            background: #1a1a2e !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 10px !important;
            min-width: 70px !important;
            transition: all 0.2s !important;
        }
        
        .jarvis-btn:hover {
            background: #00ffff !important;
            color: #000 !important;
            transform: scale(1.05) !important;
        }
        
        #jarvis-status {
            background: rgba(0,255,255,0.1) !important;
            padding: 8px !important;
            border-radius: 5px !important;
            text-align: center !important;
            font-size: 11px !important;
            margin-top: 8px !important;
        }
        
        #jarvis-name-input {
            background: rgba(0,255,255,0.1) !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
            padding: 4px !important;
            border-radius: 4px !important;
            width: 120px !important;
            text-align: center !important;
            font-size: 11px !important;
        }
        
        .jarvis-minimized {
            height: 40px !important;
            overflow: hidden !important;
        }
    `;
    document.head.appendChild(style);
}

function creerPanneauJarvis() {
    const panel = document.createElement('div');
    panel.id = 'jarvis-panel';
    panel.innerHTML = `
        <div id="jarvis-header">
            <h3 style="margin: 0; font-size: 14px;">🧠 JARVIS</h3>
            <input type="text" id="jarvis-name-input" placeholder="Nom agent" value="JARVIS">
        </div>
        
        <div id="jarvis-buttons">
            <button class="jarvis-btn" onclick="jarvisMemoire()">🧠 Mémoire</button>
            <button class="jarvis-btn" onclick="jarvisEvoluer()">🚀 Évoluer</button>
            <button class="jarvis-btn" onclick="jarvisAnalyse()">🔍 Analyse</button>
        </div>
        
        <div id="jarvis-buttons">
            <button class="jarvis-btn" onclick="jarvisToggle()">📱 Réduire</button>
            <button class="jarvis-btn" onclick="jarvisReset()">🔄 Reset</button>
            <button class="jarvis-btn" onclick="jarvisInfo()">ℹ️ Info</button>
        </div>
        
        <div id="jarvis-status">
            <div>QI: <span id="jarvis-qi">341.0</span></div>
            <div>🧠 Mémoire Thermique Active</div>
            <div>⚡ Système Cognitif Opérationnel</div>
        </div>
    `;
    
    document.body.appendChild(panel);
}

function demarrerSystemeJarvis() {
    // 🧠 VARIABLES GLOBALES
    window.jarvisQI = 341.0;
    window.jarvisMinimized = false;
    window.jarvisConversations = 0;
    
    // 🧠 FONCTIONS JARVIS
    window.jarvisMemoire = function() {
        const nom = document.getElementById('jarvis-name-input').value || 'JARVIS';
        const prompt = `${nom}, utilise ta mémoire thermique pour te souvenir de nos conversations précédentes. Analyse tes souvenirs et explique ce que tu as appris.`;
        envoyerPromptJarvis(prompt);
        console.log('🧠 Mémoire thermique activée');
    };
    
    window.jarvisEvoluer = function() {
        window.jarvisQI += Math.random() * 3 + 1;
        document.getElementById('jarvis-qi').textContent = window.jarvisQI.toFixed(1);
        
        const nom = document.getElementById('jarvis-name-input').value || 'JARVIS';
        const prompt = `${nom}, ton QI vient d'évoluer à ${window.jarvisQI.toFixed(1)}. Explique tes nouvelles capacités cognitives et comment tu peux mieux m'aider maintenant.`;
        envoyerPromptJarvis(prompt);
        console.log('🚀 Évolution QI:', window.jarvisQI.toFixed(1));
    };
    
    window.jarvisAnalyse = function() {
        const nom = document.getElementById('jarvis-name-input').value || 'JARVIS';
        const prompt = `${nom}, analyse tes processus de pensée actuels. Explique comment tu traites l'information et quelles sont tes capacités d'analyse en temps réel.`;
        envoyerPromptJarvis(prompt);
        console.log('🔍 Analyse cognitive lancée');
    };
    
    window.jarvisToggle = function() {
        const panel = document.getElementById('jarvis-panel');
        if (window.jarvisMinimized) {
            panel.classList.remove('jarvis-minimized');
            window.jarvisMinimized = false;
        } else {
            panel.classList.add('jarvis-minimized');
            window.jarvisMinimized = true;
        }
    };
    
    window.jarvisReset = function() {
        window.jarvisQI = 341.0;
        window.jarvisConversations = 0;
        document.getElementById('jarvis-qi').textContent = '341.0';
        console.log('🔄 JARVIS Reset');
        alert('🔄 JARVIS système réinitialisé');
    };
    
    window.jarvisInfo = function() {
        alert(`🧠 JARVIS - Système Cognitif
        
QI Actuel: ${window.jarvisQI.toFixed(1)}
Conversations: ${window.jarvisConversations}
Mémoire: Thermique Active
Statut: Opérationnel

Version: 1.0 Direct Injection`);
    };
    
    // 🚀 ÉVOLUTION AUTOMATIQUE
    setInterval(() => {
        window.jarvisQI += Math.random() * 0.1;
        const qiElement = document.getElementById('jarvis-qi');
        if (qiElement) {
            qiElement.textContent = window.jarvisQI.toFixed(1);
        }
    }, 15000);
    
    // 🏷️ MISE À JOUR NOM
    const nameInput = document.getElementById('jarvis-name-input');
    if (nameInput) {
        nameInput.addEventListener('input', function() {
            const newName = this.value.trim().toUpperCase();
            if (newName) {
                document.title = `${newName} - Interface Cognitive`;
                console.log('🏷️ Nom agent:', newName);
            }
        });
    }
}

function envoyerPromptJarvis(prompt) {
    // 🎯 TROUVER ZONE DE SAISIE
    const selectors = [
        'textarea',
        'input[type="text"]:not(#jarvis-name-input)',
        '[contenteditable="true"]',
        '.input-field',
        '#prompt-input',
        '#message-input'
    ];
    
    let input = null;
    for (const selector of selectors) {
        input = document.querySelector(selector);
        if (input && input.id !== 'jarvis-name-input') break;
    }
    
    if (input) {
        // 📝 INSÉRER PROMPT
        if (input.tagName === 'TEXTAREA' || input.tagName === 'INPUT') {
            input.value = prompt;
        } else {
            input.textContent = prompt;
        }
        
        input.focus();
        window.jarvisConversations++;
        
        // 🚀 ESSAYER D'ENVOYER
        setTimeout(() => {
            // Chercher bouton d'envoi
            const sendSelectors = [
                'button[type="submit"]',
                '.send-btn',
                '.submit-btn',
                'button:contains("Send")',
                'button:contains("Submit")',
                '[aria-label*="send"]'
            ];
            
            let sendBtn = null;
            for (const selector of sendSelectors) {
                sendBtn = document.querySelector(selector);
                if (sendBtn) break;
            }
            
            if (sendBtn) {
                sendBtn.click();
                console.log('✅ Prompt envoyé via bouton');
            } else {
                // Essayer Enter
                const event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                });
                input.dispatchEvent(event);
                console.log('✅ Prompt envoyé via Enter');
            }
        }, 200);
        
    } else {
        console.log('❌ Zone de saisie non trouvée');
        alert('❌ Impossible de trouver la zone de saisie');
    }
}

// 🚀 DÉMARRAGE
attendreChargement();
