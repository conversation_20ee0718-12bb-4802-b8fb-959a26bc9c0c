<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 DEBUG - Mémoire Thermique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 1200px; margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px; border-radius: 15px;
        }
        .debug-section {
            background: rgba(255,255,255,0.1);
            padding: 20px; margin: 15px 0;
            border-radius: 10px; border-left: 4px solid #4CAF50;
        }
        .step {
            background: rgba(0,0,0,0.3);
            padding: 15px; margin: 10px 0;
            border-radius: 8px; font-family: monospace;
            border-left: 3px solid #2196F3;
        }
        .result {
            background: rgba(76, 175, 80, 0.3);
            padding: 15px; margin: 10px 0;
            border-radius: 8px; font-family: monospace;
            border-left: 3px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
            padding: 15px; margin: 10px 0;
            border-radius: 8px; font-family: monospace;
            border-left: 3px solid #f44336;
        }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 15px 30px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 16px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        .memory-entry {
            background: rgba(255, 152, 0, 0.2);
            padding: 10px; margin: 5px 0;
            border-radius: 5px; font-size: 12px;
            border-left: 3px solid #FF9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 DEBUG - Mémoire Thermique JARVIS</h1>
        <p>Debug complet pour voir exactement ce qui se passe dans la mémoire thermique</p>

        <div class="debug-section">
            <h3>🎯 Test Étape par Étape</h3>
            <button onclick="step1_saveMemory()">1️⃣ Sauvegarder "Je m'appelle Jean-Luc"</button>
            <button onclick="step2_searchMemory()">2️⃣ Chercher "Tu te souviens de mon nom ?"</button>
            <button onclick="step3_fullTest()">3️⃣ Test Complet</button>
            <button onclick="clearDebug()">🗑️ Effacer</button>
        </div>

        <div id="debugOutput"></div>
    </div>

    <script>
        let thermalMemory = [];
        let debugOutput = document.getElementById('debugOutput');

        function addDebugStep(title, content, type = 'step') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<strong>${title}</strong><br>${content}`;
            debugOutput.appendChild(div);
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }

        // FONCTIONS MÉMOIRE EXACTES (comme dans l'interface)
        function saveThermalMemory(userMessage, agentResponse) {
            addDebugStep('💾 SAUVEGARDE MÉMOIRE', `User: "${userMessage}"<br>Agent: "${agentResponse}"`);
            
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse,
                keywords: extractKeywords(userMessage + ' ' + agentResponse),
                context_type: determineContextType(userMessage),
                importance: calculateImportance(userMessage, agentResponse)
            };
            
            thermalMemory.push(entry);
            localStorage.setItem('jarvis_thermal_memory_debug', JSON.stringify(thermalMemory));
            
            addDebugStep('✅ ENTRÉE CRÉÉE', 
                `ID: ${entry.id}<br>` +
                `Mots-clés: ${entry.keywords.join(', ')}<br>` +
                `Type: ${entry.context_type}<br>` +
                `Importance: ${entry.importance}/5<br>` +
                `Total entrées: ${thermalMemory.length}`, 'result');
            
            // Afficher la mémoire complète
            showMemoryContent();
        }

        function searchMemory(query) {
            addDebugStep('🔍 RECHERCHE MÉMOIRE', `Requête: "${query}"`);
            
            if (thermalMemory.length === 0) {
                addDebugStep('❌ MÉMOIRE VIDE', 'Aucune entrée en mémoire', 'error');
                return '';
            }
            
            // Analyser la requête
            const queryAnalysis = analyzeQuery(query);
            addDebugStep('🎯 ANALYSE REQUÊTE', 
                `Type: ${queryAnalysis.type}<br>` +
                `Mots-clés: ${queryAnalysis.keywords.join(', ')}<br>` +
                `Trigger mémoire: ${queryAnalysis.hasMemoryTrigger ? 'OUI' : 'NON'}`, 'step');
            
            // Rechercher
            const searchResults = performMemorySearch(queryAnalysis);
            addDebugStep('📁 RÉSULTATS RECHERCHE', 
                `Fichiers trouvés: ${searchResults.length}<br>` +
                searchResults.map((r, i) => `${i+1}. "${r.user}" [Score: ${r.relevanceScore}]`).join('<br>'), 'step');
            
            // Construire contexte
            if (searchResults.length > 0) {
                const context = buildContextFromResults(searchResults, queryAnalysis);
                addDebugStep('🧠 CONTEXTE GÉNÉRÉ', 
                    `Longueur: ${context.length} caractères<br>` +
                    `Aperçu: ${context.substring(0, 200)}...`, 'result');
                return context;
            } else {
                addDebugStep('❌ AUCUN RÉSULTAT', 'Aucun fichier pertinent trouvé', 'error');
                return '';
            }
        }

        // Fonctions utilitaires
        function extractKeywords(text) {
            const words = text.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(word => word.length > 2);
            
            const important = ['nom', 'appelle', 'suis', 'jean-luc', 'bonjour', 'souviens', 'rappelle'];
            return words.filter(word => important.includes(word) || word.length > 4);
        }

        function determineContextType(message) {
            const msg = message.toLowerCase();
            if (msg.includes('appelle') || msg.includes('nom')) return 'identity';
            if (msg.includes('souviens') || msg.includes('rappelle')) return 'memory_query';
            if (msg.includes('bonjour') || msg.includes('salut')) return 'greeting';
            return 'general';
        }

        function calculateImportance(user, response) {
            let score = 1;
            if (user.toLowerCase().includes('appelle')) score += 3;
            if (user.toLowerCase().includes('souviens')) score += 2;
            if (response.toLowerCase().includes('jean-luc')) score += 3;
            return Math.min(score, 5);
        }

        function analyzeQuery(query) {
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom', 'comment je', 'qui suis'];
            
            let type = 'general';
            if (queryLower.includes('souviens') || queryLower.includes('rappelle')) {
                type = 'memory_recall';
            } else if (queryLower.includes('nom') || queryLower.includes('appelle')) {
                type = 'identity_query';
            }
            
            const hasMemoryTrigger = triggers.some(trigger => queryLower.includes(trigger));
            const keywords = queryLower.split(' ').filter(word => word.length > 2);
            
            return { type, keywords, hasMemoryTrigger, originalQuery: query };
        }

        function performMemorySearch(queryAnalysis) {
            let results = [];
            
            thermalMemory.forEach(entry => {
                let relevanceScore = 0;
                
                // Score par mots-clés
                queryAnalysis.keywords.forEach(keyword => {
                    if (entry.keywords && entry.keywords.includes(keyword)) relevanceScore += 2;
                    if (entry.user.toLowerCase().includes(keyword)) relevanceScore += 1;
                    if (entry.agent.toLowerCase().includes(keyword)) relevanceScore += 1;
                });
                
                // Score par type
                if (entry.context_type === queryAnalysis.type) relevanceScore += 1;
                
                // Score par importance
                relevanceScore += entry.importance || 0;
                
                // Score spécial pour noms
                if (queryAnalysis.type === 'memory_recall' || queryAnalysis.type === 'identity_query') {
                    if (entry.user.toLowerCase().includes('appelle') || 
                        entry.user.toLowerCase().includes('nom') ||
                        entry.agent.toLowerCase().includes('jean-luc')) {
                        relevanceScore += 5;
                    }
                }
                
                if (relevanceScore > 0) {
                    results.push({ ...entry, relevanceScore });
                }
            });
            
            return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
        }

        function buildContextFromResults(results, queryAnalysis) {
            const topResults = results.slice(0, 3);
            
            let context = '\n\n=== CONTEXTE MÉMOIRE THERMIQUE ===\n';
            context += `Recherche: "${queryAnalysis.originalQuery}"\n`;
            context += `Fichiers: ${results.length}, Utilisés: ${topResults.length}\n\n`;
            
            topResults.forEach((result, i) => {
                context += `FICHIER ${i+1} [Score: ${result.relevanceScore}]:\n`;
                context += `👤 "${result.user}"\n`;
                context += `🤖 "${result.agent}"\n\n`;
            });
            
            context += 'INSTRUCTION: Utilise ce contexte pour répondre.\n\n';
            return context;
        }

        function showMemoryContent() {
            const memoryDiv = document.createElement('div');
            memoryDiv.className = 'debug-section';
            memoryDiv.innerHTML = '<h4>🧠 CONTENU MÉMOIRE ACTUEL</h4>';
            
            if (thermalMemory.length === 0) {
                memoryDiv.innerHTML += '<div class="error">Mémoire vide</div>';
            } else {
                thermalMemory.forEach((entry, i) => {
                    const entryDiv = document.createElement('div');
                    entryDiv.className = 'memory-entry';
                    entryDiv.innerHTML = `
                        <strong>Entrée ${i+1}</strong><br>
                        👤 "${entry.user}"<br>
                        🤖 "${entry.agent}"<br>
                        🏷️ ${entry.keywords.join(', ')} | Type: ${entry.context_type} | Score: ${entry.importance}
                    `;
                    memoryDiv.appendChild(entryDiv);
                });
            }
            
            debugOutput.appendChild(memoryDiv);
        }

        // Tests étape par étape
        function step1_saveMemory() {
            debugOutput.innerHTML = '';
            addDebugStep('🚀 ÉTAPE 1', 'Sauvegarde d\'une présentation en mémoire');
            saveThermalMemory('Bonjour, je m\'appelle Jean-Luc', 'Bonjour Jean-Luc ! Ravi de vous rencontrer.');
        }

        function step2_searchMemory() {
            addDebugStep('🚀 ÉTAPE 2', 'Recherche du nom dans la mémoire');
            const result = searchMemory('Tu te souviens de mon nom ?');
            
            if (result && result.includes('Jean-Luc')) {
                addDebugStep('🎉 SUCCÈS !', 'La mémoire a trouvé "Jean-Luc" dans le contexte !', 'result');
            } else {
                addDebugStep('❌ ÉCHEC !', 'La mémoire n\'a pas trouvé le nom', 'error');
            }
        }

        function step3_fullTest() {
            debugOutput.innerHTML = '';
            addDebugStep('🚀 TEST COMPLET', 'Simulation complète conversation avec mémoire');
            
            // Reset
            thermalMemory = [];
            
            // Étape 1
            setTimeout(() => {
                step1_saveMemory();
                
                // Étape 2
                setTimeout(() => {
                    step2_searchMemory();
                }, 1000);
            }, 500);
        }

        function clearDebug() {
            debugOutput.innerHTML = '';
            thermalMemory = [];
            localStorage.removeItem('jarvis_thermal_memory_debug');
            addDebugStep('🗑️ EFFACÉ', 'Mémoire et debug effacés');
        }

        // Chargement initial
        const saved = localStorage.getItem('jarvis_thermal_memory_debug');
        if (saved) {
            thermalMemory = JSON.parse(saved);
            addDebugStep('📁 CHARGEMENT', `${thermalMemory.length} entrée(s) chargée(s) depuis localStorage`);
            showMemoryContent();
        } else {
            addDebugStep('📁 DÉMARRAGE', 'Aucune mémoire sauvegardée - Prêt pour les tests');
        }
    </script>
</body>
</html>
