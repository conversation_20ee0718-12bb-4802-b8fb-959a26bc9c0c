// 🧠 INJECTEUR JARVIS - INJECTION DIRECTE DANS LLAMA.CPP
const fs = require('fs');
const http = require('http');

console.log('🧠 INJECTEUR JARVIS - DÉMARRAGE');

// 📜 LIRE LE SCRIPT D'INJECTION
const scriptJarvis = fs.readFileSync('./injection_jarvis_direct.js', 'utf8');

// 🎯 CRÉER SERVEUR PROXY SIMPLE
const server = http.createServer((req, res) => {
    if (req.method === 'GET' && req.url === '/') {
        // 🌐 RÉCUPÉRER PAGE ORIGINALE
        const options = {
            hostname: '127.0.0.1',
            port: 8080,
            path: '/',
            method: 'GET'
        };

        const proxyReq = http.request(options, (proxyRes) => {
            let data = '';
            
            proxyRes.on('data', chunk => {
                data += chunk;
            });
            
            proxyRes.on('end', () => {
                try {
                    // 🧠 INJECTER JARVIS
                    const htmlModifie = injecterJarvis(data);
                    
                    res.writeHead(200, {
                        'Content-Type': 'text/html; charset=utf-8',
                        'Cache-Control': 'no-cache'
                    });
                    res.end(htmlModifie);
                    
                    console.log('✅ Page servie avec JARVIS injecté');
                    
                } catch (error) {
                    console.error('❌ Erreur injection:', error.message);
                    res.writeHead(500);
                    res.end('Erreur injection JARVIS');
                }
            });
        });

        proxyReq.on('error', (error) => {
            console.error('❌ Erreur proxy:', error.message);
            res.writeHead(500);
            res.end('Erreur connexion llama.cpp');
        });

        proxyReq.end();
        
    } else {
        // 🔄 REDIRIGER AUTRES REQUÊTES
        const options = {
            hostname: '127.0.0.1',
            port: 8080,
            path: req.url,
            method: req.method,
            headers: req.headers
        };

        delete options.headers.host;

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (error) => {
            console.error('❌ Erreur redirection:', error.message);
            res.writeHead(500);
            res.end('Erreur');
        });

        req.pipe(proxyReq);
    }
});

function injecterJarvis(html) {
    console.log('🧠 Injection JARVIS en cours...');
    
    // 🏷️ CHANGER TITRE
    html = html.replace(/<title>.*?<\/title>/i, '<title>🧠 JARVIS - Interface Cognitive</title>');
    
    // 🧠 CRÉER SCRIPT D'INJECTION
    const scriptTag = `
    <script>
    ${scriptJarvis}
    </script>`;
    
    // 📍 INJECTER AVANT </body>
    if (html.includes('</body>')) {
        html = html.replace('</body>', scriptTag + '</body>');
    } else {
        // Si pas de </body>, ajouter à la fin
        html += scriptTag;
    }
    
    console.log('✅ JARVIS injecté avec succès');
    return html;
}

// 🚀 DÉMARRER SERVEUR
const PORT = 8085;
server.listen(PORT, () => {
    console.log(`✅ INJECTEUR JARVIS ACTIF sur http://127.0.0.1:${PORT}`);
    console.log('🔗 Redirection vers llama.cpp:8080');
    console.log('🧠 JARVIS sera injecté automatiquement');
});

// 🛡️ GESTION ERREURS
server.on('error', (error) => {
    console.error('❌ Erreur serveur:', error.message);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt INJECTEUR JARVIS');
    server.close();
    process.exit(0);
});
