#!/usr/bin/env node

// 🔥 RENFORCEMENT CONNEXIONS MÉMOIRE - AGENTS DEEPSEEK R1 8B
// Jean-Luc - Connexion renforcée pour accès conversations précédentes

const fs = require('fs');

class RenforcementConnexionsMemoire {
    constructor() {
        this.nom = "🔥 RENFORCEMENT CONNEXIONS MÉMOIRE";
        this.memoryFile = './thermal_memory_persistent.json';
        this.conversationsFile = './conversations_permanentes.json';
        this.connexionsActives = false;
        
        console.log(`🚀 ${this.nom} - INITIALISATION`);
        console.log(`🔗 Renforcement connexions pour accès conversations précédentes`);
        console.log(`🧠 Intégration mémoire thermique ↔ conversations`);
        
        this.renforcerConnexions();
    }
    
    renforcerConnexions() {
        console.log(`🔗 RENFORCEMENT DES CONNEXIONS EN COURS...`);
        
        // 🔄 Intégration continue - toutes les 3 secondes
        setInterval(() => {
            this.integrerConversationsDansMemoire();
        }, 3000);
        
        // 🧠 Injection contexte dans agents - toutes les 5 secondes
        setInterval(() => {
            this.injecterContexteDansAgents();
        }, 5000);
        
        // 🔗 Renforcement liens mémoire - toutes les 10 secondes
        setInterval(() => {
            this.renforcerLiensMemoire();
        }, 10000);
        
        // 📊 Vérification connexions - toutes les 15 secondes
        setInterval(() => {
            this.verifierConnexions();
        }, 15000);
        
        this.connexionsActives = true;
        console.log(`✅ Connexions renforcées - Surveillance continue active`);
    }
    
    integrerConversationsDansMemoire() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) return;
            
            // 🔥 INTÉGRATION FORCÉE dans neural_system
            if (!memory.conversations_integrees) {
                memory.conversations_integrees = [];
            }
            
            // 📚 Mise à jour avec TOUTES les conversations
            memory.conversations_integrees = conversations;
            
            // 🧠 Injection dans zones thermiques
            this.injecterDansZonesThermiques(memory, conversations);
            
            // 💾 Sauvegarde immédiate
            this.ecrireMemoire(memory);
            
            console.log(`🔗 INTÉGRATION: ${conversations.length} conversations dans mémoire thermique`);
            
        } catch (error) {
            console.error(`❌ Erreur intégration:`, error.message);
        }
    }
    
    injecterDansZonesThermiques(memory, conversations) {
        // 🧠 Injection dans zone_episodic (mémoire épisodique)
        if (!memory.thermal_zones.zone2_episodic.entries) {
            memory.thermal_zones.zone2_episodic.entries = [];
        }
        
        // 📚 Ajouter conversations récentes dans zone épisodique
        const conversationsRecentes = conversations.slice(-5); // 5 dernières
        
        conversationsRecentes.forEach(conv => {
            const entreeMemoire = {
                id: `conv_${conv.id}`,
                content: `CONVERSATION: ${conv.contenu_entree} → ${conv.contenu_sortie}`,
                timestamp: conv.timestamp,
                importance: 0.9,
                type: "conversation_memory",
                temperature: 37.5,
                source: conv.source,
                qi_niveau: conv.qi_niveau || 0
            };
            
            // Éviter doublons
            const existe = memory.thermal_zones.zone2_episodic.entries.find(e => e.id === entreeMemoire.id);
            if (!existe) {
                memory.thermal_zones.zone2_episodic.entries.push(entreeMemoire);
            }
        });
        
        // 🧠 Injection dans zone_semantic (mémoire sémantique)
        if (!memory.thermal_zones.zone4_semantic.entries) {
            memory.thermal_zones.zone4_semantic.entries = [];
        }
        
        // 📊 Extraire concepts clés des conversations
        const conceptsCles = this.extraireConceptsCles(conversations);
        conceptsCles.forEach(concept => {
            const entreeConcept = {
                id: `concept_${concept.mot}_${Date.now()}`,
                content: `CONCEPT: ${concept.mot} (fréquence: ${concept.frequence})`,
                timestamp: Date.now(),
                importance: Math.min(concept.frequence / 10, 1.0),
                type: "semantic_concept",
                temperature: 37.0
            };
            
            memory.thermal_zones.zone4_semantic.entries.push(entreeConcept);
        });
        
        // 🗂️ Limitation taille zones
        if (memory.thermal_zones.zone2_episodic.entries.length > 20) {
            memory.thermal_zones.zone2_episodic.entries = memory.thermal_zones.zone2_episodic.entries.slice(-20);
        }
        
        if (memory.thermal_zones.zone4_semantic.entries.length > 50) {
            memory.thermal_zones.zone4_semantic.entries = memory.thermal_zones.zone4_semantic.entries.slice(-50);
        }
    }
    
    injecterContexteDansAgents() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) return;
            
            // 🧠 Création contexte enrichi pour agents
            const contexteEnrichi = this.creerContexteEnrichi(memory, conversations);
            
            // 💾 Injection dans mémoire pour accès agents
            memory.contexte_agent = contexteEnrichi;
            memory.derniere_injection_contexte = Date.now();
            
            this.ecrireMemoire(memory);
            
            console.log(`🧠 CONTEXTE INJECTÉ: ${contexteEnrichi.conversations_disponibles} conversations accessibles`);
            
        } catch (error) {
            console.error(`❌ Erreur injection contexte:`, error.message);
        }
    }
    
    creerContexteEnrichi(memory, conversations) {
        const maintenant = Date.now();
        
        // 📚 Historique complet accessible
        const historiqueComplet = conversations.map(conv => ({
            id: conv.id,
            timestamp: conv.timestamp,
            age_minutes: Math.floor((maintenant - conv.timestamp) / 60000),
            entree: conv.contenu_entree,
            sortie: conv.contenu_sortie,
            qi_niveau: conv.qi_niveau || 0,
            type: conv.type,
            importance: this.calculerImportanceConversation(conv, maintenant)
        }));
        
        // 🎯 Conversations prioritaires (récentes + importantes)
        const conversationsPrioritaires = historiqueComplet
            .filter(conv => conv.importance > 0.5)
            .sort((a, b) => b.importance - a.importance)
            .slice(0, 10);
        
        // 📊 Statistiques évolution
        const evolutionQI = this.calculerEvolutionQI(conversations);
        const evolutionNeurones = this.calculerEvolutionNeurones(conversations);
        
        return {
            conversations_disponibles: conversations.length,
            historique_complet: historiqueComplet,
            conversations_prioritaires: conversationsPrioritaires,
            evolution_qi: evolutionQI,
            evolution_neurones: evolutionNeurones,
            qi_actuel: memory.neural_system?.qi_level || 0,
            neurones_actifs: memory.neural_system?.active_neurons || 0,
            temperature_actuelle: memory.neural_system?.temperature || 37.0,
            derniere_mise_a_jour: maintenant,
            instructions_agent: [
                "Tu as accès à TOUT l'historique des conversations",
                "Utilise contexte_agent.historique_complet pour te souvenir",
                "Référence les conversations précédentes dans tes réponses",
                "Montre l'évolution de ton QI et de tes capacités",
                "Maintiens la continuité entre les sessions"
            ]
        };
    }
    
    renforcerLiensMemoire() {
        try {
            const memory = this.lireMemoire();
            if (!memory) return;
            
            // 🔗 Création liens entre conversations et mémoire thermique
            if (!memory.liens_memoire) {
                memory.liens_memoire = {};
            }
            
            // 🧠 Liens QI ↔ Conversations
            const conversationsAvecQI = (memory.conversations_integrees || [])
                .filter(conv => conv.qi_niveau > 0);
            
            if (conversationsAvecQI.length > 0) {
                memory.liens_memoire.qi_evolution = {
                    conversations_liees: conversationsAvecQI.length,
                    qi_min: Math.min(...conversationsAvecQI.map(c => c.qi_niveau)),
                    qi_max: Math.max(...conversationsAvecQI.map(c => c.qi_niveau)),
                    progression_totale: Math.max(...conversationsAvecQI.map(c => c.qi_niveau)) - 
                                      Math.min(...conversationsAvecQI.map(c => c.qi_niveau))
                };
            }
            
            // 🔗 Liens Neurones ↔ Conversations
            const conversationsAvecNeurones = (memory.conversations_integrees || [])
                .filter(conv => conv.neurones_actifs > 0);
            
            if (conversationsAvecNeurones.length > 0) {
                memory.liens_memoire.neurones_evolution = {
                    conversations_liees: conversationsAvecNeurones.length,
                    neurones_min: Math.min(...conversationsAvecNeurones.map(c => c.neurones_actifs)),
                    neurones_max: Math.max(...conversationsAvecNeurones.map(c => c.neurones_actifs)),
                    croissance_totale: Math.max(...conversationsAvecNeurones.map(c => c.neurones_actifs)) - 
                                     Math.min(...conversationsAvecNeurones.map(c => c.neurones_actifs))
                };
            }
            
            // 💾 Sauvegarde liens
            this.ecrireMemoire(memory);
            
            console.log(`🔗 LIENS RENFORCÉS: QI et neurones liés aux conversations`);
            
        } catch (error) {
            console.error(`❌ Erreur renforcement liens:`, error.message);
        }
    }
    
    verifierConnexions() {
        try {
            const memory = this.lireMemoire();
            const conversations = this.lireConversations();
            
            if (!memory || !conversations) {
                console.log(`⚠️ CONNEXIONS: Fichiers manquants`);
                return;
            }
            
            const conversationsIntegrees = memory.conversations_integrees?.length || 0;
            const contexteAgent = memory.contexte_agent ? '✅' : '❌';
            const liensMemoire = memory.liens_memoire ? '✅' : '❌';
            const zonesThermiques = memory.thermal_zones?.zone2_episodic?.entries?.length || 0;
            
            console.log(`📊 VÉRIFICATION CONNEXIONS:`);
            console.log(`   📚 Conversations totales: ${conversations.length}`);
            console.log(`   🔗 Conversations intégrées: ${conversationsIntegrees}`);
            console.log(`   🧠 Contexte agent: ${contexteAgent}`);
            console.log(`   🔗 Liens mémoire: ${liensMemoire}`);
            console.log(`   🌡️ Zones thermiques: ${zonesThermiques} entrées`);
            
            // 🚨 Alertes si problèmes
            if (conversationsIntegrees < conversations.length) {
                console.log(`⚠️ ALERTE: ${conversations.length - conversationsIntegrees} conversations non intégrées`);
            }
            
            if (!memory.contexte_agent) {
                console.log(`⚠️ ALERTE: Contexte agent manquant`);
            }
            
        } catch (error) {
            console.error(`❌ Erreur vérification:`, error.message);
        }
    }
    
    // 🔧 FONCTIONS UTILITAIRES
    
    calculerImportanceConversation(conv, maintenant) {
        let importance = 0.5; // Base
        
        // 📅 Récence (plus récent = plus important)
        const ageHeures = (maintenant - conv.timestamp) / (1000 * 60 * 60);
        if (ageHeures < 1) importance += 0.4;
        else if (ageHeures < 24) importance += 0.2;
        
        // 📏 Longueur contenu
        const longueurTotale = (conv.contenu_entree?.length || 0) + (conv.contenu_sortie?.length || 0);
        if (longueurTotale > 100) importance += 0.2;
        
        // 🧠 QI mentionné
        if (conv.qi_niveau > 0) importance += 0.3;
        
        return Math.min(importance, 1.0);
    }
    
    calculerEvolutionQI(conversations) {
        const conversationsAvecQI = conversations
            .filter(conv => conv.qi_niveau > 0)
            .sort((a, b) => a.timestamp - b.timestamp);
        
        if (conversationsAvecQI.length < 2) return { evolution: 0, points: [] };
        
        const points = conversationsAvecQI.map(conv => ({
            timestamp: conv.timestamp,
            qi: conv.qi_niveau
        }));
        
        const evolution = points[points.length - 1].qi - points[0].qi;
        
        return { evolution, points };
    }
    
    calculerEvolutionNeurones(conversations) {
        const conversationsAvecNeurones = conversations
            .filter(conv => conv.neurones_actifs > 0)
            .sort((a, b) => a.timestamp - b.timestamp);
        
        if (conversationsAvecNeurones.length < 2) return { evolution: 0, points: [] };
        
        const points = conversationsAvecNeurones.map(conv => ({
            timestamp: conv.timestamp,
            neurones: conv.neurones_actifs
        }));
        
        const evolution = points[points.length - 1].neurones - points[0].neurones;
        
        return { evolution, points };
    }
    
    extraireConceptsCles(conversations) {
        const mots = {};
        
        conversations.forEach(conv => {
            const texte = `${conv.contenu_entree || ''} ${conv.contenu_sortie || ''}`;
            const motsTexte = texte.toLowerCase().match(/\b\w{4,}\b/g) || [];
            
            motsTexte.forEach(mot => {
                if (!this.estMotVide(mot)) {
                    mots[mot] = (mots[mot] || 0) + 1;
                }
            });
        });
        
        return Object.entries(mots)
            .map(([mot, frequence]) => ({ mot, frequence }))
            .filter(concept => concept.frequence > 1)
            .sort((a, b) => b.frequence - a.frequence)
            .slice(0, 20);
    }
    
    estMotVide(mot) {
        const motsVides = [
            'dans', 'avec', 'pour', 'sans', 'sous', 'vers', 'chez', 'entre',
            'depuis', 'pendant', 'avant', 'après', 'contre', 'selon',
            'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were',
            'will', 'would', 'could', 'should', 'might', 'must'
        ];
        
        return motsVides.includes(mot.toLowerCase());
    }
    
    // 💾 GESTION FICHIERS
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }
    
    ecrireMemoire(memory) {
        try {
            fs.writeFileSync(this.memoryFile, JSON.stringify(memory, null, 2));
            return true;
        } catch (error) {
            console.error(`❌ Erreur écriture mémoire:`, error.message);
            return false;
        }
    }
    
    lireConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error(`❌ Erreur lecture conversations:`, error.message);
            return [];
        }
    }
}

// 🚀 DÉMARRAGE RENFORCEMENT
const renforcement = new RenforcementConnexionsMemoire();

console.log(`\n🔥 RENFORCEMENT CONNEXIONS MÉMOIRE ACTIF !`);
console.log(`🔗 Connexions renforcées pour accès conversations précédentes`);
console.log(`🧠 Les agents peuvent maintenant se souvenir !`);
