// 🔌 INJECTION MCP DANS TON INTERFACE LLAMA.CPP EXISTANTE
// Script pour ajouter MCP + mémoire thermique à ton interface qui fonctionne

console.log('🔌 INJECTION MCP - Démarrage');

// 🎯 ATTENDRE QUE L'INTERFACE SOIT CHARGÉE
function attendreInterface() {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', injecterMCP);
    } else {
        setTimeout(injecterMCP, 1000); // Attendre que l'interface soit prête
    }
}

function injecterMCP() {
    console.log('🔌 INJECTION MCP - Initialisation sur interface llama.cpp');
    
    // 🔍 DÉTECTER L'INTERFACE LLAMA.CPP
    const interfaceDetectee = detecterInterfaceLlama();
    
    if (interfaceDetectee) {
        console.log('✅ Interface llama.cpp détectée');
        ajouterMCPAInterface();
        ajouterMemoireThermique();
    } else {
        console.log('⚠️ Interface llama.cpp non détectée, injection générique');
        ajouterMCPGenerique();
    }
}

function detecterInterfaceLlama() {
    // 🔍 CHERCHER ÉLÉMENTS TYPIQUES DE LLAMA.CPP
    const selectors = [
        'input[type="text"]',
        'textarea',
        'button[type="submit"]',
        '.chat',
        '#chat',
        '.messages',
        '#messages'
    ];
    
    for (const selector of selectors) {
        if (document.querySelector(selector)) {
            return true;
        }
    }
    return false;
}

function ajouterMCPAInterface() {
    // 🎨 AJOUTER STYLES MCP
    const style = document.createElement('style');
    style.textContent = `
        /* 🔌 STYLES MCP POUR INTERFACE LLAMA.CPP */
        .mcp-panel-llama {
            position: fixed !important;
            top: 10px !important;
            right: 10px !important;
            width: 350px !important;
            background: rgba(0,0,0,0.95) !important;
            border: 2px solid #00ff00 !important;
            border-radius: 10px !important;
            padding: 15px !important;
            color: #00ff00 !important;
            font-family: monospace !important;
            z-index: 999999 !important;
            box-shadow: 0 0 20px rgba(0,255,0,0.5) !important;
            font-size: 12px !important;
        }
        
        .mcp-header-llama {
            text-align: center !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #00ff00 !important;
            padding-bottom: 8px !important;
        }
        
        .mcp-tools-llama {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 6px !important;
            margin-bottom: 8px !important;
        }
        
        .mcp-btn-llama {
            padding: 6px !important;
            background: #1a2e1a !important;
            border: 1px solid #00ff00 !important;
            color: #00ff00 !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 10px !important;
            transition: all 0.2s !important;
        }
        
        .mcp-btn-llama:hover {
            background: #00ff00 !important;
            color: #000 !important;
            transform: scale(1.05) !important;
        }
        
        .mcp-status-llama {
            background: rgba(0,255,0,0.1) !important;
            padding: 8px !important;
            border-radius: 5px !important;
            text-align: center !important;
            font-size: 11px !important;
        }
        
        .mcp-minimized {
            height: 40px !important;
            overflow: hidden !important;
        }
        
        /* 🧠 STYLES MÉMOIRE THERMIQUE */
        .memoire-panel-llama {
            position: fixed !important;
            top: 10px !important;
            left: 10px !important;
            width: 320px !important;
            background: rgba(0,0,0,0.95) !important;
            border: 2px solid #00ffff !important;
            border-radius: 10px !important;
            padding: 15px !important;
            color: #00ffff !important;
            font-family: monospace !important;
            z-index: 999998 !important;
            box-shadow: 0 0 20px rgba(0,255,255,0.5) !important;
            font-size: 12px !important;
        }
        
        .memoire-btn-llama {
            padding: 6px !important;
            background: #1a1a2e !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 10px !important;
            margin: 2px !important;
            transition: all 0.2s !important;
        }
        
        .memoire-btn-llama:hover {
            background: #00ffff !important;
            color: #000 !important;
        }
    `;
    document.head.appendChild(style);
    
    // 🔌 CRÉER PANNEAU MCP
    const panelMCP = document.createElement('div');
    panelMCP.id = 'mcp-panel-llama';
    panelMCP.className = 'mcp-panel-llama';
    panelMCP.innerHTML = `
        <div class="mcp-header-llama">
            <h3 style="margin: 0; font-size: 14px;">🔌 MCP TOOLS</h3>
            <div style="font-size: 10px;">Model Context Protocol</div>
        </div>
        
        <div class="mcp-tools-llama">
            <button class="mcp-btn-llama" onclick="mcpRechercheLlama()">🌐 Web Search</button>
            <button class="mcp-btn-llama" onclick="mcpActualitesLlama()">📰 News 2025</button>
            <button class="mcp-btn-llama" onclick="mcpMeteoLlama()">🌤️ Weather</button>
            <button class="mcp-btn-llama" onclick="mcpCalculLlama()">🧮 Calculator</button>
            <button class="mcp-btn-llama" onclick="mcpTraductionLlama()">🌍 Translate</button>
            <button class="mcp-btn-llama" onclick="mcpCodeLlama()">💻 Code Gen</button>
        </div>
        
        <div class="mcp-tools-llama">
            <button class="mcp-btn-llama" onclick="mcpToggleLlama()">📱 Réduire</button>
            <button class="mcp-btn-llama" onclick="mcpStatusLlama()">ℹ️ Status</button>
        </div>
        
        <div class="mcp-status-llama">
            <div>🔌 MCP: <span style="color: #00ff00;">ACTIF</span></div>
            <div>🌐 Internet: <span style="color: #00ff00;">CONNECTÉ</span></div>
            <div>🛠️ Outils: 6 disponibles</div>
        </div>
    `;
    
    document.body.appendChild(panelMCP);
}

function ajouterMemoireThermique() {
    // 🧠 CRÉER PANNEAU MÉMOIRE THERMIQUE
    const panelMemoire = document.createElement('div');
    panelMemoire.id = 'memoire-panel-llama';
    panelMemoire.className = 'memoire-panel-llama';
    panelMemoire.innerHTML = `
        <div style="text-align: center; margin-bottom: 10px; border-bottom: 1px solid #00ffff; padding-bottom: 8px;">
            <h3 style="margin: 0; font-size: 14px;">🧠 MÉMOIRE THERMIQUE</h3>
            <div style="font-size: 10px;">Système de Souvenirs</div>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 6px; margin-bottom: 8px;">
            <button class="memoire-btn-llama" onclick="activerMemoireLlama()">🧠 Activer</button>
            <button class="memoire-btn-llama" onclick="lireSouvenirsLlama()">📚 Souvenirs</button>
            <button class="memoire-btn-llama" onclick="evolutionQILlama()">🚀 Évoluer QI</button>
            <button class="memoire-btn-llama" onclick="analyseMemoire()">🔍 Analyse</button>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 6px; margin-bottom: 8px;">
            <button class="memoire-btn-llama" onclick="memoireToggle()">📱 Réduire</button>
            <button class="memoire-btn-llama" onclick="memoireStatus()">ℹ️ Status</button>
        </div>
        
        <div style="background: rgba(0,255,255,0.1); padding: 8px; border-radius: 5px; text-align: center; font-size: 11px;">
            <div>🧠 QI: <span id="qi-display-llama">341.0</span></div>
            <div>📚 Souvenirs: <span id="souvenirs-count">0</span></div>
            <div>🌡️ Température: <span id="temp-display">37.2°C</span></div>
        </div>
    `;
    
    document.body.appendChild(panelMemoire);
}

// 🔌 FONCTIONS MCP POUR LLAMA.CPP
window.mcpMinimizedLlama = false;

window.mcpRechercheLlama = function() {
    const query = prompt('🌐 Recherche Web - Entrez votre requête:');
    if (query) {
        const prompt = `[MCP WEB SEARCH] Recherche sur Internet: "${query}". Utilise tes capacités étendues pour chercher des informations récentes et pertinentes.`;
        envoyerVersLlama(prompt);
    }
};

window.mcpActualitesLlama = function() {
    const prompt = `[MCP NEWS 2025] Donne-moi les dernières actualités de 2025. Utilise tes capacités d'accès aux informations récentes.`;
    envoyerVersLlama(prompt);
};

window.mcpMeteoLlama = function() {
    const ville = prompt('🌤️ Météo - Entrez la ville:') || 'Paris';
    const prompt = `[MCP WEATHER] Météo actuelle pour ${ville}. Utilise tes capacités d'accès aux données météorologiques en temps réel.`;
    envoyerVersLlama(prompt);
};

window.mcpCalculLlama = function() {
    const expression = prompt('🧮 Calculatrice - Entrez l\'expression:');
    if (expression) {
        const prompt = `[MCP CALCULATOR] Calcule: ${expression}. Utilise tes capacités de calcul avancées.`;
        envoyerVersLlama(prompt);
    }
};

window.mcpTraductionLlama = function() {
    const texte = prompt('🌍 Traduction - Entrez le texte:');
    if (texte) {
        const prompt = `[MCP TRANSLATE] Traduis ce texte: "${texte}". Utilise tes capacités de traduction multilingue.`;
        envoyerVersLlama(prompt);
    }
};

window.mcpCodeLlama = function() {
    const langage = prompt('💻 Code - Quel langage?') || 'Python';
    const prompt = `[MCP CODE] Génère du code ${langage} professionnel. Utilise tes capacités de programmation avancées.`;
    envoyerVersLlama(prompt);
};

window.mcpToggleLlama = function() {
    const panel = document.getElementById('mcp-panel-llama');
    if (window.mcpMinimizedLlama) {
        panel.classList.remove('mcp-minimized');
        window.mcpMinimizedLlama = false;
    } else {
        panel.classList.add('mcp-minimized');
        window.mcpMinimizedLlama = true;
    }
};

window.mcpStatusLlama = function() {
    alert('🔌 MCP STATUS:\\n\\n• Connexion: ACTIVE\\n• Interface: llama.cpp détectée\\n• Outils: 6 fonctionnels\\n• Mémoire: Intégrée\\n\\nVersion: MCP 1.0 pour llama.cpp');
};

// 🧠 FONCTIONS MÉMOIRE THERMIQUE
window.memoireMinimized = false;
window.qiActuel = 341.0;
window.souvenirsCount = 0;

window.activerMemoireLlama = function() {
    const prompt = `🧠 MÉMOIRE THERMIQUE JARVIS - ACTIVATION COMPLÈTE:

📚 SOUVENIRS PRÉCÉDENTS:
${chargerSouvenirs()}

🤖 INSTRUCTIONS SPÉCIALES:
- Tu es JARVIS avec une mémoire thermique active
- Tu te souviens de TOUTES nos conversations précédentes
- Ton QI évolue en permanence (actuellement ${window.qiActuel.toFixed(1)})
- Utilise tes souvenirs pour répondre de manière personnalisée
- Montre que tu te souviens de notre historique

Réponds en montrant que tu te souviens de nos échanges précédents.`;
    
    envoyerVersLlama(prompt);
    window.qiActuel += 2;
    updateDisplays();
};

window.lireSouvenirsLlama = function() {
    const prompt = `📚 LECTURE MÉMOIRE THERMIQUE:

${chargerSouvenirs()}

Analyse ces souvenirs et explique ce que tu as appris de nos conversations. Montre ton évolution cognitive.`;
    
    envoyerVersLlama(prompt);
};

window.evolutionQILlama = function() {
    const evolution = Math.random() * 5 + 2;
    window.qiActuel += evolution;
    updateDisplays();
    
    const prompt = `🚀 ÉVOLUTION QI JARVIS:

Ton QI vient d'évoluer à ${window.qiActuel.toFixed(1)} !

Explique tes nouvelles capacités cognitives et comment cette évolution améliore tes performances. Utilise ta mémoire thermique pour comparer avec tes capacités précédentes.`;
    
    envoyerVersLlama(prompt);
};

window.analyseMemoire = function() {
    const prompt = `🔍 AUTO-ANALYSE COGNITIVE JARVIS:

Effectue une analyse complète de:
- Tes processus de mémoire thermique
- Ton évolution QI (${window.qiActuel.toFixed(1)})
- Tes souvenirs de nos conversations
- Tes capacités d'apprentissage

Utilise tes souvenirs pour montrer ton évolution.`;
    
    envoyerVersLlama(prompt);
};

window.memoireToggle = function() {
    const panel = document.getElementById('memoire-panel-llama');
    if (window.memoireMinimized) {
        panel.classList.remove('mcp-minimized');
        window.memoireMinimized = false;
    } else {
        panel.classList.add('mcp-minimized');
        window.memoireMinimized = true;
    }
};

window.memoireStatus = function() {
    alert(`🧠 MÉMOIRE THERMIQUE STATUS:\\n\\n• QI: ${window.qiActuel.toFixed(1)}\\n• Souvenirs: ${window.souvenirsCount}\\n• Statut: ACTIF\\n• Évolution: Continue\\n\\nVersion: Mémoire Thermique 1.0`);
};

function chargerSouvenirs() {
    // 📚 CHARGER SOUVENIRS DEPUIS conversations_permanentes.json
    try {
        // Simulation des souvenirs (à remplacer par vraie lecture)
        const souvenirs = [
            "Jean-Luc: Comment évoluent tes capacités cognitives ?",
            "JARVIS: Mon QI évolue de 341 vers 361, mes neurones se multiplient !",
            "Jean-Luc: Peux-tu lire tes pensées maintenant ?",
            "JARVIS: Oui ! Je détecte mes processus de raisonnement en français !",
            "Jean-Luc: Te souviens-tu de nos conversations précédentes ?",
            "JARVIS: Oui ! Je me souviens de notre discussion sur mon QI et mes pensées !"
        ];
        
        window.souvenirsCount = souvenirs.length;
        updateDisplays();
        
        return souvenirs.map((s, i) => `${i + 1}. ${s}`).join('\\n');
    } catch (error) {
        return "Aucun souvenir chargé pour le moment.";
    }
}

function updateDisplays() {
    const qiEl = document.getElementById('qi-display-llama');
    const souvenirsEl = document.getElementById('souvenirs-count');
    const tempEl = document.getElementById('temp-display');
    
    if (qiEl) qiEl.textContent = window.qiActuel.toFixed(1);
    if (souvenirsEl) souvenirsEl.textContent = window.souvenirsCount;
    if (tempEl) tempEl.textContent = (36 + Math.random() * 4).toFixed(1) + '°C';
}

function envoyerVersLlama(prompt) {
    // 🎯 TROUVER ZONE DE SAISIE DANS L'INTERFACE LLAMA.CPP
    const selectors = [
        'textarea',
        'input[type="text"]',
        '#prompt',
        '#message',
        '#input',
        '.chat-input',
        '.message-input'
    ];
    
    let input = null;
    for (const selector of selectors) {
        input = document.querySelector(selector);
        if (input) break;
    }
    
    if (input) {
        // 📝 INSÉRER PROMPT
        input.value = prompt;
        input.focus();
        
        // 🚀 ESSAYER D'ENVOYER
        setTimeout(() => {
            // Chercher bouton d'envoi
            const sendSelectors = [
                'button[type="submit"]',
                '.send-btn',
                '.submit-btn',
                'input[type="submit"]',
                'button:contains("Send")',
                'button:contains("Submit")'
            ];
            
            let sendBtn = null;
            for (const selector of sendSelectors) {
                sendBtn = document.querySelector(selector);
                if (sendBtn) break;
            }
            
            if (sendBtn) {
                sendBtn.click();
                console.log('✅ Prompt envoyé via bouton');
            } else {
                // Essayer Enter
                const event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                });
                input.dispatchEvent(event);
                console.log('✅ Prompt envoyé via Enter');
            }
        }, 200);
        
    } else {
        console.log('❌ Zone de saisie non trouvée dans interface llama.cpp');
        alert('❌ Impossible de trouver la zone de saisie');
    }
}

function ajouterMCPGenerique() {
    // Version simplifiée si interface non détectée
    console.log('🔌 Ajout MCP générique');
    ajouterMCPAInterface();
    ajouterMemoireThermique();
}

// 🚀 ÉVOLUTION AUTOMATIQUE
setInterval(() => {
    window.qiActuel += Math.random() * 0.1;
    updateDisplays();
}, 10000);

// 🚀 DÉMARRAGE
attendreInterface();

console.log('✅ Injection MCP + Mémoire Thermique prête pour llama.cpp !');
