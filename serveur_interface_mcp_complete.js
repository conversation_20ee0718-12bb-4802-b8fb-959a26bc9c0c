// 🔌 SERVEUR POUR INTERFACE LLAMA.CPP AVEC MCP + MÉMOIRE THERMIQUE
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔌 SERVEUR INTERFACE MCP COMPLET - DÉMARRAGE');

const server = http.createServer((req, res) => {
    console.log(`📡 Requête: ${req.method} ${req.url}`);
    
    // 🏠 SERVIR INTERFACE MODIFIÉE
    if (req.method === 'GET' && req.url === '/') {
        try {
            const html = fs.readFileSync('./interface_8080_reelle.html', 'utf8');
            
            res.writeHead(200, {
                'Content-Type': 'text/html; charset=utf-8',
                'Cache-Control': 'no-cache',
                'Access-Control-Allow-Origin': '*'
            });
            res.end(html);
            
            console.log('✅ Interface llama.cpp + MCP + Mé<PERSON>ire servie');
            
        } catch (error) {
            console.error('❌ Erreur lecture interface:', error.message);
            res.writeHead(500);
            res.end('Erreur serveur');
        }
    }
    
    // 🔗 PROXY VERS LLAMA.CPP ORIGINAL
    else if (req.url.startsWith('/completion') || req.url.startsWith('/v1/') || req.url.startsWith('/health')) {
        const options = {
            hostname: '127.0.0.1',
            port: 8080,
            path: req.url,
            method: req.method,
            headers: { ...req.headers }
        };

        delete options.headers.host;

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, {
                ...proxyRes.headers,
                'Access-Control-Allow-Origin': '*'
            });
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (error) => {
            console.error('❌ Erreur proxy llama.cpp:', error.message);
            res.writeHead(500);
            res.end('Erreur connexion llama.cpp');
        });

        req.pipe(proxyReq);
    }
    
    // 🌐 CORS PREFLIGHT
    else if (req.method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        });
        res.end();
    }
    
    // 🚫 404
    else {
        res.writeHead(404);
        res.end('Page non trouvée');
    }
});

// 🚀 DÉMARRAGE SERVEUR
const PORT = 8085;
server.listen(PORT, () => {
    console.log(`✅ SERVEUR INTERFACE MCP COMPLET ACTIF sur http://127.0.0.1:${PORT}`);
    console.log('🔌 Interface llama.cpp + MCP + Mémoire Thermique');
    console.log('🌐 Fonctionnalités disponibles:');
    console.log('   • Interface llama.cpp originale');
    console.log('   • 🔌 MCP Tools (Web Search, News, Weather, etc.)');
    console.log('   • 🧠 Mémoire Thermique avec souvenirs');
    console.log('   • 🚀 QI évolutif');
    console.log('🔗 Proxy llama.cpp: http://127.0.0.1:8080');
});

// 🛡️ GESTION ERREURS
server.on('error', (error) => {
    console.error('❌ Erreur serveur:', error.message);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt SERVEUR INTERFACE MCP COMPLET');
    server.close();
    process.exit(0);
});
