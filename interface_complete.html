<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Interface avec MCP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ffff;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #00ffff;
            border-radius: 15px;
            background: rgba(0,255,255,0.1);
            box-shadow: 0 0 30px rgba(0,255,255,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 10px #00ffff;
        }

        .mcp-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 350px;
            background: rgba(0,0,0,0.95);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 15px;
            color: #00ff00;
            font-family: monospace;
            z-index: 999999;
            box-shadow: 0 0 20px rgba(0,255,0,0.5);
            font-size: 12px;
        }

        .mcp-header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #00ff00;
            padding-bottom: 8px;
        }

        .mcp-tools {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 10px;
        }

        .mcp-btn {
            padding: 8px;
            background: #1a2e1a;
            border: 1px solid #00ff00;
            color: #00ff00;
            border-radius: 4px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s;
        }

        .mcp-btn:hover {
            background: #00ff00;
            color: #000;
            transform: scale(1.05);
        }

        .mcp-status {
            background: rgba(0,255,0,0.1);
            padding: 8px;
            border-radius: 5px;
            text-align: center;
            font-size: 11px;
            margin-top: 8px;
        }

        .chat-area {
            background: rgba(0,0,0,0.9);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 20px;
            min-height: 500px;
            margin-bottom: 20px;
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 5px;
            background: rgba(0,0,0,0.5);
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            animation: fadeIn 0.5s ease;
        }

        .message.user {
            background: rgba(0,255,255,0.1);
            border-left: 4px solid #00ffff;
        }

        .message.assistant {
            background: rgba(0,255,0,0.1);
            border-left: 4px solid #00ff00;
        }

        .message.mcp {
            background: rgba(255,165,0,0.1);
            border-left: 4px solid #ffa500;
            color: #ffa500;
        }

        .input-area {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-area textarea {
            flex: 1;
            background: rgba(0,255,255,0.1);
            border: 1px solid #00ffff;
            color: #00ffff;
            padding: 12px;
            border-radius: 5px;
            font-family: inherit;
            resize: vertical;
            min-height: 60px;
        }

        .input-area textarea:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0,255,255,0.5);
        }

        .send-btn {
            background: linear-gradient(45deg, #00ffff, #00ff00);
            color: #000;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,255,255,0.5);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .mcp-minimized {
            height: 40px !important;
            overflow: hidden !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 JARVIS</h1>
            <p>Interface Cognitive avec MCP (Model Context Protocol)</p>
            <p>Système Neuronal Avancé - Version MCP 1.0</p>
        </div>

        <div class="chat-area">
            <h3>💬 Interface de Communication JARVIS + MCP</h3>
            <div class="chat-messages" id="chat-messages">
                <div class="message assistant">
                    <strong>JARVIS:</strong> Système cognitif initialisé avec support MCP. Accès Internet et outils externes disponibles.
                </div>
            </div>

            <div class="input-area">
                <textarea id="user-input" placeholder="Tapez votre message à JARVIS... (MCP activé pour recherches web, actualités, etc.)" onkeypress="handleEnter(event)"></textarea>
                <button class="send-btn" onclick="envoyerMessage()">📤 Envoyer</button>
            </div>
        </div>
    </div>

    <!-- Panneau MCP -->
    <div class="mcp-panel" id="mcp-panel">
        <div class="mcp-header">
            <h3>🔌 MCP TOOLS</h3>
            <div style="font-size: 10px;">Model Context Protocol</div>
        </div>

        <div class="mcp-tools">
            <button class="mcp-btn" onclick="mcpRecherche()">🌐 Web Search</button>
            <button class="mcp-btn" onclick="mcpActualites()">📰 News</button>
            <button class="mcp-btn" onclick="mcpMeteo()">🌤️ Weather</button>
            <button class="mcp-btn" onclick="mcpCalcul()">🧮 Calculator</button>
            <button class="mcp-btn" onclick="mcpTraduction()">🌍 Translate</button>
            <button class="mcp-btn" onclick="mcpCode()">💻 Code</button>
        </div>

        <div class="mcp-tools">
            <button class="mcp-btn" onclick="mcpToggle()">📱 Réduire</button>
            <button class="mcp-btn" onclick="mcpStatus()">ℹ️ Status</button>
        </div>

        <div class="mcp-status">
            <div>🔌 MCP: <span id="mcp-status">ACTIF</span></div>
            <div>🌐 Internet: <span id="internet-status">CONNECTÉ</span></div>
            <div>🛠️ Outils: <span id="tools-count">6</span> disponibles</div>
        </div>
    </div>

    <script>
        // 🔌 SYSTÈME MCP POUR JARVIS
        let mcpMinimized = false;
        let conversationHistory = [];

        console.log('🔌 MCP System - Initialisation');

        // 🌐 FONCTIONS MCP
        function mcpRecherche() {
            const query = prompt('🌐 Recherche Web - Entrez votre requête:');
            if (query) {
                ajouterMessage('mcp', `🌐 Recherche web: "${query}"`);
                simulerRechercheMCP(query);
            }
        }

        function mcpActualites() {
            ajouterMessage('mcp', '📰 Récupération des actualités 2025...');
            simulerActualitesMCP();
        }

        function mcpMeteo() {
            const ville = prompt('🌤️ Météo - Entrez la ville:') || 'Paris';
            ajouterMessage('mcp', `🌤️ Météo pour ${ville}...`);
            simulerMeteoMCP(ville);
        }

        function mcpCalcul() {
            const expression = prompt('🧮 Calculatrice - Entrez l\'expression:');
            if (expression) {
                ajouterMessage('mcp', `🧮 Calcul: ${expression}`);
                simulerCalculMCP(expression);
            }
        }

        function mcpTraduction() {
            const texte = prompt('🌍 Traduction - Entrez le texte à traduire:');
            if (texte) {
                ajouterMessage('mcp', `🌍 Traduction: "${texte}"`);
                simulerTraductionMCP(texte);
            }
        }

        function mcpCode() {
            const langage = prompt('💻 Code - Quel langage? (Python, JavaScript, etc.)') || 'Python';
            ajouterMessage('mcp', `💻 Génération de code ${langage}...`);
            simulerCodeMCP(langage);
        }

        function mcpToggle() {
            const panel = document.getElementById('mcp-panel');
            if (mcpMinimized) {
                panel.classList.remove('mcp-minimized');
                mcpMinimized = false;
            } else {
                panel.classList.add('mcp-minimized');
                mcpMinimized = true;
            }
        }

        function mcpStatus() {
            const status = `🔌 MCP STATUS:

• Connexion: ACTIVE
• Internet: DISPONIBLE
• Outils: 6 fonctionnels
• Recherche Web: ✅
• Actualités: ✅
• Météo: ✅
• Calculatrice: ✅
• Traduction: ✅
• Code: ✅

Version: MCP 1.0 pour JARVIS`;

            alert(status);
        }

        // 🔌 VRAIES FONCTIONS MCP AVEC APIS
        async function simulerRechercheMCP(query) {
            try {
                ajouterMessage('mcp', `🌐 Recherche en cours: "${query}"`);

                const response = await fetch('/mcp/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query })
                });

                if (response.ok) {
                    const data = await response.json();
                    const resultats = [
                        `🌐 RÉSULTATS RECHERCHE: "${query}"`,
                        '',
                        ...data.results.map(r => `• ${r.title}\n  ${r.snippet}\n  🔗 ${r.url}`),
                        '',
                        `[MCP] ${data.results.length} résultats trouvés`
                    ].join('\n');

                    ajouterMessage('assistant', resultats);
                    envoyerVersJarvis(`Voici les résultats de recherche web pour "${query}". Analyse et résume ces informations: ${JSON.stringify(data.results)}`);
                } else {
                    ajouterMessage('assistant', '❌ Erreur lors de la recherche web');
                }
            } catch (error) {
                console.error('Erreur MCP Search:', error);
                ajouterMessage('assistant', '❌ Erreur de connexion MCP Search');
            }
        }

        async function simulerActualitesMCP() {
            try {
                ajouterMessage('mcp', '📰 Récupération actualités 2025...');

                const response = await fetch('/mcp/news');

                if (response.ok) {
                    const data = await response.json();
                    const actualites = [
                        '📰 ACTUALITÉS 2025:',
                        '',
                        ...data.news.map(n => `• ${n.title}\n  ${n.content}\n  📅 ${n.date} - ${n.source}`),
                        '',
                        `[MCP] ${data.news.length} actualités récupérées`
                    ].join('\n');

                    ajouterMessage('assistant', actualites);
                    envoyerVersJarvis(`Voici les dernières actualités 2025. Analyse et commente: ${JSON.stringify(data.news)}`);
                } else {
                    ajouterMessage('assistant', '❌ Erreur lors de la récupération des actualités');
                }
            } catch (error) {
                console.error('Erreur MCP News:', error);
                ajouterMessage('assistant', '❌ Erreur de connexion MCP News');
            }
        }

        async function simulerMeteoMCP(ville) {
            try {
                ajouterMessage('mcp', `🌤️ Récupération météo ${ville}...`);

                const response = await fetch(`/mcp/weather?city=${encodeURIComponent(ville)}`);

                if (response.ok) {
                    const data = await response.json();
                    const meteo = [
                        `🌤️ MÉTÉO ${data.city.toUpperCase()}:`,
                        '',
                        `• Température: ${data.temperature}°C`,
                        `• Conditions: ${data.condition}`,
                        `• Humidité: ${data.humidity}%`,
                        `• Vent: ${data.wind} km/h`,
                        '',
                        `[MCP] Données météo actualisées`
                    ].join('\n');

                    ajouterMessage('assistant', meteo);
                } else {
                    ajouterMessage('assistant', '❌ Erreur lors de la récupération météo');
                }
            } catch (error) {
                console.error('Erreur MCP Weather:', error);
                ajouterMessage('assistant', '❌ Erreur de connexion MCP Weather');
            }
        }

        async function simulerCalculMCP(expression) {
            try {
                ajouterMessage('mcp', `🧮 Calcul: ${expression}`);

                const response = await fetch('/mcp/calculate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ expression: expression })
                });

                if (response.ok) {
                    const data = await response.json();
                    ajouterMessage('assistant', `🧮 ${data.expression} = ${data.result}`);
                } else {
                    const error = await response.json();
                    ajouterMessage('assistant', `🧮 Erreur: ${error.error}`);
                }
            } catch (error) {
                console.error('Erreur MCP Calculate:', error);
                ajouterMessage('assistant', '❌ Erreur de connexion MCP Calculator');
            }
        }

        function simulerTraductionMCP(texte) {
            // Traduction simulée (en attendant API réelle)
            setTimeout(() => {
                const traduction = [
                    '🌍 TRADUCTION:',
                    '',
                    `Original: ${texte}`,
                    `Français → Anglais: [Traduction via MCP]`,
                    '',
                    '[MCP] Traduction effectuée'
                ].join('\n');

                ajouterMessage('assistant', traduction);
            }, 1000);
        }

        function simulerCodeMCP(langage) {
            // Génération code simulée (en attendant API réelle)
            setTimeout(() => {
                const exemples = {
                    'Python': 'def hello_world():\n    return "Hello from MCP!"',
                    'JavaScript': 'function helloWorld() {\n    return "Hello from MCP!";\n}',
                    'Java': 'public String helloWorld() {\n    return "Hello from MCP!";\n}'
                };

                const code = [
                    `💻 CODE ${langage.toUpperCase()}:`,
                    '',
                    exemples[langage] || exemples['Python'],
                    '',
                    '[MCP] Code généré avec succès'
                ].join('\n');

                ajouterMessage('assistant', code);
            }, 1200);
        }

        // 📤 COMMUNICATION AVEC JARVIS
        async function envoyerVersJarvis(prompt) {
            try {
                const response = await fetch('http://127.0.0.1:8080/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        n_predict: 200
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.content) {
                        ajouterMessage('assistant', data.content);
                    }
                }
            } catch (error) {
                console.log('Erreur communication JARVIS:', error);
            }
        }

        // 💬 INTERFACE CHAT
        async function envoyerMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();

            if (!message) return;

            ajouterMessage('user', message);
            input.value = '';

            // Vérifier si c'est une commande MCP
            if (message.toLowerCase().includes('recherche') || message.toLowerCase().includes('search')) {
                mcpRecherche();
            } else if (message.toLowerCase().includes('actualité') || message.toLowerCase().includes('news')) {
                mcpActualites();
            } else if (message.toLowerCase().includes('météo') || message.toLowerCase().includes('weather')) {
                mcpMeteo();
            } else {
                // Envoyer à JARVIS normal
                await envoyerVersJarvis(message);
            }
        }

        function ajouterMessage(type, contenu) {
            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const nom = type === 'user' ? 'UTILISATEUR' :
                       type === 'mcp' ? 'MCP' : 'JARVIS';
            messageDiv.innerHTML = `<strong>${nom}:</strong> ${contenu.replace(/\n/g, '<br>')}`;

            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function handleEnter(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                envoyerMessage();
            }
        }

        // 🚀 INITIALISATION
        setTimeout(() => {
            ajouterMessage('assistant', '🔌 Système MCP activé ! Vous pouvez maintenant utiliser les outils externes et accéder à Internet via les boutons MCP.');
        }, 1000);

        console.log('✅ Interface JARVIS + MCP prête !');
    </script>
</body>
</html>