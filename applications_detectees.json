{"timestamp": 1750149114601, "platform": "darwin", "total": 34, "applications": ["Claude.app", "Cursor.app", "Docker.app", "Elmedia Video Player.app", "Firefox.app", "Fleet.app", "GPT4All.app", "GarageBand.app", "GitHub Desktop.app", "Google Chrome.app", "Infuse.app", "Keynote.app", "LM Studio.app", "LOUNA-AI Complete.app", "Louna AI.app", "Ollama.app", "OmniPlayerStore.app", "Opera.app", "Python 3.14", "Safari.app", "Secure Private Browser + VPN.app", "Trae.app", "Utilities", "Visual Studio Code.app", "Wave.app", "WhatsApp.app", "WinZip.app", "Windsurf.app", "Xcode.app", "Zed.app", "gpt4all", "iCompta.app", "iZip.app", "uTorrent Web.app"]}