#!/usr/bin/env node

// 🧠 MIDDLEWARE MÉMOIRE THERMIQUE POUR LLAMA.CPP
// <PERSON><PERSON>nexion interface llama.cpp à la mémoire thermique

const http = require('http');
const fs = require('fs');
const DetecteurDeclenchementMemoire = require('./detecteur_declenchement_memoire.js');

class MiddlewareMemoireLlama {
    constructor() {
        this.nom = "🧠 MIDDLEWARE MÉMOIRE LLAMA";
        this.portLlama = 8082;
        this.portProxy = 8081;
        this.detecteurMemoire = new DetecteurDeclenchementMemoire();
        this.memoryFile = './thermal_memory_persistent.json';
        this.conversationsFile = './conversations_permanentes.json';
        
        console.log(`🚀 ${this.nom} - INITIALISATION`);
        console.log(`🔗 Proxy: localhost:${this.portProxy} → llama.cpp:${this.portLlama}`);
        
        this.demarrerProxy();
    }
    
    demarrerProxy() {
        const server = http.createServer((req, res) => {
            // 🔍 Intercepter toutes les requêtes
            if (req.method === 'POST' && req.url.includes('/completion')) {
                this.intercepterCompletion(req, res);
            } else if (req.method === 'POST' && req.url.includes('/chat')) {
                this.intercepterChat(req, res);
            } else {
                // 🔄 Rediriger autres requêtes vers llama.cpp
                this.redirigerVersLlama(req, res);
            }
        });
        
        server.listen(this.portProxy, () => {
            console.log(`✅ ${this.nom} actif sur port ${this.portProxy}`);
            console.log(`🔗 Redirection vers llama.cpp:${this.portLlama}`);
            console.log(`🧠 Mémoire thermique connectée !`);
        });
    }
    
    intercepterCompletion(req, res) {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const prompt = data.prompt || '';
                
                console.log(`📥 PROMPT INTERCEPTÉ: "${prompt}"`);
                
                // 🔍 DÉCLENCHEMENT MÉMOIRE
                const declenchement = this.detecteurMemoire.detecterDeclenchementMemoire(prompt);
                
                if (declenchement && declenchement.declenchement_detecte) {
                    console.log(`🎯 DÉCLENCHEMENT DÉTECTÉ: ${declenchement.types_detectes.join(', ')}`);
                    
                    // 🧠 ENRICHIR PROMPT AVEC MÉMOIRE
                    const promptEnrichi = this.enrichirPromptAvecMemoire(prompt, declenchement);
                    data.prompt = promptEnrichi;
                    
                    console.log(`🧠 PROMPT ENRICHI AVEC MÉMOIRE`);
                } else {
                    console.log(`❌ Aucun déclenchement détecté`);
                }
                
                // 🔄 ENVOYER À LLAMA.CPP
                this.envoyerVersLlama(data, res, 'completion');
                
            } catch (error) {
                console.error(`❌ Erreur interception:`, error.message);
                res.writeHead(500);
                res.end(JSON.stringify({error: 'Erreur middleware'}));
            }
        });
    }
    
    intercepterChat(req, res) {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const messages = data.messages || [];
                const dernierMessage = messages[messages.length - 1];
                const contenu = dernierMessage ? dernierMessage.content : '';
                
                console.log(`📥 MESSAGE CHAT INTERCEPTÉ: "${contenu}"`);
                
                // 🔍 DÉCLENCHEMENT MÉMOIRE
                const declenchement = this.detecteurMemoire.detecterDeclenchementMemoire(contenu);
                
                if (declenchement && declenchement.declenchement_detecte) {
                    console.log(`🎯 DÉCLENCHEMENT CHAT DÉTECTÉ: ${declenchement.types_detectes.join(', ')}`);
                    
                    // 🧠 AJOUTER CONTEXTE MÉMOIRE
                    const contexteMemoire = this.obtenirContexteMemoire(declenchement);
                    if (contexteMemoire) {
                        messages.unshift({
                            role: 'system',
                            content: `🧠 MÉMOIRE THERMIQUE ACTIVÉE:\n${contexteMemoire}`
                        });
                    }
                }
                
                // 🔄 ENVOYER À LLAMA.CPP
                this.envoyerVersLlama(data, res, 'chat');
                
            } catch (error) {
                console.error(`❌ Erreur interception chat:`, error.message);
                res.writeHead(500);
                res.end(JSON.stringify({error: 'Erreur middleware chat'}));
            }
        });
    }
    
    enrichirPromptAvecMemoire(prompt, declenchement) {
        // 🧠 RÉCUPÉRER MÉMOIRE THERMIQUE
        const memoire = this.lireMemoire();
        const conversations = this.lireConversations();
        
        let contexteMemoire = '';
        
        // 📚 AJOUTER CONVERSATIONS PERTINENTES
        if (conversations && conversations.length > 0) {
            const conversationsRecentes = conversations.slice(-5); // 5 dernières
            contexteMemoire += '\n🧠 MÉMOIRE THERMIQUE - Conversations récentes:\n';
            
            conversationsRecentes.forEach((conv, index) => {
                if (conv.contenu_entree && conv.contenu_sortie) {
                    contexteMemoire += `${index + 1}. ${conv.contenu_entree} → ${conv.contenu_sortie}\n`;
                }
            });
        }
        
        // 🎯 AJOUTER INFORMATIONS SPÉCIFIQUES SELON TYPE
        if (declenchement.types_detectes.includes('appel_nom')) {
            contexteMemoire += '\n👤 APPEL PAR NOM DÉTECTÉ - Tu es JARVIS, assistant IA avec mémoire thermique.\n';
        }
        
        if (declenchement.types_detectes.includes('memoire_directe')) {
            contexteMemoire += '\n🧠 DEMANDE DE MÉMOIRE - Accède à tes souvenirs et conversations précédentes.\n';
        }
        
        // 🔄 PROMPT ENRICHI
        const promptEnrichi = `${contexteMemoire}\n\nUTILISATEUR: ${prompt}\n\nRÉPONDS EN UTILISANT TA MÉMOIRE:`;
        
        return promptEnrichi;
    }
    
    obtenirContexteMemoire(declenchement) {
        const conversations = this.lireConversations();
        
        if (!conversations || conversations.length === 0) {
            return null;
        }
        
        let contexte = 'MÉMOIRE THERMIQUE ACTIVÉE:\n';
        
        // 📚 DERNIÈRES CONVERSATIONS
        const recentes = conversations.slice(-3);
        recentes.forEach((conv, index) => {
            if (conv.contenu_entree && conv.contenu_sortie) {
                contexte += `${index + 1}. ${conv.contenu_entree} → ${conv.contenu_sortie}\n`;
            }
        });
        
        return contexte;
    }
    
    envoyerVersLlama(data, res, endpoint) {
        const postData = JSON.stringify(data);
        
        const options = {
            hostname: 'localhost',
            port: this.portLlama,
            path: `/${endpoint}`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        const proxyReq = http.request(options, (proxyRes) => {
            // 🔄 COPIER HEADERS
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            
            // 🔄 COPIER RÉPONSE
            proxyRes.pipe(res);
        });
        
        proxyReq.on('error', (error) => {
            console.error(`❌ Erreur proxy vers llama.cpp:`, error.message);
            res.writeHead(500);
            res.end(JSON.stringify({error: 'Erreur connexion llama.cpp'}));
        });
        
        proxyReq.write(postData);
        proxyReq.end();
    }
    
    redirigerVersLlama(req, res) {
        const options = {
            hostname: 'localhost',
            port: this.portLlama,
            path: req.url,
            method: req.method,
            headers: req.headers
        };
        
        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });
        
        proxyReq.on('error', (error) => {
            console.error(`❌ Erreur redirection:`, error.message);
            res.writeHead(500);
            res.end('Erreur proxy');
        });
        
        req.pipe(proxyReq);
    }
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur lecture mémoire:`, error.message);
            return null;
        }
    }
    
    lireConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error(`❌ Erreur lecture conversations:`, error.message);
            return [];
        }
    }
}

// 🚀 DÉMARRAGE MIDDLEWARE
if (require.main === module) {
    const middleware = new MiddlewareMemoireLlama();
    
    console.log(`\n🧠 MIDDLEWARE MÉMOIRE THERMIQUE ACTIF !`);
    console.log(`🔗 Utilisez localhost:8081 au lieu de localhost:8080`);
    console.log(`🧠 Mémoire thermique automatiquement connectée !`);
    
    // 🛡️ GESTION ARRÊT PROPRE
    process.on('SIGINT', () => {
        console.log(`\n🛑 Arrêt middleware mémoire thermique...`);
        process.exit(0);
    });
}

module.exports = MiddlewareMemoireLlama;
