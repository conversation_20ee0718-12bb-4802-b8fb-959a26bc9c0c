# 🔍 DIAGNOSTIC MCP - POURQUOI ÇA NE MARCHE PAS

## 🎯 **ÉTAPES DE DIAGNOSTIC**

### 1. 🔌 **VÉRIFIER LE SERVEUR MCP**
```bash
# Test status MCP
curl http://127.0.0.1:8086/mcp/status

# Doit retourner:
{"mcp_server":"ACTIF","internet_access":true,...}
```

### 2. 🤖 **VÉRIFIER TON AGENT LLAMA.CPP**
```bash
# Test health llama.cpp
curl http://127.0.0.1:8080/health

# Doit retourner:
{"status":"ok"}
```

### 3. 📥 **VÉRIFIER LE SCRIPT MCP**
```bash
# Test script d'injection
curl http://127.0.0.1:8086/ajouter_mcp_simple.js | head -5

# Doit retourner du JavaScript
```

### 4. 🌐 **OUVRIR LA PAGE DE TEST**
- **URL:** http://127.0.0.1:8086/test
- **Cliquer:** "📊 Status Complet"
- **Vérifier:** Tous les services sont OK

## 🔧 **MÉTHODES POUR AJOUTER MCP**

### 🎯 **MÉTHODE 1: Console Navigateur (Plus Fiable)**

1. **Ouvrir ton interface:** http://127.0.0.1:8080
2. **Appuyer F12** (outils développeur)
3. **Aller dans "Console"**
4. **Coller ce code:**
```javascript
var script = document.createElement('script');
script.src = 'http://127.0.0.1:8086/ajouter_mcp_simple.js';
script.onload = function() { console.log('✅ MCP chargé !'); };
script.onerror = function() { console.log('❌ Erreur MCP !'); };
document.head.appendChild(script);
```
5. **Appuyer Entrée**
6. **Regarder** si le bouton "🔌 MCP" apparaît en haut à droite

### 🎯 **MÉTHODE 2: Bookmarklet**

1. **Créer un marque-page** avec cette URL:
```javascript
javascript:(function(){var s=document.createElement('script');s.src='http://127.0.0.1:8086/ajouter_mcp_simple.js';s.onload=function(){alert('MCP chargé!')};document.head.appendChild(s);})();
```
2. **Aller sur:** http://127.0.0.1:8080
3. **Cliquer** sur le marque-page

### 🎯 **MÉTHODE 3: Test Direct**

1. **Ouvrir:** http://127.0.0.1:8086/test
2. **Cliquer:** "📥 Charger Script MCP"
3. **Cliquer:** "🔍 Vérifier Bouton"
4. **Voir** si le bouton MCP est détecté

## 🐛 **PROBLÈMES POSSIBLES**

### ❌ **"Serveur MCP inaccessible"**
**Solution:**
```bash
# Redémarrer le serveur MCP
node serveur_mcp_reel.js
```

### ❌ **"Script ne se charge pas"**
**Causes possibles:**
- CORS bloqué par le navigateur
- Serveur MCP arrêté
- Fichier script corrompu

**Solution:**
```bash
# Vérifier le script
curl http://127.0.0.1:8086/ajouter_mcp_simple.js
```

### ❌ **"Bouton MCP n'apparaît pas"**
**Causes possibles:**
- Script chargé mais erreur JavaScript
- Interface llama.cpp bloque l'injection
- Z-index trop bas

**Solution:**
1. **Ouvrir F12 → Console**
2. **Regarder** les erreurs JavaScript
3. **Essayer** ce code de test:
```javascript
// Test simple d'injection
var btn = document.createElement('button');
btn.innerHTML = '🔌 TEST';
btn.style.cssText = 'position:fixed;top:10px;right:10px;z-index:99999;background:red;color:white;padding:10px;';
document.body.appendChild(btn);
```

### ❌ **"Zone de saisie non trouvée"**
**Cause:** Le script ne trouve pas la zone de texte de ton interface

**Solution:** Inspecter ton interface pour trouver le bon sélecteur:
1. **F12 → Elements**
2. **Cliquer** sur la zone de texte de ton interface
3. **Noter** le sélecteur (ex: `textarea`, `.input-field`, etc.)
4. **Modifier** le script si nécessaire

## 🧪 **TESTS À FAIRE MAINTENANT**

### ✅ **Test 1: Services de base**
```bash
# Dans le terminal
curl http://127.0.0.1:8086/mcp/status
curl http://127.0.0.1:8080/health
```

### ✅ **Test 2: Page de diagnostic**
- **Ouvrir:** http://127.0.0.1:8086/test
- **Cliquer:** tous les boutons de test
- **Vérifier:** que tout est vert

### ✅ **Test 3: Injection manuelle**
- **Ouvrir:** http://127.0.0.1:8080 (ton interface)
- **F12 → Console**
- **Coller** le code d'injection
- **Vérifier** l'apparition du bouton

### ✅ **Test 4: Fonctionnement MCP**
- **Si bouton MCP visible:** cliquer dessus
- **Taper:** "actualités 2025"
- **Vérifier:** que les données sont récupérées

## 📊 **RÉSULTATS ATTENDUS**

### ✅ **Si tout fonctionne:**
1. **Serveur MCP:** Status "ACTIF"
2. **llama.cpp:** Status "ok"
3. **Script:** Se charge sans erreur
4. **Bouton MCP:** Visible en haut à droite
5. **Données MCP:** Se récupèrent et s'affichent

### ❌ **Si ça ne marche pas:**
1. **Regarder** les logs du serveur MCP
2. **Vérifier** la console du navigateur (F12)
3. **Tester** chaque étape individuellement
4. **Me dire** exactement où ça bloque

## 🎯 **PROCHAINES ÉTAPES**

**Fais ces tests dans l'ordre et dis-moi:**
1. **Quel test échoue** en premier
2. **Quels messages d'erreur** tu vois
3. **Si le bouton MCP apparaît** ou pas
4. **Si les données MCP** se récupèrent

**Avec ces infos, je pourrai corriger le problème précis !**
