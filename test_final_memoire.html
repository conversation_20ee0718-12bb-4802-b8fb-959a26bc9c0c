<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Final - Mémoire Thermique JARVIS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e; color: white;
            padding: 20px; line-height: 1.6;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px; border-radius: 15px;
        }
        .test-result {
            padding: 15px; margin: 10px 0;
            border-radius: 8px; border-left: 4px solid;
        }
        .success { border-color: #4CAF50; background: rgba(76, 175, 80, 0.2); }
        .error { border-color: #f44336; background: rgba(244, 67, 54, 0.2); }
        .info { border-color: #2196F3; background: rgba(33, 150, 243, 0.2); }
        .warning { border-color: #FF9800; background: rgba(255, 152, 0, 0.2); }
        button {
            background: #4CAF50; color: white; border: none;
            padding: 12px 24px; border-radius: 8px; cursor: pointer;
            margin: 10px 5px; font-size: 16px; font-weight: bold;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .code {
            background: #000; color: #0f0; padding: 15px;
            border-radius: 8px; font-family: monospace;
            font-size: 14px; margin: 10px 0;
        }
        .verdict {
            text-align: center; font-size: 1.5em;
            padding: 20px; margin: 20px 0;
            border-radius: 10px; font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Final - Mémoire Thermique JARVIS</h1>
        <p>Ce test vérifie si la mémoire thermique fonctionne vraiment avec DeepSeek R1 8B</p>

        <div class="test-result info">
            <h3>📋 Plan de Test</h3>
            <ol>
                <li>Vérifier la connexion au serveur DeepSeek R1 8B</li>
                <li>Tester les fonctions de mémoire thermique</li>
                <li>Simuler une conversation avec mémoire</li>
                <li>Vérifier que l'agent se souvient des informations</li>
            </ol>
        </div>

        <button onclick="runFullTest()">🚀 Lancer Test Complet</button>
        <button onclick="testMemoryOnly()">🧠 Test Mémoire Seule</button>
        <button onclick="clearResults()">🗑️ Effacer</button>

        <div id="results"></div>
        <div id="verdict"></div>

        <div class="code" id="log">
            [Prêt] Cliquez sur "Lancer Test Complet" pour commencer
        </div>
    </div>

    <script>
        let testResults = [];
        let thermalMemory = [];

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#0f0', error: '#f44', success: '#4f4', warning: '#fa0' };
            logDiv.innerHTML += `<div style="color: ${colors[type]}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function addResult(title, message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<h3>${title}</h3><p>${message}</p>`;
            resultsDiv.appendChild(resultDiv);
            testResults.push({ title, message, type, success: type === 'success' });
        }

        async function testConnection() {
            log('🔧 Test connexion serveur...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/health', { timeout: 3000 });
                if (response.ok) {
                    addResult('✅ Connexion Serveur', 'DeepSeek R1 8B est accessible', 'success');
                    log('✅ Serveur accessible', 'success');
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('❌ Connexion Serveur', `Erreur: ${error.message}`, 'error');
                log(`❌ Connexion échouée: ${error.message}`, 'error');
                return false;
            }
        }

        function testMemoryFunctions() {
            log('🧠 Test fonctions mémoire...', 'info');
            
            try {
                // Test sauvegarde
                thermalMemory = [];
                saveThermalMemory('Bonjour, je m\'appelle Jean-Luc', 'Enchanté Jean-Luc !');
                
                if (thermalMemory.length === 1) {
                    addResult('✅ Sauvegarde Mémoire', 'La fonction de sauvegarde fonctionne', 'success');
                    log('✅ Sauvegarde OK', 'success');
                } else {
                    throw new Error('Sauvegarde échouée');
                }

                // Test recherche
                const result = searchMemory('tu te souviens de mon nom ?');
                if (result && result.includes('Jean-Luc')) {
                    addResult('✅ Recherche Mémoire', 'La fonction de recherche fonctionne', 'success');
                    log('✅ Recherche OK', 'success');
                    return true;
                } else {
                    throw new Error('Recherche échouée');
                }
            } catch (error) {
                addResult('❌ Fonctions Mémoire', `Erreur: ${error.message}`, 'error');
                log(`❌ Fonctions mémoire: ${error.message}`, 'error');
                return false;
            }
        }

        async function testAgentMemory() {
            log('🤖 Test mémoire avec agent...', 'info');
            
            try {
                // Première question
                log('📝 Étape 1: Présentation', 'info');
                const response1 = await askAgent('Bonjour, je m\'appelle Jean-Luc');
                saveThermalMemory('Bonjour, je m\'appelle Jean-Luc', response1);
                log(`👤 "Bonjour, je m'appelle Jean-Luc" → 🤖 "${response1.substring(0, 50)}..."`, 'info');

                // Attendre un peu
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Deuxième question avec mémoire
                log('📝 Étape 2: Test rappel', 'info');
                const memoryContext = searchMemory('tu te souviens de mon nom ?');
                const promptWithMemory = 'Tu te souviens de mon nom ?' + memoryContext;
                
                log(`🧠 Prompt avec mémoire: "${promptWithMemory.substring(0, 100)}..."`, 'info');
                
                const response2 = await askAgent(promptWithMemory);
                saveThermalMemory('Tu te souviens de mon nom ?', response2);
                log(`👤 "Tu te souviens de mon nom ?" → 🤖 "${response2.substring(0, 50)}..."`, 'info');

                // Vérifier si l'agent se souvient
                if (response2.toLowerCase().includes('jean-luc') || 
                    response2.toLowerCase().includes('jean luc')) {
                    addResult('✅ Mémoire Agent', 'L\'agent se souvient du nom !', 'success');
                    log('✅ MÉMOIRE FONCTIONNE !', 'success');
                    return true;
                } else {
                    addResult('❌ Mémoire Agent', 'L\'agent ne se souvient pas du nom', 'error');
                    log('❌ Mémoire défaillante', 'error');
                    return false;
                }

            } catch (error) {
                addResult('❌ Test Agent', `Erreur: ${error.message}`, 'error');
                log(`❌ Test agent échoué: ${error.message}`, 'error');
                return false;
            }
        }

        async function askAgent(prompt) {
            const response = await fetch('http://localhost:8000/completion', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    prompt: prompt,
                    n_predict: 100,
                    temperature: 0.7,
                    stream: false
                })
            });

            if (!response.ok) throw new Error(`Erreur ${response.status}`);

            const data = await response.json();
            return (data.content || data.text || '').trim();
        }

        function saveThermalMemory(userMessage, agentResponse) {
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                user: userMessage,
                agent: agentResponse
            };
            
            thermalMemory.push(entry);
            log(`💾 Sauvegardé: "${userMessage}" → "${agentResponse.substring(0, 30)}..."`, 'info');
        }

        function searchMemory(query) {
            if (thermalMemory.length === 0) return '';
            
            const queryLower = query.toLowerCase();
            const triggers = ['tu te souviens', 'rappelle-toi', 'mon nom'];
            
            if (triggers.some(trigger => queryLower.includes(trigger))) {
                const results = thermalMemory.filter(entry => 
                    entry.user?.toLowerCase().includes('appelle') || 
                    entry.user?.toLowerCase().includes('nom')
                );
                
                if (results.length > 0) {
                    return '\n\nCONTEXTE MÉMOIRE:\n' + 
                           results.map(r => `- "${r.user}" → "${r.agent}"`).join('\n') + 
                           '\nUtilise ce contexte pour répondre.\n';
                }
            }
            
            return '';
        }

        async function runFullTest() {
            log('🚀 Début du test complet', 'info');
            document.getElementById('results').innerHTML = '';
            document.getElementById('verdict').innerHTML = '';
            testResults = [];

            // Test 1: Connexion
            const connectionOK = await testConnection();
            
            // Test 2: Fonctions mémoire
            const memoryFunctionsOK = testMemoryFunctions();
            
            // Test 3: Mémoire avec agent (seulement si connexion OK)
            let agentMemoryOK = false;
            if (connectionOK) {
                agentMemoryOK = await testAgentMemory();
            } else {
                addResult('⚠️ Test Agent', 'Ignoré - pas de connexion serveur', 'warning');
            }

            // Verdict final
            showVerdict(connectionOK, memoryFunctionsOK, agentMemoryOK);
        }

        async function testMemoryOnly() {
            log('🧠 Test mémoire seule', 'info');
            document.getElementById('results').innerHTML = '';
            document.getElementById('verdict').innerHTML = '';
            testResults = [];

            const memoryOK = testMemoryFunctions();
            
            if (memoryOK) {
                addResult('✅ Test Mémoire', 'Toutes les fonctions mémoire fonctionnent', 'success');
                document.getElementById('verdict').innerHTML = 
                    '<div class="verdict success">🎉 Mémoire Thermique Fonctionnelle !</div>';
            } else {
                document.getElementById('verdict').innerHTML = 
                    '<div class="verdict error">❌ Problème avec la Mémoire Thermique</div>';
            }
        }

        function showVerdict(connection, memoryFunctions, agentMemory) {
            const verdictDiv = document.getElementById('verdict');
            const successCount = [connection, memoryFunctions, agentMemory].filter(Boolean).length;
            
            let verdict = '';
            let className = '';
            
            if (successCount === 3) {
                verdict = '🎉 PARFAIT ! Mémoire thermique 100% fonctionnelle avec DeepSeek R1 8B !';
                className = 'success';
            } else if (successCount === 2 && memoryFunctions) {
                verdict = '✅ BIEN ! Mémoire thermique fonctionne, problème de connexion serveur';
                className = 'warning';
            } else if (memoryFunctions) {
                verdict = '⚠️ PARTIEL ! Fonctions mémoire OK, mais problème avec l\'agent';
                className = 'warning';
            } else {
                verdict = '❌ ÉCHEC ! Problème avec les fonctions de base de la mémoire';
                className = 'error';
            }
            
            verdictDiv.innerHTML = `<div class="verdict ${className}">${verdict}</div>`;
            log(`🏁 Test terminé: ${successCount}/3 réussis`, successCount === 3 ? 'success' : 'warning');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('verdict').innerHTML = '';
            document.getElementById('log').innerHTML = '[Effacé] Prêt pour un nouveau test';
            testResults = [];
            thermalMemory = [];
        }

        // Initialisation
        log('🧪 Test final de mémoire thermique prêt', 'info');
        log('✅ Mémoire thermique activée par défaut (comme dans JARVIS)', 'success');
        log('💡 Assurez-vous que le serveur DeepSeek R1 8B fonctionne', 'warning');
    </script>
</body>
</html>
