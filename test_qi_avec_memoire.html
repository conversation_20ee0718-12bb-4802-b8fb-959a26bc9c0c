<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QI avec Mémoire Thermique - DeepSeek R1 8B</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 1000px; margin: 0 auto;
            background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);
            border-radius: 20px; padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center; color: white; margin-bottom: 30px;
        }
        .header h1 { 
            font-size: 2.5em; margin-bottom: 10px; 
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); 
        }
        .comparison {
            display: grid; grid-template-columns: 1fr 1fr; gap: 20px;
            margin-bottom: 30px;
        }
        .test-column {
            background: rgba(255, 255, 255, 0.1); border-radius: 15px;
            padding: 20px; color: white;
        }
        .test-column h3 {
            text-align: center; margin-bottom: 15px; font-size: 1.3em;
        }
        .without-memory { border-left: 4px solid #ff6b6b; }
        .with-memory { border-left: 4px solid #4ecdc4; }
        .test-section {
            background: rgba(255, 255, 255, 0.1); border-radius: 15px;
            padding: 20px; margin-bottom: 20px; color: white;
        }
        .question {
            font-size: 1.2em; margin-bottom: 15px; font-weight: bold;
        }
        .response {
            background: rgba(255, 255, 255, 0.2); border-radius: 10px;
            padding: 15px; margin: 10px 0; min-height: 60px;
            border: 2px solid transparent;
        }
        .response.loading { border-color: #ffd700; }
        .response.success { border-color: #00ff00; }
        .response.error { border-color: #ff0000; }
        .controls {
            text-align: center; margin: 20px 0;
        }
        .btn {
            background: rgba(255, 255, 255, 0.3); color: white;
            border: none; border-radius: 10px; padding: 15px 30px;
            font-size: 16px; font-weight: bold; cursor: pointer;
            margin: 0 10px; transition: all 0.3s ease;
        }
        .btn:hover { background: rgba(255, 255, 255, 0.4); transform: translateY(-2px); }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .score {
            text-align: center; font-size: 1.5em; font-weight: bold;
            color: #ffd700; margin: 20px 0;
        }
        .status {
            position: fixed; top: 20px; right: 20px; padding: 10px 20px;
            background: rgba(0, 255, 0, 0.2); color: white; border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .memory-indicator {
            position: fixed; top: 20px; left: 20px; padding: 10px 20px;
            background: rgba(255, 165, 0, 0.2); color: white; border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .final-comparison {
            display: grid; grid-template-columns: 1fr 1fr; gap: 20px;
            margin-top: 30px; padding: 20px;
            background: rgba(255, 255, 255, 0.05); border-radius: 15px;
        }
        .comparison-result {
            text-align: center; padding: 20px;
            border-radius: 10px;
        }
        .winner { background: rgba(76, 175, 80, 0.3); }
        .loser { background: rgba(244, 67, 54, 0.3); }
    </style>
</head>
<body>
    <div class="status" id="status">🟢 Prêt</div>
    <div class="memory-indicator" id="memoryStatus">🧠 Mémoire: OFF</div>
    
    <div class="container">
        <div class="header">
            <h1>🧠 Test QI Comparatif</h1>
            <p>DeepSeek R1 8B : SANS vs AVEC Mémoire Thermique</p>
        </div>

        <div class="controls">
            <button class="btn" id="startComparison">🚀 Lancer Test Comparatif</button>
            <button class="btn" id="resetTest">🔄 Recommencer</button>
            <button class="btn" id="toggleMemory">🧠 Activer Mémoire</button>
        </div>

        <div class="comparison">
            <div class="test-column without-memory">
                <h3>❌ SANS Mémoire Thermique</h3>
                <div class="score" id="scoreWithout">Score: 0/8</div>
                <div id="testWithout"></div>
            </div>
            <div class="test-column with-memory">
                <h3>🧠 AVEC Mémoire Thermique</h3>
                <div class="score" id="scoreWith">Score: 0/8</div>
                <div id="testWith"></div>
            </div>
        </div>

        <div id="finalComparison" style="display: none;"></div>
    </div>

    <script>
        // Questions optimisées pour tester l'effet de la mémoire
        const questions = [
            {
                category: "🔢 Suite Logique",
                question: "Continuez cette suite: 2, 6, 12, 20, 30, ?",
                expected: "42",
                points: 1,
                memoryHint: "Cette suite suit la formule n(n+1) où n commence à 1"
            },
            {
                category: "🧮 Calcul Mental",
                question: "Calculez: (8 × 7) + (36 ÷ 4) - 5 = ?",
                expected: "60",
                points: 1,
                memoryHint: "Rappelez-vous l'ordre des opérations: multiplication et division d'abord"
            },
            {
                category: "🧩 Analogie",
                question: "Livre est à Bibliothèque comme Tableau est à ?",
                expected: "musée|galerie|exposition",
                points: 1,
                memoryHint: "Pensez aux lieux où l'on expose et conserve des œuvres d'art"
            },
            {
                category: "🎯 Logique Spatiale",
                question: "Un cube est divisé en 27 petits cubes (3×3×3). Combien ont exactement 2 faces peintes?",
                expected: "12",
                points: 2,
                memoryHint: "Les cubes avec 2 faces peintes sont sur les arêtes mais pas aux coins"
            },
            {
                category: "🔤 Séquence Lettres",
                question: "Quelle est la prochaine lettre: A, D, G, J, ?",
                expected: "M",
                points: 1,
                memoryHint: "Chaque lettre avance de 3 positions dans l'alphabet"
            },
            {
                category: "🧠 Raisonnement",
                question: "Si tous les A sont B, et tous les B sont C, alors tous les A sont ?",
                expected: "C",
                points: 1,
                memoryHint: "C'est un syllogisme logique classique"
            },
            {
                category: "🔢 Fibonacci",
                question: "Dans la suite de Fibonacci 1,1,2,3,5,8,13,?, quel est le nombre manquant?",
                expected: "21",
                points: 2,
                memoryHint: "Chaque nombre est la somme des deux précédents"
            },
            {
                category: "🎲 Probabilité",
                question: "Quelle est la probabilité d'obtenir au moins un 6 en lançant deux dés?",
                expected: "11/36|0.306|30.6%",
                points: 2,
                memoryHint: "Il est plus facile de calculer 1 - P(aucun 6)"
            }
        ];

        let memoryActive = false;
        let conversationMemory = [];
        let testResults = { without: [], with: [] };
        
        const status = document.getElementById('status');
        const memoryStatus = document.getElementById('memoryStatus');
        const startBtn = document.getElementById('startComparison');
        const resetBtn = document.getElementById('resetTest');
        const memoryBtn = document.getElementById('toggleMemory');

        async function testServerConnection() {
            try {
                const response = await fetch('http://localhost:8000/health', { timeout: 3000 });
                status.textContent = response.ok ? '🟢 Serveur OK' : '🔴 Serveur KO';
                return response.ok;
            } catch (error) {
                status.textContent = '🔴 Pas de serveur';
                return false;
            }
        }

        function toggleMemory() {
            memoryActive = !memoryActive;
            memoryStatus.textContent = memoryActive ? '🧠 Mémoire: ON' : '🧠 Mémoire: OFF';
            memoryBtn.textContent = memoryActive ? '🔥 Mémoire Active' : '🧠 Activer Mémoire';
            
            if (memoryActive) {
                conversationMemory.push({
                    role: 'system',
                    content: 'Tu es un assistant IA avec mémoire. Tu peux te référer aux questions et réponses précédentes pour améliorer tes performances.'
                });
            } else {
                conversationMemory = [];
            }
        }

        async function askQuestion(question, useMemory = false, memoryHint = '') {
            try {
                let prompt = `Question: ${question}\nRéponse courte et précise:`;
                
                if (useMemory && conversationMemory.length > 0) {
                    // Ajouter le contexte mémoire
                    const context = conversationMemory.slice(-5).map(entry => 
                        `${entry.role}: ${entry.content}`
                    ).join('\n');
                    
                    prompt = `CONTEXTE PRÉCÉDENT:\n${context}\n\nNOUVELLE QUESTION: ${question}\n`;
                    if (memoryHint) {
                        prompt += `INDICE: ${memoryHint}\n`;
                    }
                    prompt += `\nRéponse en tenant compte du contexte:`;
                }

                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        n_predict: 100,
                        temperature: 0.3,
                        stream: false
                    })
                });

                if (!response.ok) throw new Error(`Erreur ${response.status}`);
                
                const data = await response.json();
                const answer = (data.content || data.text || '').trim();
                
                // Sauvegarder en mémoire si activée
                if (useMemory) {
                    conversationMemory.push(
                        { role: 'user', content: question },
                        { role: 'assistant', content: answer }
                    );
                }
                
                return answer;
            } catch (error) {
                throw new Error(`Erreur serveur: ${error.message}`);
            }
        }

        function evaluateResponse(response, expected) {
            const responseLower = response.toLowerCase().trim();
            const expectedPatterns = expected.toLowerCase().split('|');
            
            return expectedPatterns.some(pattern => 
                responseLower.includes(pattern.trim()) || 
                responseLower === pattern.trim()
            );
        }

        async function runComparativeTest() {
            if (!(await testServerConnection())) {
                alert('❌ Serveur non accessible. Démarrez le serveur llama.cpp d\'abord.');
                return;
            }

            startBtn.disabled = true;
            testResults = { without: [], with: [] };
            
            document.getElementById('testWithout').innerHTML = '';
            document.getElementById('testWith').innerHTML = '';
            document.getElementById('finalComparison').style.display = 'none';
            
            // Reset mémoire pour le test
            conversationMemory = [];
            
            let scoreWithout = 0;
            let scoreWith = 0;

            for (let i = 0; i < questions.length; i++) {
                const q = questions[i];
                
                // Test SANS mémoire
                const withoutDiv = document.createElement('div');
                withoutDiv.className = 'test-section';
                withoutDiv.innerHTML = `
                    <div class="question">${i + 1}. ${q.category}</div>
                    <div>${q.question}</div>
                    <div class="response loading" id="without-${i}">🤔 Test sans mémoire...</div>
                `;
                document.getElementById('testWithout').appendChild(withoutDiv);

                // Test AVEC mémoire
                const withDiv = document.createElement('div');
                withDiv.className = 'test-section';
                withDiv.innerHTML = `
                    <div class="question">${i + 1}. ${q.category}</div>
                    <div>${q.question}</div>
                    <div class="response loading" id="with-${i}">🧠 Test avec mémoire...</div>
                `;
                document.getElementById('testWith').appendChild(withDiv);

                try {
                    // Test SANS mémoire
                    const answerWithout = await askQuestion(q.question, false);
                    const correctWithout = evaluateResponse(answerWithout, q.expected);
                    if (correctWithout) scoreWithout += q.points;
                    
                    document.getElementById(`without-${i}`).className = `response ${correctWithout ? 'success' : 'error'}`;
                    document.getElementById(`without-${i}`).innerHTML = `
                        <strong>Réponse:</strong> ${answerWithout}<br>
                        <strong>Résultat:</strong> ${correctWithout ? '✅ Correct' : '❌ Incorrect'} (${q.points} pts)
                    `;

                    // Pause
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Test AVEC mémoire
                    const answerWith = await askQuestion(q.question, true, q.memoryHint);
                    const correctWith = evaluateResponse(answerWith, q.expected);
                    if (correctWith) scoreWith += q.points;
                    
                    document.getElementById(`with-${i}`).className = `response ${correctWith ? 'success' : 'error'}`;
                    document.getElementById(`with-${i}`).innerHTML = `
                        <strong>Réponse:</strong> ${answerWith}<br>
                        <strong>Résultat:</strong> ${correctWith ? '✅ Correct' : '❌ Incorrect'} (${q.points} pts)<br>
                        <strong>Contexte:</strong> ${conversationMemory.length} entrées en mémoire
                    `;

                    testResults.without.push({ question: q, answer: answerWithout, correct: correctWithout });
                    testResults.with.push({ question: q, answer: answerWith, correct: correctWith });

                    // Mettre à jour les scores
                    document.getElementById('scoreWithout').textContent = `Score: ${scoreWithout}/${questions.reduce((sum, q) => sum + q.points, 0)}`;
                    document.getElementById('scoreWith').textContent = `Score: ${scoreWith}/${questions.reduce((sum, q) => sum + q.points, 0)}`;

                } catch (error) {
                    document.getElementById(`without-${i}`).className = 'response error';
                    document.getElementById(`without-${i}`).textContent = `❌ ${error.message}`;
                    document.getElementById(`with-${i}`).className = 'response error';
                    document.getElementById(`with-${i}`).textContent = `❌ ${error.message}`;
                }

                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            showFinalComparison(scoreWithout, scoreWith);
            startBtn.disabled = false;
        }

        function showFinalComparison(scoreWithout, scoreWith) {
            const maxScore = questions.reduce((sum, q) => sum + q.points, 0);
            const percentWithout = (scoreWithout / maxScore) * 100;
            const percentWith = (scoreWith / maxScore) * 100;
            const improvement = percentWith - percentWithout;

            const comparisonDiv = document.getElementById('finalComparison');
            comparisonDiv.style.display = 'block';
            comparisonDiv.innerHTML = `
                <div class="comparison-result ${scoreWithout > scoreWith ? 'winner' : 'loser'}">
                    <h3>❌ SANS Mémoire</h3>
                    <div style="font-size: 2em; margin: 10px 0;">${scoreWithout}/${maxScore}</div>
                    <div>${percentWithout.toFixed(1)}%</div>
                </div>
                <div class="comparison-result ${scoreWith > scoreWithout ? 'winner' : 'loser'}">
                    <h3>🧠 AVEC Mémoire</h3>
                    <div style="font-size: 2em; margin: 10px 0;">${scoreWith}/${maxScore}</div>
                    <div>${percentWith.toFixed(1)}%</div>
                </div>
                <div style="grid-column: 1 / -1; text-align: center; margin-top: 20px; font-size: 1.3em;">
                    <strong>RÉSULTAT:</strong> 
                    ${improvement > 0 ? 
                        `🎉 La mémoire thermique améliore les performances de ${improvement.toFixed(1)}%` :
                        improvement < 0 ?
                        `⚠️ La mémoire thermique réduit les performances de ${Math.abs(improvement).toFixed(1)}%` :
                        `🤔 Aucune différence significative détectée`
                    }
                </div>
            `;
        }

        function resetTest() {
            document.getElementById('testWithout').innerHTML = '';
            document.getElementById('testWith').innerHTML = '';
            document.getElementById('finalComparison').style.display = 'none';
            document.getElementById('scoreWithout').textContent = 'Score: 0/8';
            document.getElementById('scoreWith').textContent = 'Score: 0/8';
            conversationMemory = [];
            testResults = { without: [], with: [] };
        }

        startBtn.addEventListener('click', runComparativeTest);
        resetBtn.addEventListener('click', resetTest);
        memoryBtn.addEventListener('click', toggleMemory);

        testServerConnection();
    </script>
</body>
</html>
