// 🧠 AMÉLIORATION INTERFACE JARVIS EXISTANTE
// Script pour ajouter des fonctionnalités à ton interface qui fonctionne

console.log('🧠 AMÉLIORATION JARVIS - Démarrage');

// 🎯 ATTENDRE QUE L'INTERFACE SOIT CHARGÉE
function attendreInterface() {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ameliorerJarvis);
    } else {
        setTimeout(ameliorerJarvis, 2000); // Attendre que ton interface soit prête
    }
}

function ameliorerJarvis() {
    console.log('🧠 AMÉLIORATION JARVIS - Initialisation');
    
    // 🔍 VÉRIFIER SI TON INTERFACE EST PRÉSENTE
    const agentNameInput = document.getElementById('agent-name-input');
    const qiDisplay = document.getElementById('qi-display');
    
    if (agentNameInput || qiDisplay) {
        console.log('✅ Interface JARVIS détectée - Ajout des améliorations');
        ajouterAmeliorations();
    } else {
        console.log('⚠️ Interface JARVIS non détectée - Création du panneau');
        creerPanneauJarvis();
    }
}

function ajouterAmeliorations() {
    // 🎨 AJOUTER STYLES AMÉLIORÉS
    ajouterStylesAmeliores();
    
    // 🧠 AJOUTER PANNEAU COGNITIF
    ajouterPanneauCognitif();
    
    // 🚀 AMÉLIORER FONCTIONNALITÉS EXISTANTES
    ameliorerFonctionnalites();
    
    console.log('✅ Améliorations JARVIS appliquées');
}

function ajouterStylesAmeliores() {
    const style = document.createElement('style');
    style.textContent = `
        /* 🧠 STYLES JARVIS AMÉLIORÉS */
        .jarvis-panel-cognitif {
            position: fixed !important;
            top: 10px !important;
            left: 10px !important;
            width: 300px !important;
            background: rgba(0,0,0,0.95) !important;
            border: 2px solid #00ffff !important;
            border-radius: 10px !important;
            padding: 15px !important;
            color: #00ffff !important;
            font-family: 'Courier New', monospace !important;
            z-index: 999999 !important;
            box-shadow: 0 0 20px rgba(0,255,255,0.5) !important;
            font-size: 12px !important;
        }
        
        .jarvis-header-cognitif {
            text-align: center !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #00ffff !important;
            padding-bottom: 8px !important;
        }
        
        .jarvis-buttons-cognitif {
            display: flex !important;
            gap: 5px !important;
            margin-bottom: 8px !important;
            flex-wrap: wrap !important;
        }
        
        .jarvis-btn-cognitif {
            flex: 1 !important;
            padding: 6px !important;
            background: #1a1a2e !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 10px !important;
            min-width: 70px !important;
            transition: all 0.2s !important;
        }
        
        .jarvis-btn-cognitif:hover {
            background: #00ffff !important;
            color: #000 !important;
            transform: scale(1.05) !important;
        }
        
        .jarvis-status-cognitif {
            background: rgba(0,255,255,0.1) !important;
            padding: 8px !important;
            border-radius: 5px !important;
            text-align: center !important;
            font-size: 11px !important;
            margin-top: 8px !important;
        }
        
        .jarvis-minimized {
            height: 40px !important;
            overflow: hidden !important;
        }
        
        /* 🎯 AMÉLIORATION INTERFACE EXISTANTE */
        #agent-name-input {
            background: rgba(0,255,255,0.1) !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
        }
        
        #qi-display {
            color: #00ff00 !important;
            font-weight: bold !important;
            text-shadow: 0 0 5px #00ff00 !important;
        }
    `;
    document.head.appendChild(style);
}

function ajouterPanneauCognitif() {
    const panel = document.createElement('div');
    panel.id = 'jarvis-panel-cognitif';
    panel.className = 'jarvis-panel-cognitif';
    panel.innerHTML = `
        <div class="jarvis-header-cognitif">
            <h3 style="margin: 0; font-size: 14px;">🧠 JARVIS COGNITIF</h3>
            <div style="font-size: 10px;">Interface Améliorée</div>
        </div>
        
        <div class="jarvis-buttons-cognitif">
            <button class="jarvis-btn-cognitif" onclick="jarvisMemoire()">🧠 Mémoire</button>
            <button class="jarvis-btn-cognitif" onclick="jarvisEvoluer()">🚀 Évoluer</button>
            <button class="jarvis-btn-cognitif" onclick="jarvisAnalyse()">🔍 Analyse</button>
        </div>
        
        <div class="jarvis-buttons-cognitif">
            <button class="jarvis-btn-cognitif" onclick="jarvisConnexion()">🔗 Test</button>
            <button class="jarvis-btn-cognitif" onclick="jarvisToggleCognitif()">📱 Réduire</button>
            <button class="jarvis-btn-cognitif" onclick="jarvisInfo()">ℹ️ Info</button>
        </div>
        
        <div class="jarvis-status-cognitif">
            <div>QI: <span id="jarvis-qi-cognitif">341.0</span></div>
            <div>🧠 Mémoire Thermique Active</div>
            <div>⚡ Système Cognitif Opérationnel</div>
        </div>
    `;
    
    document.body.appendChild(panel);
}

function ameliorerFonctionnalites() {
    // 🧠 VARIABLES GLOBALES AMÉLIORÉES
    window.jarvisQICognitif = 341.0;
    window.jarvisMinimizedCognitif = false;
    window.jarvisConversationsCognitif = 0;
    
    // 🧠 FONCTIONS JARVIS AMÉLIORÉES
    window.jarvisMemoire = function() {
        const nom = document.getElementById('agent-name-input')?.value || 'JARVIS';
        const prompt = `${nom}, utilise ta mémoire thermique pour analyser nos conversations précédentes. Explique ce que tu as appris et comment tu as évolué.`;
        envoyerPromptAmeliore(prompt);
        console.log('🧠 Mémoire thermique activée');
        
        // Mettre à jour QI
        window.jarvisQICognitif += 2;
        updateQICognitif();
    };
    
    window.jarvisEvoluer = function() {
        const evolution = Math.random() * 5 + 2;
        window.jarvisQICognitif += evolution;
        updateQICognitif();
        
        const nom = document.getElementById('agent-name-input')?.value || 'JARVIS';
        const prompt = `${nom}, ton QI vient d'évoluer à ${window.jarvisQICognitif.toFixed(1)}. Explique tes nouvelles capacités cognitives et comment tu peux mieux m'aider maintenant.`;
        envoyerPromptAmeliore(prompt);
        console.log('🚀 Évolution QI:', window.jarvisQICognitif.toFixed(1));
    };
    
    window.jarvisAnalyse = function() {
        const nom = document.getElementById('agent-name-input')?.value || 'JARVIS';
        const prompt = `${nom}, effectue une auto-analyse complète de tes processus cognitifs. Explique comment tu traites l'information, tes capacités d'apprentissage et ton évolution depuis notre dernière conversation.`;
        envoyerPromptAmeliore(prompt);
        console.log('🔍 Analyse cognitive lancée');
    };
    
    window.jarvisConnexion = function() {
        // Test de connexion au serveur
        fetch('http://127.0.0.1:8080/completion', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                prompt: 'JARVIS test de connexion cognitive',
                n_predict: 50
            })
        })
        .then(response => {
            if (response.ok) {
                console.log('✅ Connexion serveur réussie');
                alert('✅ Connexion au serveur JARVIS réussie !');
            } else {
                console.log('❌ Connexion serveur échouée');
                alert('❌ Connexion au serveur échouée');
            }
        })
        .catch(error => {
            console.log('❌ Erreur connexion:', error);
            alert('❌ Erreur de connexion au serveur');
        });
    };
    
    window.jarvisToggleCognitif = function() {
        const panel = document.getElementById('jarvis-panel-cognitif');
        if (panel) {
            if (window.jarvisMinimizedCognitif) {
                panel.classList.remove('jarvis-minimized');
                window.jarvisMinimizedCognitif = false;
            } else {
                panel.classList.add('jarvis-minimized');
                window.jarvisMinimizedCognitif = true;
            }
        }
    };
    
    window.jarvisInfo = function() {
        const info = `🧠 JARVIS COGNITIF - Informations:

QI Actuel: ${window.jarvisQICognitif.toFixed(1)}
Conversations: ${window.jarvisConversationsCognitif}
Mémoire: Thermique Active
Statut: Opérationnel
Version: Améliorée 2.0

Interface: ${document.getElementById('agent-name-input') ? 'Détectée' : 'Non détectée'}
Serveur: Port 8080`;
        
        alert(info);
    };
    
    // 🚀 ÉVOLUTION AUTOMATIQUE
    setInterval(() => {
        window.jarvisQICognitif += Math.random() * 0.1;
        updateQICognitif();
    }, 15000);
}

function updateQICognitif() {
    const qiElement = document.getElementById('jarvis-qi-cognitif');
    if (qiElement) {
        qiElement.textContent = window.jarvisQICognitif.toFixed(1);
    }
    
    // Mettre à jour aussi l'affichage original si présent
    const qiOriginal = document.getElementById('qi-display');
    if (qiOriginal) {
        qiOriginal.textContent = window.jarvisQICognitif.toFixed(1);
    }
}

function envoyerPromptAmeliore(prompt) {
    // 🎯 TROUVER ZONE DE SAISIE DANS TON INTERFACE
    const selectors = [
        'textarea',
        'input[type="text"]:not(#agent-name-input)',
        '[contenteditable="true"]',
        '.input-field',
        '#prompt-input',
        '#message-input'
    ];
    
    let input = null;
    for (const selector of selectors) {
        input = document.querySelector(selector);
        if (input && input.id !== 'agent-name-input') break;
    }
    
    if (input) {
        // 📝 INSÉRER PROMPT
        if (input.tagName === 'TEXTAREA' || input.tagName === 'INPUT') {
            input.value = prompt;
        } else {
            input.textContent = prompt;
        }
        
        input.focus();
        window.jarvisConversationsCognitif++;
        
        // 🚀 ESSAYER D'ENVOYER
        setTimeout(() => {
            // Chercher bouton d'envoi
            const sendSelectors = [
                'button[type="submit"]',
                '.send-btn',
                '.submit-btn',
                'button:contains("Send")',
                'button:contains("Submit")',
                '[aria-label*="send"]'
            ];
            
            let sendBtn = null;
            for (const selector of sendSelectors) {
                sendBtn = document.querySelector(selector);
                if (sendBtn) break;
            }
            
            if (sendBtn) {
                sendBtn.click();
                console.log('✅ Prompt envoyé via bouton');
            } else {
                // Essayer Enter
                const event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true
                });
                input.dispatchEvent(event);
                console.log('✅ Prompt envoyé via Enter');
            }
        }, 200);
        
    } else {
        console.log('❌ Zone de saisie non trouvée');
        alert('❌ Impossible de trouver la zone de saisie');
    }
}

function creerPanneauJarvis() {
    // Si ton interface n'est pas détectée, créer un panneau de base
    ajouterStylesAmeliores();
    ajouterPanneauCognitif();
    ameliorerFonctionnalites();
    console.log('✅ Panneau JARVIS créé');
}

// 🚀 DÉMARRAGE
attendreInterface();
