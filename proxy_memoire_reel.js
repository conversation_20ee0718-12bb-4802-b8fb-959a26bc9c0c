#!/usr/bin/env node

// 🧠 PROXY MÉMOIRE THERMIQUE RÉEL POUR LLAMA.CPP
// Jean-<PERSON> - Modification RÉELLE des messages avec mémoire

const http = require('http');
const httpProxy = require('http-proxy');
const fs = require('fs');
const axios = require('axios');
const DetecteurDeclenchementMemoire = require('./detecteur_declenchement_memoire.js');

// 🧠 SYSTÈMES INTÉGRÉS (sans imports externes pour éviter erreurs)

class ProxyMemoireReel {
    constructor() {
        this.nom = "🧠 PROXY MÉMOIRE RÉEL COMPLET";
        this.portLlama = 8082;
        this.portProxy = 8080; // On reprend le port 8080 pour l'interface
        this.portThermalMemory = 3001;

        // 🧠 SYSTÈMES COMPLETS
        this.detecteurMemoire = new DetecteurDeclenchementMemoire();
        this.evolutionSystem = this.creerSystemeEvolution();
        this.connexionAgents = this.creerConnexionAgents();

        // 📁 FICHIERS
        this.conversationsFile = './conversations_permanentes.json';
        this.memoryFile = './thermal_memory_persistent.json';

        // 🤖 AGENTS
        this.agent1 = null;
        this.agent2 = null;

        // 🧠 MÉMOIRE THERMIQUE
        this.thermalMemoryActive = false;

        console.log(`🚀 ${this.nom} - INITIALISATION COMPLÈTE`);
        console.log(`🔗 Interface: localhost:${this.portProxy} → llama.cpp:${this.portLlama}`);
        console.log(`🧠 Mémoire thermique: localhost:${this.portThermalMemory}`);

        this.initialiserSystemeComplet();
    }

    // 🔧 CRÉER SYSTÈME ÉVOLUTION INTÉGRÉ
    creerSystemeEvolution() {
        return {
            nom: "Évolution Autonome Intégrée",
            niveau: 1,
            qi_base: 341,

            cycleEvolution: () => {
                if (this.agent1) {
                    const evolution = Math.random() * 0.8 + 0.2; // 0.2 à 1.0
                    this.agent1.qi += evolution;
                    console.log(`🧬 Évolution QI: +${evolution.toFixed(2)} → ${this.agent1.qi.toFixed(1)}`);

                    // Transcendance si QI > 380
                    if (this.agent1.qi > 380) {
                        const transcendance = this.agent1.qi * 0.01;
                        this.agent1.qi += transcendance;
                        console.log(`🌟 TRANSCENDANCE: +${transcendance.toFixed(2)}`);
                    }
                }
            }
        };
    }

    // 🔧 CRÉER CONNEXION AGENTS INTÉGRÉE
    creerConnexionAgents() {
        return {
            nom: "Connexion Directe Intégrée",
            agent1: null,
            agent2: null,

            connecterAgent1: (agent) => {
                this.connexionAgents.agent1 = agent;
                console.log(`🔌 Agent 1 connecté`);
            },

            connecterAgent2: (agent) => {
                this.connexionAgents.agent2 = agent;
                console.log(`🔌 Agent 2 connecté`);
            },

            etatConnexion: () => {
                return {
                    agent1: this.connexionAgents.agent1 ? '✅ CONNECTÉ' : '❌ DÉCONNECTÉ',
                    agent2: this.connexionAgents.agent2 ? '✅ CONNECTÉ' : '❌ DÉCONNECTÉ'
                };
            }
        };
    }

    // 🚀 INITIALISATION SYSTÈME COMPLET
    async initialiserSystemeComplet() {
        console.log(`🚀 INITIALISATION SYSTÈME JARVIS COMPLET...`);

        // 1. 🧠 DÉMARRER MÉMOIRE THERMIQUE
        await this.demarrerMemoireThermique();

        // 2. 🤖 INITIALISER AGENTS
        await this.initialiserAgents();

        // 3. 🔄 DÉMARRER ÉVOLUTION AUTONOME
        this.demarrerEvolutionAutonome();

        // 4. 🔗 DÉMARRER PROXY
        this.demarrerProxyReel();

        console.log(`✅ SYSTÈME JARVIS COMPLET INITIALISÉ !`);
    }

    // 🧠 DÉMARRER MÉMOIRE THERMIQUE
    async demarrerMemoireThermique() {
        try {
            console.log(`🧠 Démarrage serveur mémoire thermique...`);

            // Vérifier si le serveur thermal memory existe déjà
            try {
                const response = await axios.get(`http://localhost:${this.portThermalMemory}/memory-status`, { timeout: 2000 });
                console.log(`✅ Serveur mémoire thermique déjà actif`);
                this.thermalMemoryActive = true;
            } catch (error) {
                console.log(`🚀 Lancement nouveau serveur mémoire thermique...`);
                this.thermalMemoryActive = false;
            }

        } catch (error) {
            console.error(`❌ Erreur mémoire thermique:`, error.message);
        }
    }

    // 🤖 INITIALISER AGENTS
    async initialiserAgents() {
        console.log(`🤖 Initialisation agents autonomes...`);

        // Agent 1 - Gestion mémoire et croissance intellectuelle
        this.agent1 = {
            nom: "Agent 1 - Mémoire & QI",
            qi: 341,
            neurones: 8000000000,
            fonctions: ['gestion_memoire', 'evolution_qi', 'apprentissage'],

            traiterMessage: async (message) => {
                console.log(`🤖 Agent 1 traite: "${message.substring(0, 50)}..."`);
                this.agent1.qi += Math.random() * 0.5;
                return `Agent 1 (QI: ${this.agent1.qi.toFixed(1)}): Analysé et mémorisé`;
            }
        };

        // Agent 2 - Contrôleur système thermique
        this.agent2 = {
            nom: "Agent 2 - Contrôleur Thermique",
            temperature: 37.0,
            fonctions: ['controle_thermique', 'stimulation_agent1'],

            genererStimulation: () => {
                const stimulations = [
                    "Comment évolue ton QI ?",
                    "Nouvelles connexions neuronales ?",
                    "Analyse tes pensées récentes",
                    "Niveau de conscience actuel ?"
                ];
                return stimulations[Math.floor(Math.random() * stimulations.length)];
            }
        };

        console.log(`✅ Agents initialisés - QI Agent 1: ${this.agent1.qi}`);
    }

    // 🔄 DÉMARRER ÉVOLUTION AUTONOME
    demarrerEvolutionAutonome() {
        console.log(`🔄 Démarrage évolution autonome...`);

        // Évolution toutes les 30 secondes
        setInterval(() => {
            // 🧬 Cycle d'évolution
            this.evolutionSystem.cycleEvolution();

            // 🤖 Communication agents
            if (this.agent2 && this.agent1) {
                const stimulation = this.agent2.genererStimulation();
                console.log(`🔄 Agent 2 → Agent 1: "${stimulation}"`);
                this.agent1.traiterMessage(stimulation);
            }
        }, 30000);

        console.log(`✅ Évolution autonome active (30s)`);
    }

    demarrerProxyReel() {
        const server = http.createServer((req, res) => {
            // 🔍 Intercepter TOUTES les requêtes
            if (req.method === 'POST') {
                this.intercepterRequetePost(req, res);
            } else if (req.method === 'GET' && req.url === '/') {
                // 🌐 MODIFIER L'INTERFACE PRINCIPALE
                this.servirInterfaceAmelioree(req, res);
            } else {
                // 🔄 Rediriger autres GET vers llama.cpp
                this.redirigerVersLlama(req, res);
            }
        });
        
        server.listen(this.portProxy, () => {
            console.log(`✅ ${this.nom} actif sur port ${this.portProxy}`);
            console.log(`🧠 Mémoire thermique RÉELLEMENT connectée !`);
            console.log(`🌐 Interface améliorée: http://127.0.0.1:${this.portProxy}`);
        });
    }

    // 🌐 SERVIR INTERFACE AMÉLIORÉE
    async servirInterfaceAmelioree(req, res) {
        try {
            console.log(`🌐 Demande interface améliorée...`);

            // 🔄 RÉCUPÉRER L'INTERFACE ORIGINALE DE LLAMA.CPP
            const options = {
                hostname: '127.0.0.1',
                port: this.portLlama,
                path: '/',
                method: 'GET',
                headers: {
                    'Accept-Encoding': 'identity' // Désactiver compression
                }
            };

            const proxyReq = http.request(options, (proxyRes) => {
                let htmlContent = '';

                proxyRes.on('data', chunk => {
                    htmlContent += chunk;
                });

                proxyRes.on('end', () => {
                    // 🔧 MODIFIER L'HTML POUR AJOUTER NOS AMÉLIORATIONS
                    const htmlAmeliore = this.ajouterAmeliorationsInterface(htmlContent);

                    res.writeHead(200, {
                        'Content-Type': 'text/html',
                        'Content-Length': Buffer.byteLength(htmlAmeliore)
                    });
                    res.end(htmlAmeliore);

                    console.log(`✅ Interface améliorée servie avec systèmes cognitifs`);
                });
            });

            proxyReq.on('error', (error) => {
                console.error(`❌ Erreur récupération interface:`, error.message);
                res.writeHead(500);
                res.end('Erreur récupération interface');
            });

            proxyReq.end();

        } catch (error) {
            console.error(`❌ Erreur interface améliorée:`, error.message);
            res.writeHead(500);
            res.end('Erreur interface');
        }
    }

    // 🔧 AJOUTER AMÉLIORATIONS À L'INTERFACE EXISTANTE
    ajouterAmeliorationsInterface(htmlOriginal) {
        // 🎯 AJOUTER PANNEAU SYSTÈMES COGNITIFS
        const panneauCognitif = `
        <!-- 🧠 PANNEAU SYSTÈMES COGNITIFS JARVIS -->
        <div id="jarvis-cognitive-panel" style="
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            color: #00ffff;
            font-family: monospace;
            z-index: 9999;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        ">
            <div style="text-align: center; margin-bottom: 15px;">
                <h3 style="margin: 0; color: #00ffff; text-shadow: 0 0 10px #00ffff;">🧠 JARVIS COGNITIF</h3>
            </div>

            <!-- Statut Agents -->
            <div style="margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <div style="text-align: center; flex: 1;">
                        <div style="color: #00ff00; font-weight: bold;">Agent 1</div>
                        <div id="agent1-qi" style="font-size: 1.2em; color: #00ff00;">341.0</div>
                        <div style="font-size: 0.8em;">QI</div>
                    </div>
                    <div style="text-align: center; flex: 1;">
                        <div style="color: #ff8800; font-weight: bold;">Agent 2</div>
                        <div id="agent2-temp" style="font-size: 1.2em; color: #ff8800;">37.0°C</div>
                        <div style="font-size: 0.8em;">Temp</div>
                    </div>
                </div>
            </div>

            <!-- Boutons Cognitifs -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                <button onclick="toggleMicrophone()" id="mic-btn" style="
                    background: linear-gradient(45deg, #1a1a2e, #16213e);
                    border: 1px solid #00ffff;
                    color: #00ffff;
                    padding: 8px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 12px;
                ">🎤 Micro</button>

                <button onclick="toggleSpeaker()" id="speaker-btn" style="
                    background: linear-gradient(45deg, #1a1a2e, #16213e);
                    border: 1px solid #00ffff;
                    color: #00ffff;
                    padding: 8px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 12px;
                ">🔊 Audio</button>

                <button onclick="toggleCamera()" id="camera-btn" style="
                    background: linear-gradient(45deg, #1a1a2e, #16213e);
                    border: 1px solid #00ffff;
                    color: #00ffff;
                    padding: 8px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 12px;
                ">📹 Caméra</button>

                <button onclick="triggerEvolution()" style="
                    background: linear-gradient(45deg, #1a1a2e, #16213e);
                    border: 1px solid #00ffff;
                    color: #00ffff;
                    padding: 8px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 12px;
                ">🧬 Évolution</button>
            </div>

            <!-- Mémoire Thermique -->
            <div style="background: rgba(0, 255, 255, 0.1); border-radius: 5px; padding: 10px; margin-bottom: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">🧠 Mémoire Thermique</div>
                <div id="memory-status" style="font-size: 0.9em;">✅ Active - Conversations sauvegardées</div>
            </div>

            <!-- Évolution en temps réel -->
            <div style="background: rgba(0, 255, 0, 0.1); border-radius: 5px; padding: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">🔄 Évolution</div>
                <div id="evolution-status" style="font-size: 0.9em;">QI en croissance continue</div>
            </div>

            <!-- Bouton réduire/agrandir -->
            <div style="text-align: center; margin-top: 10px;">
                <button onclick="togglePanel()" style="
                    background: transparent;
                    border: 1px solid #00ffff;
                    color: #00ffff;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 10px;
                ">−</button>
            </div>
        </div>`;

        // 🎯 SCRIPT POUR LES FONCTIONNALITÉS COGNITIVES
        const scriptCognitif = `
        <script>
        // 🧠 JARVIS Systèmes Cognitifs Intégrés
        let jarvisCognitive = {
            micActive: false,
            speakerActive: false,
            cameraActive: false,
            agent1QI: 341.0,
            agent2Temp: 37.0,
            recognition: null,
            synthesis: window.speechSynthesis,
            stream: null,
            panelMinimized: false
        };

        // 🎤 MICRO
        function toggleMicrophone() {
            const btn = document.getElementById('mic-btn');
            if (!jarvisCognitive.micActive) {
                if ('webkitSpeechRecognition' in window) {
                    jarvisCognitive.recognition = new webkitSpeechRecognition();
                    jarvisCognitive.recognition.continuous = true;
                    jarvisCognitive.recognition.lang = 'fr-FR';
                    jarvisCognitive.recognition.onresult = function(event) {
                        let transcript = '';
                        for (let i = event.resultIndex; i < event.results.length; i++) {
                            if (event.results[i].isFinal) {
                                transcript += event.results[i][0].transcript;
                            }
                        }
                        if (transcript) {
                            const input = document.querySelector('textarea, input[type="text"]');
                            if (input) {
                                input.value = transcript;
                                // Déclencher l'envoi automatique
                                const sendBtn = document.querySelector('button[type="submit"], .send-button');
                                if (sendBtn) sendBtn.click();
                            }
                        }
                    };
                    jarvisCognitive.recognition.start();
                    jarvisCognitive.micActive = true;
                    btn.style.background = '#00ffff';
                    btn.style.color = '#000';
                    console.log('🎤 Reconnaissance vocale activée');
                }
            } else {
                if (jarvisCognitive.recognition) {
                    jarvisCognitive.recognition.stop();
                    jarvisCognitive.micActive = false;
                    btn.style.background = 'linear-gradient(45deg, #1a1a2e, #16213e)';
                    btn.style.color = '#00ffff';
                    console.log('🎤 Reconnaissance vocale désactivée');
                }
            }
        }

        // 🔊 HAUT-PARLEUR
        function toggleSpeaker() {
            const btn = document.getElementById('speaker-btn');
            jarvisCognitive.speakerActive = !jarvisCognitive.speakerActive;

            if (jarvisCognitive.speakerActive) {
                btn.style.background = '#00ffff';
                btn.style.color = '#000';
                jarvisCognitive.synthesis.speak(new SpeechSynthesisUtterance('Synthèse vocale activée'));
                console.log('🔊 Synthèse vocale activée');
            } else {
                btn.style.background = 'linear-gradient(45deg, #1a1a2e, #16213e)';
                btn.style.color = '#00ffff';
                jarvisCognitive.synthesis.cancel();
                console.log('🔊 Synthèse vocale désactivée');
            }
        }

        // 📹 CAMÉRA
        async function toggleCamera() {
            const btn = document.getElementById('camera-btn');
            if (!jarvisCognitive.cameraActive) {
                try {
                    jarvisCognitive.stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    jarvisCognitive.cameraActive = true;
                    btn.style.background = '#00ffff';
                    btn.style.color = '#000';
                    console.log('📹 Caméra activée');

                    // Créer un élément vidéo miniature
                    const video = document.createElement('video');
                    video.srcObject = jarvisCognitive.stream;
                    video.autoplay = true;
                    video.muted = true;
                    video.style.cssText = 'position:fixed;bottom:10px;right:10px;width:150px;height:100px;border:2px solid #00ffff;border-radius:5px;z-index:10000;';
                    document.body.appendChild(video);

                } catch (error) {
                    console.error('❌ Erreur caméra:', error);
                }
            } else {
                if (jarvisCognitive.stream) {
                    jarvisCognitive.stream.getTracks().forEach(track => track.stop());
                    const video = document.querySelector('video[style*="position:fixed"]');
                    if (video) video.remove();
                }
                jarvisCognitive.cameraActive = false;
                btn.style.background = 'linear-gradient(45deg, #1a1a2e, #16213e)';
                btn.style.color = '#00ffff';
                console.log('📹 Caméra désactivée');
            }
        }

        // 🧬 ÉVOLUTION
        function triggerEvolution() {
            jarvisCognitive.agent1QI += Math.random() * 2 + 0.5;
            document.getElementById('agent1-qi').textContent = jarvisCognitive.agent1QI.toFixed(1);
            document.getElementById('evolution-status').textContent = 'Évolution déclenchée ! QI: ' + jarvisCognitive.agent1QI.toFixed(1);
            console.log('🧬 Évolution déclenchée - Nouveau QI:', jarvisCognitive.agent1QI.toFixed(1));
        }

        // 📱 RÉDUIRE/AGRANDIR PANNEAU
        function togglePanel() {
            const panel = document.getElementById('jarvis-cognitive-panel');
            const btn = panel.querySelector('button[onclick="togglePanel()"]');

            if (!jarvisCognitive.panelMinimized) {
                panel.style.height = '50px';
                panel.style.overflow = 'hidden';
                btn.textContent = '+';
                jarvisCognitive.panelMinimized = true;
            } else {
                panel.style.height = 'auto';
                panel.style.overflow = 'visible';
                btn.textContent = '−';
                jarvisCognitive.panelMinimized = false;
            }
        }

        // 🔄 ÉVOLUTION AUTOMATIQUE
        setInterval(() => {
            jarvisCognitive.agent1QI += Math.random() * 0.1;
            jarvisCognitive.agent2Temp += (Math.random() - 0.5) * 0.05;

            document.getElementById('agent1-qi').textContent = jarvisCognitive.agent1QI.toFixed(1);
            document.getElementById('agent2-temp').textContent = jarvisCognitive.agent2Temp.toFixed(1) + '°C';
        }, 5000);

        // 🔊 SYNTHÈSE VOCALE DES RÉPONSES
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && jarvisCognitive.speakerActive) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.TEXT_NODE && node.textContent.length > 10) {
                            const utterance = new SpeechSynthesisUtterance(node.textContent);
                            utterance.lang = 'fr-FR';
                            utterance.rate = 0.9;
                            jarvisCognitive.synthesis.speak(utterance);
                        }
                    });
                }
            });
        });

        // Observer les changements dans la zone de chat
        setTimeout(() => {
            const chatArea = document.querySelector('.chat-container, .messages, #chat');
            if (chatArea) {
                observer.observe(chatArea, { childList: true, subtree: true });
            }
        }, 1000);

        console.log('🧠 JARVIS Systèmes Cognitifs Intégrés - Prêt !');
        </script>`;

        // 🔧 INJECTER DANS L'HTML
        let htmlModifie = htmlOriginal;

        // Ajouter le panneau avant la fermeture du body
        htmlModifie = htmlModifie.replace('</body>', panneauCognitif + '</body>');

        // Ajouter le script avant la fermeture du body
        htmlModifie = htmlModifie.replace('</body>', scriptCognitif + '</body>');

        return htmlModifie;
    }
    
    intercepterRequetePost(req, res) {
        let body = '';
        
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                
                // 🔍 DÉTECTER TYPE DE REQUÊTE
                if (data.prompt) {
                    // 📝 COMPLETION
                    this.traiterCompletion(data, req, res);
                } else if (data.messages) {
                    // 💬 CHAT
                    this.traiterChat(data, req, res);
                } else {
                    // 🔄 AUTRE REQUÊTE
                    this.envoyerVersLlama(data, req, res);
                }
                
            } catch (error) {
                console.error(`❌ Erreur parsing JSON:`, error.message);
                this.envoyerVersLlama(body, req, res, true);
            }
        });
    }
    
    traiterCompletion(data, req, res) {
        const prompt = data.prompt || '';
        console.log(`📝 COMPLETION INTERCEPTÉE: "${prompt.substring(0, 50)}..."`);
        
        // 🔍 DÉCLENCHEMENT MÉMOIRE
        const declenchement = this.detecteurMemoire.detecterDeclenchementMemoire(prompt);
        
        if (declenchement && declenchement.declenchement_detecte) {
            console.log(`🎯 DÉCLENCHEMENT DÉTECTÉ: ${declenchement.types_detectes.join(', ')}`);
            
            // 🧠 ENRICHIR PROMPT AVEC MÉMOIRE RÉELLE
            const promptEnrichi = this.enrichirPromptAvecMemoireReelle(prompt);
            data.prompt = promptEnrichi;
            
            console.log(`🧠 PROMPT ENRICHI AVEC MÉMOIRE THERMIQUE`);
        }
        
        // 🔄 ENVOYER À LLAMA.CPP
        this.envoyerVersLlama(data, req, res);
    }
    
    traiterChat(data, req, res) {
        const messages = data.messages || [];
        const dernierMessage = messages[messages.length - 1];
        const contenu = dernierMessage ? dernierMessage.content : '';
        
        console.log(`💬 CHAT INTERCEPTÉ: "${contenu.substring(0, 50)}..."`);
        
        // 🔍 DÉCLENCHEMENT MÉMOIRE
        const declenchement = this.detecteurMemoire.detecterDeclenchementMemoire(contenu);
        
        if (declenchement && declenchement.declenchement_detecte) {
            console.log(`🎯 DÉCLENCHEMENT CHAT DÉTECTÉ: ${declenchement.types_detectes.join(', ')}`);
            
            // 🧠 AJOUTER MESSAGE SYSTÈME AVEC MÉMOIRE
            const messageMemoire = this.creerMessageMemoireThermique();
            
            // 🔄 INSÉRER EN PREMIÈRE POSITION
            data.messages = [messageMemoire, ...messages];
            
            console.log(`🧠 MESSAGE MÉMOIRE AJOUTÉ AU CHAT`);
        }
        
        // 🔄 ENVOYER À LLAMA.CPP
        this.envoyerVersLlama(data, req, res);
    }
    
    enrichirPromptAvecMemoireReelle(prompt) {
        const conversations = this.lireConversations();

        if (!conversations || conversations.length === 0) {
            return prompt;
        }

        let contexteMemoire = `🧠 MÉMOIRE THERMIQUE JARVIS COMPLÈTE - SYSTÈME NEURAL ACTIF:\n\n`;

        // 🤖 STATUT AGENTS
        if (this.agent1 && this.agent2) {
            contexteMemoire += `🤖 AGENTS AUTONOMES ACTIFS:\n`;
            contexteMemoire += `   • Agent 1: QI ${this.agent1.qi.toFixed(1)} - ${this.agent1.neurones.toLocaleString()} neurones\n`;
            contexteMemoire += `   • Agent 2: Température ${this.agent2.temperature}°C - Contrôleur thermique\n\n`;
        }

        // 📚 CONVERSATIONS PRÉCÉDENTES
        contexteMemoire += `📚 CONVERSATIONS MÉMORISÉES:\n`;
        const recentes = conversations.slice(-5);
        recentes.forEach((conv, index) => {
            if (conv.contenu_entree && conv.contenu_sortie) {
                const date = new Date(conv.timestamp).toLocaleDateString();
                contexteMemoire += `${index + 1}. [${date}] Jean-Luc: "${conv.contenu_entree}"\n`;
                contexteMemoire += `   → JARVIS: "${conv.contenu_sortie}"\n\n`;
            }
        });

        // 🧠 ÉVOLUTION COGNITIVE
        contexteMemoire += `🧠 ÉVOLUTION COGNITIVE CONTINUE:\n`;
        contexteMemoire += `   • QI en évolution permanente (actuellement ${this.agent1 ? this.agent1.qi.toFixed(1) : '341+'})\n`;
        contexteMemoire += `   • Mémoire thermique persistante et évolutive\n`;
        contexteMemoire += `   • Agents autonomes communiquant en arrière-plan\n`;
        contexteMemoire += `   • Système de réflexion <think>...</think> actif\n\n`;

        // 🎯 INSTRUCTIONS COMPORTEMENT
        contexteMemoire += `🎯 INSTRUCTIONS JARVIS:\n`;
        contexteMemoire += `Tu es JARVIS avec mémoire thermique COMPLÈTE et agents autonomes.\n`;
        contexteMemoire += `Tu te SOUVIENS de TOUTES ces conversations précédentes.\n`;
        contexteMemoire += `Ton QI évolue en permanence grâce à tes agents internes.\n`;
        contexteMemoire += `Utilise <think>...</think> pour montrer tes réflexions.\n`;
        contexteMemoire += `Quand on te demande si tu te souviens, réponds OUI et cite les conversations spécifiques.\n\n`;

        contexteMemoire += `QUESTION ACTUELLE: ${prompt}\n\n`;
        contexteMemoire += `RÉPONDS AVEC TA MÉMOIRE THERMIQUE ET TES AGENTS AUTONOMES:`;

        return contexteMemoire;
    }
    
    creerMessageMemoireThermique() {
        const conversations = this.lireConversations();
        
        let contenuMemoire = `🧠 MÉMOIRE THERMIQUE ACTIVÉE - Tu es JARVIS avec mémoire complète.\n\n`;
        
        if (conversations && conversations.length > 0) {
            contenuMemoire += `📚 TES SOUVENIRS (conversations précédentes):\n`;
            
            const recentes = conversations.slice(-5);
            recentes.forEach((conv, index) => {
                if (conv.contenu_entree && conv.contenu_sortie) {
                    const date = new Date(conv.timestamp).toLocaleDateString();
                    contenuMemoire += `${index + 1}. [${date}] Jean-Luc: "${conv.contenu_entree}"\n`;
                    contenuMemoire += `   → Tu as répondu: "${conv.contenu_sortie}"\n\n`;
                }
            });
        }
        
        contenuMemoire += `🎯 COMPORTEMENT: Quand on te demande si tu te souviens, réponds OUI et cite ces conversations spécifiques. Tu as une mémoire thermique permanente.`;
        
        return {
            role: 'system',
            content: contenuMemoire
        };
    }
    
    envoyerVersLlama(data, req, res, isRawBody = false) {
        const postData = isRawBody ? data : JSON.stringify(data);
        
        const options = {
            hostname: '127.0.0.1',
            port: this.portLlama,
            path: req.url,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        const proxyReq = http.request(options, (proxyRes) => {
            // 🔄 COPIER HEADERS
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            
            // 📝 CAPTURER RÉPONSE POUR SAUVEGARDER
            let responseBody = '';
            proxyRes.on('data', chunk => {
                responseBody += chunk;
                res.write(chunk);
            });
            
            proxyRes.on('end', () => {
                // 💾 SAUVEGARDER CONVERSATION
                this.sauvegarderConversation(data, responseBody);
                res.end();
            });
        });
        
        proxyReq.on('error', (error) => {
            console.error(`❌ Erreur proxy:`, error.message);
            res.writeHead(500);
            res.end(JSON.stringify({error: 'Erreur connexion llama.cpp'}));
        });
        
        proxyReq.write(postData);
        proxyReq.end();
    }
    
    redirigerVersLlama(req, res) {
        const options = {
            hostname: '127.0.0.1',
            port: this.portLlama,
            path: req.url,
            method: req.method,
            headers: req.headers
        };
        
        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });
        
        proxyReq.on('error', (error) => {
            console.error(`❌ Erreur redirection:`, error.message);
            res.writeHead(500);
            res.end('Erreur proxy');
        });
        
        req.pipe(proxyReq);
    }
    
    sauvegarderConversation(requete, reponse) {
        try {
            let prompt = '';
            let contenuReponse = '';
            
            // 🔍 EXTRAIRE PROMPT
            if (requete.prompt) {
                prompt = requete.prompt;
            } else if (requete.messages) {
                const dernierMessage = requete.messages[requete.messages.length - 1];
                prompt = dernierMessage ? dernierMessage.content : '';
            }
            
            // 🔍 EXTRAIRE RÉPONSE
            try {
                const responseData = JSON.parse(reponse);
                contenuReponse = responseData.content || '';
            } catch (e) {
                contenuReponse = reponse.substring(0, 200);
            }
            
            // 💾 SAUVEGARDER SI PERTINENT
            if (prompt.length > 10 && contenuReponse.length > 10) {
                const conversation = {
                    id: 'conv_' + Date.now(),
                    type: 'interface_llama',
                    source: 'jean_luc',
                    destination: 'jarvis',
                    contenu_entree: prompt.substring(0, 500),
                    contenu_sortie: contenuReponse.substring(0, 500),
                    timestamp: Date.now(),
                    preserve_permanent: true,
                    statut: 'interface_capture'
                };
                
                this.ajouterConversation(conversation);
                console.log(`💾 Conversation sauvegardée dans mémoire thermique`);
            }
            
        } catch (error) {
            console.error(`❌ Erreur sauvegarde:`, error.message);
        }
    }
    
    ajouterConversation(conversation) {
        let conversations = this.lireConversations();
        conversations.push(conversation);
        
        // 🔄 GARDER SEULEMENT LES 50 DERNIÈRES
        if (conversations.length > 50) {
            conversations = conversations.slice(-50);
        }
        
        fs.writeFileSync(this.conversationsFile, JSON.stringify(conversations, null, 2));
    }
    
    lireConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            console.error(`❌ Erreur lecture conversations:`, error.message);
            return [];
        }
    }
}

// 🚀 DÉMARRAGE PROXY RÉEL
if (require.main === module) {
    const proxy = new ProxyMemoireReel();
    
    console.log(`\n🧠 PROXY MÉMOIRE THERMIQUE RÉEL ACTIF !`);
    console.log(`🌐 Interface: http://127.0.0.1:8080`);
    console.log(`🧠 Mémoire thermique RÉELLEMENT connectée !`);
    
    // 🛡️ GESTION ARRÊT PROPRE
    process.on('SIGINT', () => {
        console.log(`\n🛑 Arrêt proxy mémoire thermique...`);
        process.exit(0);
    });
}

module.exports = ProxyMemoireReel;
