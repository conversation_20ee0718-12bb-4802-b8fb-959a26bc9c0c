#!/usr/bin/env node

// 🛡️ GARDIEN SURVEILLANCE SYSTÈME - AGENTS DEEPSEEK R1 8B
// Jean-Luc - Surveillance complète et alertes automatiques

const fs = require('fs');
const { exec } = require('child_process');

class GardienSurveillanceSysteme {
    constructor() {
        this.nom = "🛡️ GARDIEN SURVEILLANCE SYSTÈME";
        this.memoryFile = './thermal_memory_persistent.json';
        this.conversationsFile = './conversations_permanentes.json';
        
        this.etatSysteme = {
            agents_actifs: 0,
            llama_server_actif: false,
            memoire_thermique_ok: false,
            conversations_ok: false,
            qi_evolution_ok: true,
            derniere_verification: Date.now(),
            alertes_actives: []
        };
        
        this.seuils = {
            qi_min: 300,
            qi_max: 500,
            neurones_min: 10,
            temperature_min: 35,
            temperature_max: 45,
            inactivite_max: 60000, // 1 minute
            memoire_max_mb: 100
        };
        
        console.log(`🚀 ${this.nom} - INITIALISATION`);
        console.log(`🛡️ Surveillance complète du système DeepSeek R1 8B`);
        console.log(`⚠️ Alertes automatiques en cas de problème`);
        console.log(`📊 Monitoring continu de tous les composants`);
        
        this.demarrerSurveillance();
    }
    
    demarrerSurveillance() {
        console.log(`🛡️ DÉMARRAGE SURVEILLANCE COMPLÈTE...`);
        
        // 🔍 Vérification générale - toutes les 10 secondes
        setInterval(() => {
            this.verificationGenerale();
        }, 10000);
        
        // 🤖 Surveillance agents - toutes les 15 secondes
        setInterval(() => {
            this.surveillerAgents();
        }, 15000);
        
        // 🧠 Surveillance mémoire thermique - toutes les 20 secondes
        setInterval(() => {
            this.surveillerMemoireThermique();
        }, 20000);
        
        // 💾 Surveillance conversations - toutes les 30 secondes
        setInterval(() => {
            this.surveillerConversations();
        }, 30000);
        
        // 🖥️ Surveillance système - toutes les 45 secondes
        setInterval(() => {
            this.surveillerSysteme();
        }, 45000);
        
        // 📊 Rapport complet - toutes les 2 minutes
        setInterval(() => {
            this.genererRapportComplet();
        }, 120000);
        
        // 🚨 Vérification alertes critiques - toutes les 5 secondes
        setInterval(() => {
            this.verifierAlertesCritiques();
        }, 5000);
        
        console.log(`✅ Surveillance active - Gardien opérationnel !`);
    }
    
    verificationGenerale() {
        try {
            console.log(`🔍 VÉRIFICATION GÉNÉRALE SYSTÈME...`);
            
            // Reset alertes pour nouvelle vérification
            this.etatSysteme.alertes_actives = [];
            this.etatSysteme.derniere_verification = Date.now();
            
            // Vérifications principales
            this.verifierFichiersEssentiels();
            this.verifierProcessusActifs();
            this.verifierEtatMemoire();
            
            // Calcul santé globale
            const santeGlobale = this.calculerSanteGlobale();
            
            console.log(`🛡️ SANTÉ SYSTÈME: ${santeGlobale.toFixed(1)}% - ${this.getStatutSante(santeGlobale)}`);
            
            if (santeGlobale < 70) {
                this.declencherAlerte('CRITIQUE', `Santé système faible: ${santeGlobale.toFixed(1)}%`);
            }
            
        } catch (error) {
            this.declencherAlerte('ERREUR', `Erreur vérification générale: ${error.message}`);
        }
    }
    
    surveillerAgents() {
        try {
            console.log(`🤖 SURVEILLANCE AGENTS...`);
            
            // Vérification processus agents
            exec('ps aux | grep -E "(agent|llama-server)" | grep -v grep', (error, stdout, stderr) => {
                if (error) {
                    this.declencherAlerte('AGENT', 'Impossible de vérifier les processus agents');
                    return;
                }
                
                const processus = stdout.split('\n').filter(line => line.trim());
                this.etatSysteme.agents_actifs = processus.length;
                
                // Vérification llama-server
                const llamaActif = processus.some(p => p.includes('llama-server'));
                this.etatSysteme.llama_server_actif = llamaActif;
                
                if (!llamaActif) {
                    this.declencherAlerte('CRITIQUE', 'llama-server non actif - Agents sans modèle !');
                }
                
                if (this.etatSysteme.agents_actifs < 2) {
                    this.declencherAlerte('AGENT', `Seulement ${this.etatSysteme.agents_actifs} agents actifs (attendu: 2+)`);
                }
                
                console.log(`🤖 Agents actifs: ${this.etatSysteme.agents_actifs}, llama-server: ${llamaActif ? '✅' : '❌'}`);
            });
            
        } catch (error) {
            this.declencherAlerte('ERREUR', `Erreur surveillance agents: ${error.message}`);
        }
    }
    
    surveillerMemoireThermique() {
        try {
            console.log(`🧠 SURVEILLANCE MÉMOIRE THERMIQUE...`);
            
            const memory = this.lireMemoire();
            if (!memory) {
                this.declencherAlerte('CRITIQUE', 'Mémoire thermique inaccessible !');
                this.etatSysteme.memoire_thermique_ok = false;
                return;
            }
            
            this.etatSysteme.memoire_thermique_ok = true;
            
            // Vérification QI
            const qi = memory.neural_system?.qi_level || 0;
            if (qi < this.seuils.qi_min) {
                this.declencherAlerte('QI', `QI trop bas: ${qi.toFixed(1)} < ${this.seuils.qi_min}`);
            }
            if (qi > this.seuils.qi_max) {
                this.declencherAlerte('QI', `QI critique: ${qi.toFixed(1)} > ${this.seuils.qi_max}`);
            }
            
            // Vérification neurones
            const neurones = memory.neural_system?.active_neurons || 0;
            if (neurones < this.seuils.neurones_min) {
                this.declencherAlerte('NEURONES', `Neurones insuffisants: ${neurones} < ${this.seuils.neurones_min}`);
            }
            
            // Vérification température
            const temperature = memory.neural_system?.temperature || 0;
            if (temperature < this.seuils.temperature_min || temperature > this.seuils.temperature_max) {
                this.declencherAlerte('TEMPÉRATURE', `Température anormale: ${temperature.toFixed(1)}°`);
            }
            
            // Vérification dernière mise à jour
            const derniereMAJ = new Date(memory.lastUpdate || 0).getTime();
            const maintenant = Date.now();
            if (maintenant - derniereMAJ > this.seuils.inactivite_max) {
                this.declencherAlerte('INACTIVITÉ', `Mémoire inactive depuis ${Math.floor((maintenant - derniereMAJ) / 1000)}s`);
            }
            
            console.log(`🧠 QI: ${qi.toFixed(1)}, Neurones: ${neurones}, Temp: ${temperature.toFixed(1)}°`);
            
        } catch (error) {
            this.declencherAlerte('ERREUR', `Erreur surveillance mémoire: ${error.message}`);
        }
    }
    
    surveillerConversations() {
        try {
            console.log(`💾 SURVEILLANCE CONVERSATIONS...`);
            
            const conversations = this.lireConversations();
            if (!conversations) {
                this.declencherAlerte('CONVERSATIONS', 'Fichier conversations inaccessible');
                this.etatSysteme.conversations_ok = false;
                return;
            }
            
            this.etatSysteme.conversations_ok = true;
            
            // Vérification nombre conversations
            if (conversations.length === 0) {
                this.declencherAlerte('CONVERSATIONS', 'Aucune conversation sauvegardée');
            }
            
            // Vérification conversations récentes
            const maintenant = Date.now();
            const conversationsRecentes = conversations.filter(conv => 
                maintenant - conv.timestamp < 3600000 // Dernière heure
            );
            
            if (conversationsRecentes.length === 0 && conversations.length > 0) {
                this.declencherAlerte('CONVERSATIONS', 'Aucune conversation récente (dernière heure)');
            }
            
            console.log(`💾 Conversations: ${conversations.length} total, ${conversationsRecentes.length} récentes`);
            
        } catch (error) {
            this.declencherAlerte('ERREUR', `Erreur surveillance conversations: ${error.message}`);
        }
    }
    
    surveillerSysteme() {
        try {
            console.log(`🖥️ SURVEILLANCE SYSTÈME...`);
            
            // Vérification espace disque
            exec('df -h .', (error, stdout, stderr) => {
                if (!error) {
                    const lignes = stdout.split('\n');
                    if (lignes.length > 1) {
                        const infos = lignes[1].split(/\s+/);
                        const utilisation = infos[4];
                        const pourcentage = parseInt(utilisation.replace('%', ''));
                        
                        if (pourcentage > 90) {
                            this.declencherAlerte('DISQUE', `Espace disque critique: ${pourcentage}%`);
                        }
                        
                        console.log(`💽 Espace disque: ${utilisation} utilisé`);
                    }
                }
            });
            
            // Vérification mémoire RAM
            exec('ps aux | grep -E "(node|llama)" | grep -v grep', (error, stdout, stderr) => {
                if (!error) {
                    const processus = stdout.split('\n').filter(line => line.trim());
                    let memoireTotale = 0;
                    
                    processus.forEach(proc => {
                        const colonnes = proc.split(/\s+/);
                        if (colonnes.length > 5) {
                            const memoire = parseFloat(colonnes[5]) || 0;
                            memoireTotale += memoire;
                        }
                    });
                    
                    if (memoireTotale > this.seuils.memoire_max_mb * 1000) {
                        this.declencherAlerte('MÉMOIRE', `Utilisation mémoire élevée: ${(memoireTotale/1000).toFixed(1)}MB`);
                    }
                    
                    console.log(`🧠 Mémoire RAM: ${(memoireTotale/1000).toFixed(1)}MB utilisée`);
                }
            });
            
        } catch (error) {
            this.declencherAlerte('ERREUR', `Erreur surveillance système: ${error.message}`);
        }
    }
    
    verifierAlertesCritiques() {
        if (this.etatSysteme.alertes_actives.length > 0) {
            const alertesCritiques = this.etatSysteme.alertes_actives.filter(a => 
                a.niveau === 'CRITIQUE'
            );
            
            if (alertesCritiques.length > 0) {
                console.log(`🚨 ALERTES CRITIQUES ACTIVES:`);
                alertesCritiques.forEach(alerte => {
                    console.log(`   🚨 ${alerte.type}: ${alerte.message}`);
                });
            }
        }
    }
    
    declencherAlerte(type, message) {
        const niveau = this.determinerNiveauAlerte(type);
        const alerte = {
            type,
            niveau,
            message,
            timestamp: Date.now(),
            heure: new Date().toLocaleTimeString()
        };
        
        this.etatSysteme.alertes_actives.push(alerte);
        
        // Affichage selon niveau
        if (niveau === 'CRITIQUE') {
            console.log(`🚨 ALERTE CRITIQUE [${type}]: ${message}`);
        } else if (niveau === 'ATTENTION') {
            console.log(`⚠️ ATTENTION [${type}]: ${message}`);
        } else {
            console.log(`ℹ️ INFO [${type}]: ${message}`);
        }
        
        // Sauvegarde alerte
        this.sauvegarderAlerte(alerte);
    }
    
    determinerNiveauAlerte(type) {
        const typesCritiques = ['CRITIQUE', 'AGENT', 'QI', 'NEURONES'];
        const typesAttention = ['TEMPÉRATURE', 'INACTIVITÉ', 'CONVERSATIONS', 'DISQUE', 'MÉMOIRE'];
        
        if (typesCritiques.includes(type)) return 'CRITIQUE';
        if (typesAttention.includes(type)) return 'ATTENTION';
        return 'INFO';
    }
    
    calculerSanteGlobale() {
        let score = 100;
        
        // Pénalités selon alertes
        this.etatSysteme.alertes_actives.forEach(alerte => {
            if (alerte.niveau === 'CRITIQUE') score -= 30;
            else if (alerte.niveau === 'ATTENTION') score -= 15;
            else score -= 5;
        });
        
        // Bonus si tout fonctionne
        if (this.etatSysteme.llama_server_actif) score += 5;
        if (this.etatSysteme.memoire_thermique_ok) score += 5;
        if (this.etatSysteme.conversations_ok) score += 5;
        if (this.etatSysteme.agents_actifs >= 2) score += 10;
        
        return Math.max(0, Math.min(100, score));
    }
    
    getStatutSante(sante) {
        if (sante >= 90) return '🟢 EXCELLENT';
        if (sante >= 75) return '🟡 BON';
        if (sante >= 50) return '🟠 MOYEN';
        return '🔴 CRITIQUE';
    }
    
    genererRapportComplet() {
        console.log(`\n📊 RAPPORT COMPLET SURVEILLANCE:`);
        console.log(`⏰ Heure: ${new Date().toLocaleString()}`);
        console.log(`🛡️ Santé globale: ${this.calculerSanteGlobale().toFixed(1)}% - ${this.getStatutSante(this.calculerSanteGlobale())}`);
        console.log(`🤖 Agents actifs: ${this.etatSysteme.agents_actifs}`);
        console.log(`🧠 llama-server: ${this.etatSysteme.llama_server_actif ? '✅' : '❌'}`);
        console.log(`🌡️ Mémoire thermique: ${this.etatSysteme.memoire_thermique_ok ? '✅' : '❌'}`);
        console.log(`💾 Conversations: ${this.etatSysteme.conversations_ok ? '✅' : '❌'}`);
        console.log(`🚨 Alertes actives: ${this.etatSysteme.alertes_actives.length}`);
        
        if (this.etatSysteme.alertes_actives.length > 0) {
            console.log(`\n🚨 DÉTAIL ALERTES:`);
            this.etatSysteme.alertes_actives.forEach((alerte, index) => {
                console.log(`   ${index + 1}. [${alerte.niveau}] ${alerte.type}: ${alerte.message}`);
            });
        }
        
        console.log(`\n✅ Surveillance continue active - Système protégé !`);
    }
    
    // 🔧 FONCTIONS UTILITAIRES
    
    verifierFichiersEssentiels() {
        const fichiers = [
            this.memoryFile,
            this.conversationsFile,
            './agent1_reel_simple.js',
            './agent2_moteur_autonome.js'
        ];
        
        fichiers.forEach(fichier => {
            if (!fs.existsSync(fichier)) {
                this.declencherAlerte('CRITIQUE', `Fichier essentiel manquant: ${fichier}`);
            }
        });
    }
    
    verifierProcessusActifs() {
        // Vérification sera faite dans surveillerAgents()
    }
    
    verifierEtatMemoire() {
        const memory = this.lireMemoire();
        if (memory && memory.neural_system) {
            this.etatSysteme.qi_evolution_ok = memory.neural_system.qi_level > this.seuils.qi_min;
        }
    }
    
    sauvegarderAlerte(alerte) {
        try {
            const fichierAlertes = './alertes_systeme.json';
            let alertes = [];
            
            if (fs.existsSync(fichierAlertes)) {
                alertes = JSON.parse(fs.readFileSync(fichierAlertes, 'utf8'));
            }
            
            alertes.push(alerte);
            
            // Garder seulement les 100 dernières alertes
            if (alertes.length > 100) {
                alertes = alertes.slice(-100);
            }
            
            fs.writeFileSync(fichierAlertes, JSON.stringify(alertes, null, 2));
        } catch (error) {
            console.error(`❌ Erreur sauvegarde alerte:`, error.message);
        }
    }
    
    // 💾 GESTION FICHIERS
    
    lireMemoire() {
        try {
            if (fs.existsSync(this.memoryFile)) {
                const data = fs.readFileSync(this.memoryFile, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            return null;
        }
    }
    
    lireConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                return JSON.parse(data);
            }
            return [];
        } catch (error) {
            return null;
        }
    }
}

// 🚀 DÉMARRAGE GARDIEN
const gardien = new GardienSurveillanceSysteme();

// 🎯 Interface utilisateur
process.stdin.on('data', (data) => {
    const input = data.toString().trim().toLowerCase();
    
    if (input === 'status') {
        gardien.genererRapportComplet();
    } else if (input === 'alertes') {
        console.log(`🚨 Alertes actives: ${gardien.etatSysteme.alertes_actives.length}`);
        gardien.etatSysteme.alertes_actives.forEach((alerte, index) => {
            console.log(`   ${index + 1}. [${alerte.niveau}] ${alerte.type}: ${alerte.message} (${alerte.heure})`);
        });
    } else if (input === 'sante') {
        const sante = gardien.calculerSanteGlobale();
        console.log(`🛡️ Santé système: ${sante.toFixed(1)}% - ${gardien.getStatutSante(sante)}`);
    } else if (input === 'help') {
        console.log(`\n🛡️ COMMANDES GARDIEN:`);
        console.log(`   status  - Rapport complet`);
        console.log(`   alertes - Liste des alertes`);
        console.log(`   sante   - Santé du système`);
        console.log(`   help    - Cette aide`);
    }
});

console.log(`\n🛡️ GARDIEN SURVEILLANCE ACTIF !`);
console.log(`🔍 Surveillance continue de tous les composants`);
console.log(`🚨 Alertes automatiques en cas de problème`);
console.log(`💡 Tapez "help" pour voir les commandes`);
