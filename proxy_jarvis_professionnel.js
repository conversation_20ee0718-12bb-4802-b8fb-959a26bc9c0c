const http = require('http');
const zlib = require('zlib');
const fs = require('fs');

class ProxyJarvisProfessionnel {
    constructor() {
        this.portProxy = 8083;  // Port libre pour notre interface
        this.portLlama = 8080;  // Port où llama.cpp fonctionne déjà
        this.nom = "🧠 JARVIS INTERFACE PROFESSIONNELLE";
        this.agentName = "JARVIS";
        this.erreurs = [];
    }

    demarrer() {
        console.log(`🚀 ${this.nom} - INITIALISATION`);
        
        const server = http.createServer((req, res) => {
            try {
                if (req.method === 'GET' && req.url === '/') {
                    this.servirInterfaceComplete(req, res);
                } else {
                    this.redirigerVersLlama(req, res);
                }
            } catch (error) {
                this.gererErreur(error, res);
            }
        });

        server.listen(this.portProxy, () => {
            console.log(`✅ ${this.nom} ACTIF`);
            console.log(`🌐 Interface: http://127.0.0.1:${this.portProxy}`);
            console.log(`🔗 Redirection: llama.cpp:${this.portLlama}`);
        });

        // 🔍 VALIDATION CONTINUE
        this.demarrerValidation();
    }

    async servirInterfaceComplete(req, res) {
        try {
            console.log(`🌐 Récupération interface llama.cpp...`);
            
            const htmlOriginal = await this.recupererInterfaceOriginale();
            const htmlAmeliore = this.ameliorerInterfaceComplete(htmlOriginal);
            
            res.writeHead(200, {
                'Content-Type': 'text/html; charset=utf-8',
                'Content-Length': Buffer.byteLength(htmlAmeliore, 'utf8'),
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            });
            res.end(htmlAmeliore);
            
            console.log(`✅ Interface JARVIS complète servie !`);
            
        } catch (error) {
            this.gererErreur(error, res);
        }
    }

    recupererInterfaceOriginale() {
        return new Promise((resolve, reject) => {
            const options = {
                hostname: '127.0.0.1',
                port: this.portLlama,
                path: '/',
                method: 'GET',
                headers: {
                    'Accept-Encoding': 'gzip, deflate, identity',
                    'User-Agent': 'JARVIS-Proxy/2.0',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
            };

            const proxyReq = http.request(options, (proxyRes) => {
                let chunks = [];
                
                proxyRes.on('data', chunk => chunks.push(chunk));
                
                proxyRes.on('end', () => {
                    let buffer = Buffer.concat(chunks);
                    const encoding = proxyRes.headers['content-encoding'];
                    
                    if (encoding === 'gzip') {
                        zlib.gunzip(buffer, (err, decompressed) => {
                            if (err) reject(err);
                            else resolve(decompressed.toString());
                        });
                    } else if (encoding === 'deflate') {
                        zlib.inflate(buffer, (err, decompressed) => {
                            if (err) reject(err);
                            else resolve(decompressed.toString());
                        });
                    } else {
                        resolve(buffer.toString());
                    }
                });
            });

            proxyReq.on('error', reject);
            proxyReq.end();
        });
    }

    ameliorerInterfaceComplete(html) {
        console.log(`🔧 Amélioration interface complète...`);
        
        let htmlModifie = html;
        
        // 🏷️ CHANGER TITRES ET NOMS
        htmlModifie = htmlModifie.replace(/<title>.*?<\/title>/i, `<title>${this.agentName} - Interface Cognitive Avancée</title>`);
        htmlModifie = htmlModifie.replace(/llama\.cpp/g, this.agentName);
        htmlModifie = htmlModifie.replace(/llama-cpp/g, this.agentName);
        
        // 🧠 AJOUTER PANNEAU JARVIS COMPLET
        const panneauJarvis = this.creerPanneauJarvisComplet();
        const scriptJarvis = this.creerScriptJarvisComplet();
        
        // 🎨 AJOUTER STYLES AVANCÉS
        const stylesAvances = this.creerStylesAvances();
        
        // 🔧 INJECTION DANS HTML
        htmlModifie = htmlModifie.replace('</head>', stylesAvances + '</head>');
        htmlModifie = htmlModifie.replace('</body>', panneauJarvis + scriptJarvis + '</body>');
        
        return htmlModifie;
    }

    creerPanneauJarvisComplet() {
        return `
        <!-- 🧠 JARVIS INTERFACE COGNITIVE COMPLÈTE -->
        <div id="jarvis-main-panel" class="jarvis-panel">
            <!-- En-tête avec nom configurable -->
            <div class="jarvis-header">
                <h3 id="jarvis-title">🧠 ${this.agentName}</h3>
                <input type="text" id="agent-name-config" placeholder="Nom de votre agent" value="${this.agentName}" class="jarvis-input">
            </div>
            
            <!-- Boutons cognitifs principaux (horizontal) -->
            <div class="jarvis-buttons-main">
                <button onclick="jarvisToggleMic()" id="jarvis-mic-btn" class="jarvis-btn jarvis-btn-primary">🎤 Micro</button>
                <button onclick="jarvisToggleSpeaker()" id="jarvis-speaker-btn" class="jarvis-btn jarvis-btn-primary">🔊 Audio</button>
                <button onclick="jarvisToggleCamera()" id="jarvis-camera-btn" class="jarvis-btn jarvis-btn-primary">📹 Caméra</button>
                <button onclick="jarvisEvolve()" class="jarvis-btn jarvis-btn-primary">🧬 Évoluer</button>
            </div>
            
            <!-- Boutons mémoire thermique (horizontal) -->
            <div class="jarvis-buttons-memory">
                <button onclick="jarvisTriggerMemory()" class="jarvis-btn jarvis-btn-memory">🧠 Mémoire</button>
                <button onclick="jarvisTriggerEvolution()" class="jarvis-btn jarvis-btn-evolution">🚀 Évolution</button>
                <button onclick="jarvisTriggerAnalysis()" class="jarvis-btn jarvis-btn-analysis">🔍 Analyse</button>
            </div>
            
            <!-- Boutons interface originaux (préservés) -->
            <div class="jarvis-buttons-original">
                <button onclick="jarvisToggleSettings()" class="jarvis-btn jarvis-btn-secondary">⚙️ Settings</button>
                <button onclick="jarvisToggleTheme()" class="jarvis-btn jarvis-btn-secondary">🎨 Theme</button>
                <button onclick="jarvisTogglePanel()" class="jarvis-btn jarvis-btn-secondary">📱 Réduire</button>
            </div>
            
            <!-- Statut et informations -->
            <div class="jarvis-status">
                <div class="jarvis-qi">QI Agent: <span id="jarvis-qi-display">341.0</span></div>
                <div class="jarvis-memory-status">🧠 Mémoire Thermique Active</div>
                <div class="jarvis-evolution-status">🔄 Évolution Continue</div>
            </div>
            
            <!-- Zone d'erreurs -->
            <div id="jarvis-errors" class="jarvis-errors" style="display:none;"></div>
        </div>`;
    }

    creerStylesAvances() {
        return `
        <style>
        /* 🎨 STYLES JARVIS PROFESSIONNELS */
        .jarvis-panel {
            position: fixed !important;
            top: 10px !important;
            right: 10px !important;
            width: 420px !important;
            background: linear-gradient(135deg, rgba(0,0,0,0.95), rgba(26,26,46,0.95)) !important;
            border: 2px solid #00ffff !important;
            border-radius: 12px !important;
            padding: 16px !important;
            color: #00ffff !important;
            font-family: 'Courier New', monospace !important;
            z-index: 99999 !important;
            box-shadow: 0 0 25px rgba(0,255,255,0.6), inset 0 0 15px rgba(0,255,255,0.1) !important;
            backdrop-filter: blur(10px) !important;
            transition: all 0.3s ease !important;
        }
        
        .jarvis-header {
            text-align: center !important;
            margin-bottom: 12px !important;
            border-bottom: 1px solid rgba(0,255,255,0.3) !important;
            padding-bottom: 10px !important;
        }
        
        .jarvis-header h3 {
            margin: 0 !important;
            color: #00ffff !important;
            text-shadow: 0 0 10px #00ffff !important;
            font-size: 16px !important;
        }
        
        .jarvis-input {
            background: rgba(0,255,255,0.1) !important;
            border: 1px solid #00ffff !important;
            color: #00ffff !important;
            padding: 6px !important;
            border-radius: 6px !important;
            font-size: 12px !important;
            text-align: center !important;
            margin-top: 6px !important;
            width: 180px !important;
        }
        
        .jarvis-buttons-main, .jarvis-buttons-memory, .jarvis-buttons-original {
            display: flex !important;
            gap: 8px !important;
            margin-bottom: 10px !important;
            justify-content: space-between !important;
        }
        
        .jarvis-btn {
            flex: 1 !important;
            padding: 8px 6px !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            font-size: 11px !important;
            font-weight: bold !important;
            transition: all 0.3s ease !important;
            border: 1px solid !important;
            text-align: center !important;
        }
        
        .jarvis-btn-primary {
            background: linear-gradient(45deg, #1a1a2e, #16213e) !important;
            border-color: #00ffff !important;
            color: #00ffff !important;
        }
        
        .jarvis-btn-primary:hover {
            background: linear-gradient(45deg, #00ffff, #0088cc) !important;
            color: #000 !important;
            box-shadow: 0 0 10px rgba(0,255,255,0.5) !important;
        }
        
        .jarvis-btn-memory {
            background: linear-gradient(45deg, #2e1a1a, #3e1621) !important;
            border-color: #ff6600 !important;
            color: #ff6600 !important;
        }
        
        .jarvis-btn-evolution {
            background: linear-gradient(45deg, #1a2e1a, #213e16) !important;
            border-color: #00ff00 !important;
            color: #00ff00 !important;
        }
        
        .jarvis-btn-analysis {
            background: linear-gradient(45deg, #2e2e1a, #3e3e21) !important;
            border-color: #ffff00 !important;
            color: #ffff00 !important;
        }
        
        .jarvis-btn-secondary {
            background: linear-gradient(45deg, #2e2e2e, #1a1a1a) !important;
            border-color: #888888 !important;
            color: #cccccc !important;
        }
        
        .jarvis-status {
            background: rgba(0,255,255,0.15) !important;
            border-radius: 8px !important;
            padding: 12px !important;
            text-align: center !important;
            margin-top: 10px !important;
        }
        
        .jarvis-qi {
            font-size: 14px !important;
            font-weight: bold !important;
            margin-bottom: 6px !important;
        }
        
        .jarvis-memory-status, .jarvis-evolution-status {
            font-size: 10px !important;
            color: #88ffff !important;
            margin: 2px 0 !important;
        }
        
        .jarvis-errors {
            background: rgba(255,0,0,0.2) !important;
            border: 1px solid #ff0000 !important;
            border-radius: 6px !important;
            padding: 8px !important;
            margin-top: 10px !important;
            font-size: 10px !important;
            color: #ff6666 !important;
        }
        
        .jarvis-panel.minimized {
            height: 60px !important;
            overflow: hidden !important;
        }
        </style>`;
    }

    creerScriptJarvisComplet() {
        return `
        <script>
        // 🧠 JARVIS SYSTÈME COGNITIF PROFESSIONNEL
        class JarvisCognitiveSystem {
            constructor() {
                this.micActive = false;
                this.speakerActive = false;
                this.cameraActive = false;
                this.qi = 341.0;
                this.recognition = null;
                this.synthesis = window.speechSynthesis;
                this.stream = null;
                this.panelMinimized = false;
                this.errors = [];

                this.init();
            }

            init() {
                console.log('🧠 JARVIS Système Cognitif - Initialisation...');
                this.setupEventListeners();
                this.startEvolution();
                this.validateSystem();
                console.log('✅ JARVIS Système Cognitif - Prêt !');
            }

            setupEventListeners() {
                // Configuration nom agent
                const nameInput = document.getElementById('agent-name-config');
                if (nameInput) {
                    nameInput.addEventListener('input', (e) => this.updateAgentName(e.target.value));
                }
            }

            updateAgentName(newName) {
                if (!newName || newName.trim() === '') return;

                const cleanName = newName.trim().toUpperCase();
                document.getElementById('jarvis-title').textContent = '🧠 ' + cleanName;
                document.title = cleanName + ' - Interface Cognitive Avancée';

                // Remplacer dans l'interface
                this.replaceInterfaceNames(cleanName);

                console.log('🏷️ Nom agent mis à jour:', cleanName);
            }

            replaceInterfaceNames(newName) {
                try {
                    const elements = document.querySelectorAll('*');
                    elements.forEach(el => {
                        if (el.textContent && el.textContent.includes('JARVIS') && !el.id.includes('jarvis')) {
                            el.textContent = el.textContent.replace(/JARVIS/g, newName);
                        }
                    });
                } catch (error) {
                    this.logError('Erreur remplacement noms', error);
                }
            }

            validateSystem() {
                const requiredElements = [
                    'jarvis-main-panel',
                    'jarvis-mic-btn',
                    'jarvis-speaker-btn',
                    'jarvis-camera-btn',
                    'jarvis-qi-display'
                ];

                requiredElements.forEach(id => {
                    if (!document.getElementById(id)) {
                        this.logError('Élément manquant: ' + id);
                    }
                });
            }

            logError(message, error = null) {
                console.error('❌ JARVIS:', message, error);
                this.errors.push({ message, error, timestamp: new Date() });
                this.displayErrors();
            }

            displayErrors() {
                const errorDiv = document.getElementById('jarvis-errors');
                if (errorDiv && this.errors.length > 0) {
                    errorDiv.style.display = 'block';
                    errorDiv.innerHTML = this.errors.slice(-3).map(err =>
                        \`<div>❌ \${err.message} (\${err.timestamp.toLocaleTimeString()})</div>\`
                    ).join('');
                }
            }

            startEvolution() {
                setInterval(() => {
                    this.qi += Math.random() * 0.1;
                    const qiDisplay = document.getElementById('jarvis-qi-display');
                    if (qiDisplay) {
                        qiDisplay.textContent = this.qi.toFixed(1);
                    }
                }, 10000);
            }
        }

        // Instance globale
        let jarvisSystem = null;

        // 🎤 FONCTIONS COGNITIVES
        function jarvisToggleMic() {
            try {
                const btn = document.getElementById('jarvis-mic-btn');
                if (!jarvisSystem.micActive && 'webkitSpeechRecognition' in window) {
                    jarvisSystem.recognition = new webkitSpeechRecognition();
                    jarvisSystem.recognition.continuous = true;
                    jarvisSystem.recognition.lang = 'fr-FR';
                    jarvisSystem.recognition.onresult = function(event) {
                        let transcript = '';
                        for (let i = event.resultIndex; i < event.results.length; i++) {
                            if (event.results[i].isFinal) {
                                transcript += event.results[i][0].transcript;
                            }
                        }
                        if (transcript) {
                            jarvisSendToInput(transcript);
                        }
                    };
                    jarvisSystem.recognition.start();
                    jarvisSystem.micActive = true;
                    btn.style.background = '#00ffff';
                    btn.style.color = '#000';
                    console.log('🎤 Reconnaissance vocale activée');
                } else if (jarvisSystem.recognition) {
                    jarvisSystem.recognition.stop();
                    jarvisSystem.micActive = false;
                    btn.style.background = 'linear-gradient(45deg, #1a1a2e, #16213e)';
                    btn.style.color = '#00ffff';
                    console.log('🎤 Reconnaissance vocale désactivée');
                }
            } catch (error) {
                jarvisSystem.logError('Erreur micro', error);
            }
        }

        function jarvisToggleSpeaker() {
            try {
                const btn = document.getElementById('jarvis-speaker-btn');
                jarvisSystem.speakerActive = !jarvisSystem.speakerActive;

                if (jarvisSystem.speakerActive) {
                    btn.style.background = '#00ffff';
                    btn.style.color = '#000';
                    jarvisSystem.synthesis.speak(new SpeechSynthesisUtterance('Synthèse vocale activée'));
                    console.log('🔊 Synthèse vocale activée');
                } else {
                    btn.style.background = 'linear-gradient(45deg, #1a1a2e, #16213e)';
                    btn.style.color = '#00ffff';
                    jarvisSystem.synthesis.cancel();
                    console.log('🔊 Synthèse vocale désactivée');
                }
            } catch (error) {
                jarvisSystem.logError('Erreur haut-parleur', error);
            }
        }

        async function jarvisToggleCamera() {
            try {
                const btn = document.getElementById('jarvis-camera-btn');
                if (!jarvisSystem.cameraActive) {
                    jarvisSystem.stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    jarvisSystem.cameraActive = true;
                    btn.style.background = '#00ffff';
                    btn.style.color = '#000';
                    console.log('📹 Caméra activée');

                    const video = document.createElement('video');
                    video.srcObject = jarvisSystem.stream;
                    video.autoplay = true;
                    video.muted = true;
                    video.style.cssText = 'position:fixed;bottom:10px;right:10px;width:150px;height:100px;border:2px solid #00ffff;border-radius:8px;z-index:10000;';
                    document.body.appendChild(video);
                } else {
                    if (jarvisSystem.stream) {
                        jarvisSystem.stream.getTracks().forEach(track => track.stop());
                        const video = document.querySelector('video[style*="position:fixed"]');
                        if (video) video.remove();
                    }
                    jarvisSystem.cameraActive = false;
                    btn.style.background = 'linear-gradient(45deg, #1a1a2e, #16213e)';
                    btn.style.color = '#00ffff';
                    console.log('📹 Caméra désactivée');
                }
            } catch (error) {
                jarvisSystem.logError('Erreur caméra', error);
            }
        }

        function jarvisEvolve() {
            try {
                jarvisSystem.qi += Math.random() * 2 + 0.5;
                document.getElementById('jarvis-qi-display').textContent = jarvisSystem.qi.toFixed(1);
                console.log('🧬 Évolution déclenchée - Nouveau QI:', jarvisSystem.qi.toFixed(1));
            } catch (error) {
                jarvisSystem.logError('Erreur évolution', error);
            }
        }

        // 🧠 DÉCLENCHEURS MÉMOIRE THERMIQUE
        function jarvisTriggerMemory() {
            const agentName = document.getElementById('agent-name-config').value || 'JARVIS';
            const prompt = agentName + ', te souviens-tu de nos conversations précédentes ? Utilise ta mémoire thermique pour me rappeler ce dont nous avons parlé et montre-moi que tu te souviens vraiment.';
            jarvisSendPrompt(prompt);
            console.log('🧠 Déclenchement mémoire thermique');
        }

        function jarvisTriggerEvolution() {
            const agentName = document.getElementById('agent-name-config').value || 'JARVIS';
            const prompt = agentName + ', comment évolue ton QI avec tes agents autonomes ? Explique-moi tes nouvelles capacités, tes connexions neuronales et ton processus d\\'évolution cognitive.';
            jarvisSendPrompt(prompt);
            console.log('🚀 Déclenchement évolution cognitive');
        }

        function jarvisTriggerAnalysis() {
            const agentName = document.getElementById('agent-name-config').value || 'JARVIS';
            const prompt = agentName + ', analyse tes pensées récentes et explique-moi ton processus de réflexion. Que penses-tu de notre interaction actuelle et comment traites-tu l\\'information ?';
            jarvisSendPrompt(prompt);
            console.log('🔍 Déclenchement analyse cognitive');
        }

        // 🔧 FONCTIONS INTERFACE ORIGINALES
        function jarvisToggleSettings() {
            // Chercher et déclencher le bouton settings original
            const originalSettingsBtn = document.querySelector('button[onclick*="settings"], button[title*="Settings"], .settings-button');
            if (originalSettingsBtn) {
                originalSettingsBtn.click();
                console.log('⚙️ Settings ouvert');
            } else {
                console.log('⚙️ Bouton Settings non trouvé');
            }
        }

        function jarvisToggleTheme() {
            // Chercher et déclencher le bouton theme original
            const originalThemeBtn = document.querySelector('button[onclick*="theme"], button[title*="Theme"], .theme-button');
            if (originalThemeBtn) {
                originalThemeBtn.click();
                console.log('🎨 Theme changé');
            } else {
                console.log('🎨 Bouton Theme non trouvé');
            }
        }

        function jarvisTogglePanel() {
            const panel = document.getElementById('jarvis-main-panel');
            if (panel) {
                jarvisSystem.panelMinimized = !jarvisSystem.panelMinimized;
                if (jarvisSystem.panelMinimized) {
                    panel.classList.add('minimized');
                } else {
                    panel.classList.remove('minimized');
                }
            }
        }

        // 📤 UTILITAIRES
        function jarvisSendToInput(text) {
            const input = document.querySelector('textarea, input[type="text"]');
            if (input) {
                input.value = text;
                input.focus();
            }
        }

        function jarvisSendPrompt(prompt) {
            jarvisSendToInput(prompt);

            setTimeout(() => {
                const sendBtn = document.querySelector('button[type="submit"], .send-button, button:contains("Send")');
                if (sendBtn) {
                    sendBtn.click();
                } else {
                    // Essayer avec Enter
                    const input = document.querySelector('textarea, input[type="text"]');
                    if (input) {
                        const event = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true
                        });
                        input.dispatchEvent(event);
                    }
                }
            }, 200);
        }

        // 🚀 INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                jarvisSystem = new JarvisCognitiveSystem();
            }, 1000);
        });

        // Initialisation immédiate si DOM déjà chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => {
                    if (!jarvisSystem) {
                        jarvisSystem = new JarvisCognitiveSystem();
                    }
                }, 1000);
            });
        } else {
            setTimeout(() => {
                if (!jarvisSystem) {
                    jarvisSystem = new JarvisCognitiveSystem();
                }
            }, 1000);
        }
        </script>`;
    }

    redirigerVersLlama(req, res) {
        const options = {
            hostname: '127.0.0.1',
            port: this.portLlama,
            path: req.url,
            method: req.method,
            headers: { ...req.headers }
        };

        delete options.headers.host;

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res, { end: true });
        });

        proxyReq.on('error', (error) => {
            this.gererErreur(error, res);
        });

        req.pipe(proxyReq, { end: true });
    }

    gererErreur(error, res) {
        console.error(`❌ ERREUR ${this.nom}:`, error.message);
        this.erreurs.push({ error: error.message, timestamp: new Date() });

        if (!res.headersSent) {
            res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <html>
                <head><title>ERREUR JARVIS</title></head>
                <body style="background:#1a1a2e;color:#ff0000;font-family:monospace;padding:20px;">
                    <h1>❌ ERREUR SYSTÈME JARVIS</h1>
                    <p><strong>Erreur:</strong> ${error.message}</p>
                    <p><strong>Heure:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Solution:</strong> Vérifiez que llama.cpp fonctionne sur le port ${this.portLlama}</p>
                    <button onclick="location.reload()">🔄 Réessayer</button>
                </body>
                </html>
            `);
        }
    }

    demarrerValidation() {
        console.log(`🔍 Démarrage validation continue...`);

        setInterval(() => {
            this.validerSysteme();
        }, 30000); // Validation toutes les 30 secondes
    }

    validerSysteme() {
        // Test connexion llama.cpp
        const testReq = http.request({
            hostname: '127.0.0.1',
            port: this.portLlama,
            path: '/',
            method: 'HEAD',
            timeout: 5000
        }, (res) => {
            if (res.statusCode === 200) {
                console.log(`✅ llama.cpp connecté (port ${this.portLlama})`);
            } else {
                console.log(`⚠️ llama.cpp réponse: ${res.statusCode}`);
            }
        });

        testReq.on('error', (error) => {
            console.error(`❌ llama.cpp non accessible:`, error.message);
        });

        testReq.on('timeout', () => {
            console.error(`⏰ Timeout connexion llama.cpp`);
            testReq.destroy();
        });

        testReq.end();
    }
}

// 🚀 DÉMARRAGE AUTOMATIQUE
const proxy = new ProxyJarvisProfessionnel();
proxy.demarrer();
