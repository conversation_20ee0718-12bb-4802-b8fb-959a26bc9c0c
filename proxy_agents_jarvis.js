#!/usr/bin/env node

/**
 * PROXY JARVIS - Connecte l'interface llama.cpp aux Agent 1 & Agent 2
 * Usage: node proxy_agents_jarvis.js
 * Interface: http://localhost:8081 (proxy vers localhost:8000)
 */

const http = require('http');
const httpProxy = require('http-proxy-middleware');
const express = require('express');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
    PROXY_PORT: 8081,
    TARGET_PORT: 8000,
    MEMORY_FILE: 'thermal_memory_persistent.json'
};

// Mémoire thermique
let thermalMemory = [];
let currentAgent = 'agent1';

// Charger la mémoire thermique
function loadThermalMemory() {
    try {
        if (fs.existsSync(CONFIG.MEMORY_FILE)) {
            const data = fs.readFileSync(CONFIG.MEMORY_FILE, 'utf8');
            const parsed = JSON.parse(data);
            thermalMemory = parsed.conversations || [];
            console.log(`🧠 Mémoire thermique chargée: ${thermalMemory.length} entrées`);
        }
    } catch (error) {
        console.log('📝 Création nouvelle mémoire thermique');
        thermalMemory = [];
    }
}

// Sauvegarder la mémoire thermique
function saveThermalMemory() {
    try {
        const data = {
            conversations: thermalMemory,
            lastUpdate: new Date().toISOString(),
            totalEntries: thermalMemory.length
        };
        fs.writeFileSync(CONFIG.MEMORY_FILE, JSON.stringify(data, null, 2));
        console.log(`💾 Mémoire sauvegardée: ${thermalMemory.length} entrées`);
    } catch (error) {
        console.error('❌ Erreur sauvegarde mémoire:', error.message);
    }
}

// Rechercher dans la mémoire thermique
function searchThermalMemory(query) {
    const keywords = query.toLowerCase().split(' ').filter(word => word.length > 3);
    const results = [];
    
    thermalMemory.slice(-100).forEach(entry => {
        const content = entry.content.toLowerCase();
        const score = keywords.reduce((acc, keyword) => {
            return acc + (content.includes(keyword) ? 2 : 0) + 
                   (content.includes(keyword.substring(0, 4)) ? 1 : 0);
        }, 0);
        
        if (score > 0) {
            results.push({ ...entry, score });
        }
    });
    
    return results
        .sort((a, b) => b.score - a.score)
        .slice(0, 3)
        .map(entry => `[${entry.sender}]: ${entry.content.substring(0, 150)}`);
}

// Ajouter à la mémoire thermique
function addToThermalMemory(content, sender, agent = currentAgent) {
    const entry = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        sender: sender,
        content: content,
        agent: agent,
        keywords: content.toLowerCase().split(' ').filter(word => word.length > 3).slice(0, 10)
    };
    
    thermalMemory.push(entry);
    
    // Garder seulement les 2000 dernières entrées
    if (thermalMemory.length > 2000) {
        thermalMemory = thermalMemory.slice(-2000);
    }
    
    saveThermalMemory();
}

// Traitement des agents
function processWithAgent(prompt, agent = 'agent1') {
    let systemPrompt = '';
    let temperature = 0.7;
    
    if (agent === 'agent1') {
        systemPrompt = `[AGENT 1 - PRINCIPAL JARVIS] Tu es l'agent principal de JARVIS. Tu gères les conversations et la mémoire thermique. Réponds de manière directe, utile et professionnelle. Tu peux accéder à la mémoire des conversations précédentes.`;
        temperature = 0.7;
    } else {
        systemPrompt = `[AGENT 2 - MOTEUR THERMIQUE] Tu es l'agent de réflexion autonome de JARVIS. Tu analyses en profondeur, apprends des interactions et proposes des améliorations. Sois analytique, réfléchi et créatif.`;
        temperature = 0.9;
    }
    
    return {
        enhancedPrompt: `${systemPrompt}\n\n${prompt}`,
        temperature: temperature,
        max_tokens: 512
    };
}

// Créer l'application Express
const app = express();

// Middleware pour parser JSON
app.use(express.json({ limit: '10mb' }));

// Servir l'interface modifiée
app.get('/', (req, res) => {
    const interfacePath = path.join(__dirname, 'interface_exacte_llama.html');
    if (fs.existsSync(interfacePath)) {
        let html = fs.readFileSync(interfacePath, 'utf8');
        
        // Injecter le script de connexion aux agents
        const agentScript = `
        <script>
            // Configuration proxy agents
            window.JARVIS_CONFIG = {
                proxyMode: true,
                currentAgent: '${currentAgent}',
                memoryEntries: ${thermalMemory.length}
            };
            
            // Indicateur d'agent
            document.addEventListener('DOMContentLoaded', () => {
                const indicator = document.createElement('div');
                indicator.id = 'jarvis-agent-indicator';
                indicator.style.cssText = \`
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: ${currentAgent === 'agent1' ? '#007bff' : '#28a745'};
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 10000;
                    cursor: pointer;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                \`;
                indicator.textContent = '🤖 ${currentAgent === 'agent1' ? 'Agent 1 - Principal' : 'Agent 2 - Thermique'}';
                indicator.onclick = () => {
                    fetch('/switch-agent', { method: 'POST' })
                        .then(r => r.json())
                        .then(data => {
                            indicator.textContent = '🤖 ' + data.agentName;
                            indicator.style.background = data.color;
                            console.log('Basculé vers:', data.agentName);
                        });
                };
                document.body.appendChild(indicator);
                
                // Indicateur mémoire
                const memoryIndicator = document.createElement('div');
                memoryIndicator.style.cssText = \`
                    position: fixed;
                    top: 50px;
                    right: 10px;
                    background: #6c757d;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    z-index: 10000;
                \`;
                memoryIndicator.textContent = '🧠 ${thermalMemory.length} souvenirs';
                document.body.appendChild(memoryIndicator);
            });
        </script>
        `;
        
        html = html.replace('</head>', agentScript + '</head>');
        res.send(html);
    } else {
        res.status(404).send('Interface non trouvée');
    }
});

// Endpoint pour changer d'agent
app.post('/switch-agent', (req, res) => {
    currentAgent = currentAgent === 'agent1' ? 'agent2' : 'agent1';
    const agentInfo = {
        agent1: { name: 'Agent 1 - Principal', color: '#007bff' },
        agent2: { name: 'Agent 2 - Thermique', color: '#28a745' }
    };
    
    console.log(`🔄 Basculé vers ${agentInfo[currentAgent].name}`);
    res.json({
        currentAgent: currentAgent,
        agentName: agentInfo[currentAgent].name,
        color: agentInfo[currentAgent].color
    });
});

// Endpoint pour la mémoire
app.get('/memory', (req, res) => {
    res.json({
        entries: thermalMemory.length,
        recent: thermalMemory.slice(-10),
        currentAgent: currentAgent
    });
});

// Proxy middleware avec interception
const proxyMiddleware = httpProxy.createProxyMiddleware({
    target: `http://localhost:${CONFIG.TARGET_PORT}`,
    changeOrigin: true,
    selfHandleResponse: true,
    onProxyReq: (proxyReq, req, res) => {
        // Intercepter les requêtes de completion
        if (req.url.includes('/completion') || req.url.includes('/chat/completions')) {
            let body = '';
            
            req.on('data', chunk => {
                body += chunk.toString();
            });
            
            req.on('end', () => {
                try {
                    const requestData = JSON.parse(body);
                    const userPrompt = requestData.prompt || 
                                     (requestData.messages && requestData.messages[requestData.messages.length - 1]?.content);
                    
                    if (userPrompt) {
                        console.log(`📝 [${currentAgent.toUpperCase()}] Question: ${userPrompt.substring(0, 100)}...`);
                        
                        // Sauvegarder la question
                        addToThermalMemory(userPrompt, 'user', currentAgent);
                        
                        // Rechercher dans la mémoire
                        const memoryContext = searchThermalMemory(userPrompt);
                        
                        // Traiter avec l'agent actuel
                        const agentProcessing = processWithAgent(userPrompt, currentAgent);
                        
                        // Construire le prompt enrichi
                        let enhancedPrompt = agentProcessing.enhancedPrompt;
                        if (memoryContext.length > 0) {
                            enhancedPrompt += `\n\nContexte mémoire thermique:\n${memoryContext.join('\n')}`;
                        }
                        
                        // Modifier la requête
                        const modifiedRequest = {
                            ...requestData,
                            prompt: enhancedPrompt,
                            temperature: agentProcessing.temperature,
                            max_tokens: agentProcessing.max_tokens
                        };
                        
                        // Réécrire le body
                        const newBody = JSON.stringify(modifiedRequest);
                        proxyReq.setHeader('Content-Length', Buffer.byteLength(newBody));
                        proxyReq.write(newBody);
                    }
                } catch (error) {
                    console.error('❌ Erreur traitement requête:', error.message);
                }
            });
        }
    },
    onProxyRes: (proxyRes, req, res) => {
        let body = '';
        proxyRes.on('data', chunk => {
            body += chunk;
        });
        
        proxyRes.on('end', () => {
            try {
                if (req.url.includes('/completion') || req.url.includes('/chat/completions')) {
                    const responseData = JSON.parse(body);
                    const agentResponse = responseData.content || responseData.text || 
                                        (responseData.choices && responseData.choices[0]?.message?.content);
                    
                    if (agentResponse) {
                        console.log(`🤖 [${currentAgent.toUpperCase()}] Réponse: ${agentResponse.substring(0, 100)}...`);
                        addToThermalMemory(agentResponse, currentAgent);
                    }
                }
                
                res.writeHead(proxyRes.statusCode, proxyRes.headers);
                res.end(body);
            } catch (error) {
                console.error('❌ Erreur traitement réponse:', error.message);
                res.writeHead(proxyRes.statusCode, proxyRes.headers);
                res.end(body);
            }
        });
    }
});

// Utiliser le proxy pour toutes les autres routes
app.use('/', proxyMiddleware);

// Démarrer le serveur
function startServer() {
    loadThermalMemory();
    
    app.listen(CONFIG.PROXY_PORT, () => {
        console.log('🚀 ================================');
        console.log('🤖 JARVIS PROXY AGENTS DÉMARRÉ');
        console.log('🚀 ================================');
        console.log(`📡 Proxy: http://localhost:${CONFIG.PROXY_PORT}`);
        console.log(`🎯 Target: http://localhost:${CONFIG.TARGET_PORT}`);
        console.log(`🧠 Mémoire: ${thermalMemory.length} entrées`);
        console.log(`🤖 Agent actuel: ${currentAgent}`);
        console.log('🚀 ================================');
    });
}

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
    console.log('\n💾 Sauvegarde finale de la mémoire...');
    saveThermalMemory();
    process.exit(0);
});

startServer();
