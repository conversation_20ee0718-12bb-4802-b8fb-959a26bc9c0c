<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS R1 8B - Interface avec Bulles d'Agents</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            color: #00ff00;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        /* SIDEBAR GAUCHE */
        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.8);
            border-right: 2px solid #00ff00;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h2 {
            color: #00ff00;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 0 10px #00ff00;
        }

        .agent-status {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .agent-card {
            background: rgba(0, 100, 255, 0.1);
            border: 1px solid #0066ff;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            text-align: center;
        }

        .qi-display {
            font-size: 24px;
            font-weight: bold;
            color: #ffff00;
            text-shadow: 0 0 5px #ffff00;
        }

        /* ZONE PRINCIPALE */
        .main-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .chat-messages {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }

        .message.user {
            background: rgba(0, 255, 0, 0.1);
            border-left: 4px solid #00ff00;
        }

        .message.jarvis {
            background: rgba(0, 100, 255, 0.1);
            border-left: 4px solid #0066ff;
        }

        .message.agent2 {
            background: rgba(255, 165, 0, 0.1);
            border-left: 4px solid #ffa500;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        .input-area textarea {
            flex: 1;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #00ff00;
            border-radius: 8px;
            color: #00ff00;
            padding: 15px;
            font-family: inherit;
            resize: vertical;
            min-height: 60px;
        }

        .input-area button {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            border-radius: 8px;
            color: #00ff00;
            padding: 15px 25px;
            cursor: pointer;
            font-family: inherit;
            transition: all 0.3s;
        }

        .input-area button:hover {
            background: rgba(0, 255, 0, 0.3);
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
        }

        /* BULLES D'AGENTS */
        .agent-bubble {
            position: fixed;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #ffa500;
            border-radius: 15px;
            padding: 20px;
            min-width: 300px;
            max-width: 500px;
            box-shadow: 0 0 20px rgba(255, 165, 0, 0.5);
            z-index: 1000;
            animation: bubbleAppear 0.5s ease-out;
            resize: both;
            overflow: auto;
        }

        .agent-bubble.minimized {
            width: 200px;
            height: 60px;
            overflow: hidden;
        }

        .agent-bubble.maximized {
            width: 80vw;
            height: 80vh;
            top: 10vh !important;
            left: 10vw !important;
        }

        .bubble-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #ffa500;
            padding-bottom: 10px;
        }

        .bubble-title {
            color: #ffa500;
            font-weight: bold;
            font-size: 16px;
        }

        .bubble-controls {
            display: flex;
            gap: 5px;
        }

        .bubble-btn {
            background: rgba(255, 165, 0, 0.2);
            border: 1px solid #ffa500;
            border-radius: 4px;
            color: #ffa500;
            padding: 5px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .bubble-btn:hover {
            background: rgba(255, 165, 0, 0.4);
        }

        .bubble-content {
            color: #ffffff;
            line-height: 1.5;
        }

        @keyframes bubbleAppear {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* RESPONSIVE */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .agent-bubble {
                width: 90vw;
                max-width: none;
                left: 5vw !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- SIDEBAR GAUCHE -->
        <div class="sidebar">
            <h2>🤖 JARVIS R1 8B</h2>
            
            <div class="agent-status">
                <h3>📊 Status Agents</h3>
                <div class="agent-card">
                    <div>Agent 1 - Principal</div>
                    <div class="qi-display" id="agent1QI">341.5</div>
                    <div>QI</div>
                </div>
                <div class="agent-card">
                    <div>Agent 2 - Thermique</div>
                    <div class="qi-display" id="agent2Temp">37.2°C</div>
                    <div>Temp</div>
                </div>
            </div>

            <div class="agent-status">
                <h3>🌡️ Mémoire Thermique</h3>
                <div>Conversations: <span id="conversationCount">0</span></div>
                <div>Souvenirs: <span id="memoryCount">0</span></div>
                <div>QI Global: <span id="globalQI">341.5</span></div>
            </div>

            <div class="agent-status">
                <h3>🔌 MCP Tools</h3>
                <button class="bubble-btn" onclick="testInternet()">🌐 Test Internet</button>
                <button class="bubble-btn" onclick="getNews()">📰 Actualités</button>
                <button class="bubble-btn" onclick="getWeather()">🌤️ Météo</button>
            </div>

            <div class="agent-status">
                <h3>🤖 Contrôles Agents</h3>
                <button class="bubble-btn" onclick="simulateAgent2Message()">💬 Simuler Agent 2</button>
                <button class="bubble-btn" onclick="clearBubbles()">🧹 Nettoyer Bulles</button>
            </div>
        </div>

        <!-- ZONE PRINCIPALE -->
        <div class="main-area">
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message jarvis">
                        <strong>JARVIS:</strong> Système cognitif R1 8B initialisé. Mémoire thermique active. Agent 2 en standby.
                    </div>
                </div>

                <div class="input-area">
                    <textarea id="userInput" placeholder="Tapez votre message à JARVIS... (Agent 2 peut intervenir automatiquement)" onkeypress="handleEnter(event)"></textarea>
                    <button onclick="sendMessage()">📤 Envoyer</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let bubbleCounter = 0;
        let activeBubbles = [];
        let agent1QI = 341.5;
        let agent2Temp = 37.2;
        let conversationCount = 0;
        let memoryCount = 0;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 JARVIS Interface avec Bulles initialisée');
            updateDisplays();
            startAgentSimulation();
        });

        // Mise à jour des affichages
        function updateDisplays() {
            document.getElementById('agent1QI').textContent = agent1QI.toFixed(1);
            document.getElementById('agent2Temp').textContent = agent2Temp.toFixed(1) + '°C';
            document.getElementById('globalQI').textContent = agent1QI.toFixed(1);
            document.getElementById('conversationCount').textContent = conversationCount;
            document.getElementById('memoryCount').textContent = memoryCount;
        }

        // Gestion des messages
        function handleEnter(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('userInput');
            const message = input.value.trim();
            
            if (message) {
                addMessage('user', message);
                input.value = '';
                
                // Simuler réponse JARVIS
                setTimeout(() => {
                    processMessage(message);
                }, 1000);
                
                conversationCount++;
                updateDisplays();
            }
        }

        function addMessage(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const prefix = type === 'user' ? 'VOUS:' : 
                          type === 'jarvis' ? 'JARVIS:' : 'AGENT 2:';
            
            messageDiv.innerHTML = `<strong>${prefix}</strong> ${content}`;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Traitement des messages - UTILISE LA MÊME LOGIQUE QUE L'INTERFACE ORIGINALE
        async function processMessage(message) {
            try {
                console.log('📡 Envoi message vers JARVIS:', message);

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: message })
                });

                if (response.ok) {
                    const data = await response.json();
                    const reponseJarvis = data.response || 'Réponse en cours de traitement...';

                    addMessage('jarvis', reponseJarvis);
                    console.log('✅ Réponse JARVIS reçue');

                    // Mettre à jour les stats
                    memoryCount = data.memoryStatus?.souvenirs || memoryCount;
                    updateDisplays();

                    // Chance que l'Agent 2 intervienne (comme dans l'original)
                    if (Math.random() > 0.7) {
                        setTimeout(() => {
                            createAgent2Bubble(message);
                        }, 2000);
                    }
                } else {
                    console.error('❌ Erreur HTTP:', response.status);
                    addMessage('jarvis', `❌ Erreur de communication (${response.status})`);
                }

            } catch (error) {
                console.error('❌ Erreur:', error);
                addMessage('jarvis', `❌ Erreur de communication: ${error.message}`);
            }
        }

        // Création des bulles d'Agent 2
        function createAgent2Bubble(triggerMessage) {
            bubbleCounter++;
            const bubbleId = `bubble-${bubbleCounter}`;

            const bubble = document.createElement('div');
            bubble.className = 'agent-bubble';
            bubble.id = bubbleId;
            bubble.style.top = `${100 + (bubbleCounter * 50)}px`;
            bubble.style.right = `${50 + (bubbleCounter * 20)}px`;

            const agent2Messages = [
                `🤖 Agent 2 analyse: "${triggerMessage.substring(0, 30)}..."`,
                `🧠 Réflexion thermique activée sur votre demande`,
                `🔥 Température cognitive en hausse: ${(agent2Temp + Math.random()).toFixed(1)}°C`,
                `💭 Connexions neuronales renforcées avec Agent 1`,
                `⚡ Stimulation cognitive détectée - Analyse en cours`,
                `🌡️ Mémoire thermique mise à jour avec nouveau pattern`
            ];

            const randomMessage = agent2Messages[Math.floor(Math.random() * agent2Messages.length)];

            bubble.innerHTML = `
                <div class="bubble-header">
                    <div class="bubble-title">🤖 Agent 2 - Contrôleur Thermique</div>
                    <div class="bubble-controls">
                        <button class="bubble-btn" onclick="minimizeBubble('${bubbleId}')">➖</button>
                        <button class="bubble-btn" onclick="maximizeBubble('${bubbleId}')">⬜</button>
                        <button class="bubble-btn" onclick="closeBubble('${bubbleId}')">❌</button>
                    </div>
                </div>
                <div class="bubble-content">
                    <p><strong>💬 Message:</strong> ${randomMessage}</p>
                    <p><strong>🌡️ Température:</strong> ${agent2Temp.toFixed(1)}°C</p>
                    <p><strong>⏰ Timestamp:</strong> ${new Date().toLocaleTimeString()}</p>
                    <p><strong>🎯 Déclencheur:</strong> "${triggerMessage.substring(0, 50)}..."</p>
                    <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ffa500;">
                        <small>🔄 Agent 2 surveille en continu les interactions et ajuste la température cognitive selon les besoins.</small>
                    </div>
                </div>
            `;

            document.body.appendChild(bubble);
            activeBubbles.push(bubbleId);

            // Faire vibrer légèrement la bulle
            setTimeout(() => {
                bubble.style.animation = 'none';
            }, 500);

            // Auto-fermeture après 30 secondes si pas d'interaction
            setTimeout(() => {
                if (document.getElementById(bubbleId)) {
                    closeBubble(bubbleId);
                }
            }, 30000);

            // Mettre à jour la température de l'Agent 2
            agent2Temp += Math.random() * 0.5;
            updateDisplays();

            console.log(`💬 Bulle Agent 2 créée: ${bubbleId}`);
        }

        // Contrôles des bulles
        function minimizeBubble(bubbleId) {
            const bubble = document.getElementById(bubbleId);
            if (bubble) {
                bubble.classList.toggle('minimized');
                console.log(`➖ Bulle ${bubbleId} minimisée/restaurée`);
            }
        }

        function maximizeBubble(bubbleId) {
            const bubble = document.getElementById(bubbleId);
            if (bubble) {
                bubble.classList.toggle('maximized');
                console.log(`⬜ Bulle ${bubbleId} maximisée/restaurée`);
            }
        }

        function closeBubble(bubbleId) {
            const bubble = document.getElementById(bubbleId);
            if (bubble) {
                bubble.style.animation = 'bubbleAppear 0.3s ease-in reverse';
                setTimeout(() => {
                    bubble.remove();
                    activeBubbles = activeBubbles.filter(id => id !== bubbleId);
                    console.log(`❌ Bulle ${bubbleId} fermée`);
                }, 300);
            }
        }

        function clearBubbles() {
            activeBubbles.forEach(bubbleId => {
                closeBubble(bubbleId);
            });
            console.log('🧹 Toutes les bulles nettoyées');
        }

        // Simulation Agent 2
        function simulateAgent2Message() {
            const simulatedMessages = [
                "Test de communication Agent 2",
                "Vérification des systèmes thermiques",
                "Analyse des patterns de conversation",
                "Optimisation des connexions neuronales"
            ];

            const randomMsg = simulatedMessages[Math.floor(Math.random() * simulatedMessages.length)];
            createAgent2Bubble(randomMsg);
        }

        // Simulation automatique des agents
        function startAgentSimulation() {
            // Agent 2 envoie des messages périodiquement
            setInterval(() => {
                if (Math.random() > 0.8 && activeBubbles.length < 3) {
                    const autoMessages = [
                        "Surveillance thermique automatique",
                        "Évolution cognitive détectée",
                        "Mise à jour mémoire thermique",
                        "Optimisation des performances"
                    ];

                    const autoMsg = autoMessages[Math.floor(Math.random() * autoMessages.length)];
                    createAgent2Bubble(autoMsg);
                }

                // Évolution des paramètres
                agent1QI += Math.random() * 0.1;
                agent2Temp += (Math.random() - 0.5) * 0.1;
                updateDisplays();

            }, 15000); // Toutes les 15 secondes
        }

        // Fonctions MCP
        function testInternet() {
            addMessage('jarvis', '🌐 Test de connexion Internet via MCP...');
            fetch('/mcp/search?q=test')
                .then(response => response.json())
                .then(data => {
                    addMessage('jarvis', `✅ Internet MCP actif: ${data.resultats?.length || 0} résultats trouvés`);
                })
                .catch(error => {
                    addMessage('jarvis', '❌ Erreur connexion Internet MCP');
                });
        }

        function getNews() {
            addMessage('jarvis', '📰 Récupération des actualités...');
            fetch('/mcp/news')
                .then(response => response.json())
                .then(data => {
                    addMessage('jarvis', `📰 ${data.actualites?.length || 0} actualités récupérées via MCP`);
                })
                .catch(error => {
                    addMessage('jarvis', '❌ Erreur récupération actualités');
                });
        }

        function getWeather() {
            addMessage('jarvis', '🌤️ Récupération météo...');
            fetch('/mcp/weather?city=Paris')
                .then(response => response.json())
                .then(data => {
                    addMessage('jarvis', `🌤️ Météo: ${data.temperature || 'N/A'}°C à ${data.city || 'Paris'}`);
                })
                .catch(error => {
                    addMessage('jarvis', '❌ Erreur récupération météo');
                });
        }
    </script>
</body>
</html>
