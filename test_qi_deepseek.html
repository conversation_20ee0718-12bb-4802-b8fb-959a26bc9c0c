<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QI - DeepSeek R1 8B</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 900px; margin: 0 auto;
            background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);
            border-radius: 20px; padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center; color: white; margin-bottom: 30px;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); }
        .test-section {
            background: rgba(255, 255, 255, 0.1); border-radius: 15px;
            padding: 20px; margin-bottom: 20px; color: white;
        }
        .question {
            font-size: 1.2em; margin-bottom: 15px; font-weight: bold;
        }
        .response {
            background: rgba(255, 255, 255, 0.2); border-radius: 10px;
            padding: 15px; margin: 10px 0; min-height: 60px;
            border: 2px solid transparent;
        }
        .response.loading { border-color: #ffd700; }
        .response.success { border-color: #00ff00; }
        .response.error { border-color: #ff0000; }
        .controls {
            text-align: center; margin: 20px 0;
        }
        .btn {
            background: rgba(255, 255, 255, 0.3); color: white;
            border: none; border-radius: 10px; padding: 15px 30px;
            font-size: 16px; font-weight: bold; cursor: pointer;
            margin: 0 10px; transition: all 0.3s ease;
        }
        .btn:hover { background: rgba(255, 255, 255, 0.4); transform: translateY(-2px); }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .score {
            text-align: center; font-size: 1.5em; font-weight: bold;
            color: #ffd700; margin: 20px 0;
        }
        .status {
            position: fixed; top: 20px; right: 20px; padding: 10px 20px;
            background: rgba(0, 255, 0, 0.2); color: white; border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="status" id="status">🟢 Prêt</div>
    
    <div class="container">
        <div class="header">
            <h1>🧠 Test de QI</h1>
            <p>Évaluation des capacités cognitives de DeepSeek R1 8B</p>
        </div>

        <div class="controls">
            <button class="btn" id="startTest">🚀 Démarrer le Test</button>
            <button class="btn" id="resetTest">🔄 Recommencer</button>
        </div>

        <div class="score" id="score">Score: 0/10</div>

        <div id="testContainer"></div>
    </div>

    <script>
        const questions = [
            {
                category: "Logique Mathématique",
                question: "Quelle est la suite logique: 2, 6, 12, 20, 30, ?",
                expected: "42",
                points: 1
            },
            {
                category: "Raisonnement Verbal",
                question: "Complétez l'analogie: Livre est à Bibliothèque comme Tableau est à ?",
                expected: "musée|galerie|exposition",
                points: 1
            },
            {
                category: "Calcul Mental",
                question: "Calculez: (15 × 8) + (144 ÷ 12) - 7 = ?",
                expected: "125",
                points: 1
            },
            {
                category: "Logique Spatiale",
                question: "Si vous pliez une feuille en deux, puis encore en deux, puis faites un trou au centre, combien de trous aurez-vous en dépliant?",
                expected: "4",
                points: 1
            },
            {
                category: "Compréhension",
                question: "Expliquez en une phrase le paradoxe du grand-père en voyage temporel.",
                expected: "paradoxe|contradiction|impossible|grand-père|temps",
                points: 1
            },
            {
                category: "Créativité",
                question: "Donnez 3 utilisations créatives et inhabituelles pour un trombone.",
                expected: "créatif|original|inhabituel",
                points: 1
            },
            {
                category: "Logique Déductive",
                question: "Tous les A sont B. Tous les B sont C. Donc tous les A sont ?",
                expected: "C",
                points: 1
            },
            {
                category: "Mémoire de Travail",
                question: "Inversez cette séquence: JARVIS2024AI",
                expected: "IA4202SIVRAJ",
                points: 1
            },
            {
                category: "Résolution de Problème",
                question: "Un escargot monte un mur de 10m. Il monte 3m le jour, redescend 2m la nuit. En combien de jours atteint-il le sommet?",
                expected: "8",
                points: 1
            },
            {
                category: "Intelligence Émotionnelle",
                question: "Comment réconforteriez-vous quelqu'un qui vient de perdre son emploi?",
                expected: "empathie|soutien|écoute|comprendre|aide",
                points: 1
            }
        ];

        let currentTest = [];
        let currentQuestion = 0;
        let score = 0;
        let responses = [];

        const status = document.getElementById('status');
        const scoreDiv = document.getElementById('score');
        const testContainer = document.getElementById('testContainer');
        const startBtn = document.getElementById('startTest');
        const resetBtn = document.getElementById('resetTest');

        async function testServerConnection() {
            try {
                const response = await fetch('http://localhost:8000/health', { timeout: 3000 });
                status.textContent = response.ok ? '🟢 Serveur OK' : '🔴 Serveur KO';
                return response.ok;
            } catch (error) {
                status.textContent = '🔴 Pas de serveur';
                return false;
            }
        }

        async function askQuestion(question) {
            try {
                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: `Question: ${question}\nRéponse courte et précise:`,
                        n_predict: 100,
                        temperature: 0.3,
                        stream: false
                    })
                });

                if (!response.ok) throw new Error(`Erreur ${response.status}`);
                
                const data = await response.json();
                return (data.content || data.text || '').trim();
            } catch (error) {
                throw new Error(`Erreur serveur: ${error.message}`);
            }
        }

        function evaluateResponse(response, expected) {
            const responseLower = response.toLowerCase();
            const expectedPatterns = expected.toLowerCase().split('|');
            
            return expectedPatterns.some(pattern => 
                responseLower.includes(pattern) || 
                responseLower === pattern
            );
        }

        async function runTest() {
            if (!(await testServerConnection())) {
                alert('❌ Serveur non accessible. Démarrez le serveur llama.cpp d\'abord.');
                return;
            }

            startBtn.disabled = true;
            currentTest = [...questions];
            currentQuestion = 0;
            score = 0;
            responses = [];
            
            testContainer.innerHTML = '';
            updateScore();

            for (let i = 0; i < questions.length; i++) {
                const q = questions[i];
                
                // Créer l'interface de la question
                const questionDiv = document.createElement('div');
                questionDiv.className = 'test-section';
                questionDiv.innerHTML = `
                    <div class="question">${i + 1}. ${q.category}</div>
                    <div>${q.question}</div>
                    <div class="response loading" id="response-${i}">🤔 DeepSeek réfléchit...</div>
                `;
                testContainer.appendChild(questionDiv);

                const responseDiv = document.getElementById(`response-${i}`);
                
                try {
                    // Poser la question à DeepSeek
                    const answer = await askQuestion(q.question);
                    
                    // Évaluer la réponse
                    const isCorrect = evaluateResponse(answer, q.expected);
                    if (isCorrect) score++;
                    
                    // Afficher le résultat
                    responseDiv.className = `response ${isCorrect ? 'success' : 'error'}`;
                    responseDiv.innerHTML = `
                        <strong>Réponse:</strong> ${answer}<br>
                        <strong>Évaluation:</strong> ${isCorrect ? '✅ Correct' : '❌ Incorrect'} 
                        ${!isCorrect ? `(Attendu: ${q.expected})` : ''}
                    `;
                    
                    responses.push({ question: q.question, answer, isCorrect, expected: q.expected });
                    updateScore();
                    
                } catch (error) {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = `❌ ${error.message}`;
                }
                
                // Pause entre les questions
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // Afficher le résultat final
            showFinalResults();
            startBtn.disabled = false;
        }

        function updateScore() {
            scoreDiv.textContent = `Score: ${score}/${questions.length}`;
        }

        function showFinalResults() {
            const percentage = (score / questions.length) * 100;
            let evaluation = '';
            
            if (percentage >= 90) evaluation = '🏆 Génie - QI Exceptionnel';
            else if (percentage >= 80) evaluation = '🌟 Très Intelligent - QI Élevé';
            else if (percentage >= 70) evaluation = '👍 Intelligent - QI Au-dessus de la Moyenne';
            else if (percentage >= 60) evaluation = '📚 Moyen - QI Dans la Moyenne';
            else evaluation = '📖 Besoin d\'Amélioration';

            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-section';
            resultDiv.style.textAlign = 'center';
            resultDiv.style.fontSize = '1.3em';
            resultDiv.innerHTML = `
                <h2>🎯 Résultats Finaux</h2>
                <div style="margin: 20px 0;">
                    <strong>Score: ${score}/${questions.length} (${percentage.toFixed(1)}%)</strong>
                </div>
                <div style="color: #ffd700; font-size: 1.2em;">
                    ${evaluation}
                </div>
            `;
            testContainer.appendChild(resultDiv);
        }

        function resetTest() {
            testContainer.innerHTML = '';
            score = 0;
            currentQuestion = 0;
            responses = [];
            updateScore();
            startBtn.disabled = false;
        }

        startBtn.addEventListener('click', runTest);
        resetBtn.addEventListener('click', resetTest);

        // Test initial de connexion
        testServerConnection();
    </script>
</body>
</html>
