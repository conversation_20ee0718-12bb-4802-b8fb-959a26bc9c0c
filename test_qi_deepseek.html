<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QI - DeepSeek R1 8B</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 900px; margin: 0 auto;
            background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);
            border-radius: 20px; padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center; color: white; margin-bottom: 30px;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); }
        .test-section {
            background: rgba(255, 255, 255, 0.1); border-radius: 15px;
            padding: 20px; margin-bottom: 20px; color: white;
        }
        .question {
            font-size: 1.2em; margin-bottom: 15px; font-weight: bold;
        }
        .response {
            background: rgba(255, 255, 255, 0.2); border-radius: 10px;
            padding: 15px; margin: 10px 0; min-height: 60px;
            border: 2px solid transparent;
        }
        .response.loading { border-color: #ffd700; }
        .response.success { border-color: #00ff00; }
        .response.error { border-color: #ff0000; }
        .controls {
            text-align: center; margin: 20px 0;
        }
        .btn {
            background: rgba(255, 255, 255, 0.3); color: white;
            border: none; border-radius: 10px; padding: 15px 30px;
            font-size: 16px; font-weight: bold; cursor: pointer;
            margin: 0 10px; transition: all 0.3s ease;
        }
        .btn:hover { background: rgba(255, 255, 255, 0.4); transform: translateY(-2px); }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .score {
            text-align: center; font-size: 1.5em; font-weight: bold;
            color: #ffd700; margin: 20px 0;
        }
        .status {
            position: fixed; top: 20px; right: 20px; padding: 10px 20px;
            background: rgba(0, 255, 0, 0.2); color: white; border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="status" id="status">🟢 Prêt</div>
    
    <div class="container">
        <div class="header">
            <h1>🧠 Test de QI</h1>
            <p>Évaluation des capacités cognitives de DeepSeek R1 8B</p>
        </div>

        <div class="controls">
            <button class="btn" id="startTest">🚀 Démarrer le Test</button>
            <button class="btn" id="resetTest">🔄 Recommencer</button>
            <button class="btn" id="exportResults" style="display: none;">📊 Exporter Résultats</button>
        </div>

        <div class="score" id="score">Score: 0/10</div>

        <div id="testContainer"></div>
    </div>

    <script>
        const questions = [
            {
                category: "🔢 Logique Mathématique Avancée",
                question: "Trouvez le terme manquant dans cette suite complexe: 1, 1, 2, 3, 5, 8, 13, ?, 34. Expliquez la logique.",
                expected: "21|fibonacci|suite de fibonacci",
                points: 2,
                difficulty: "Difficile",
                explanation: "Suite de Fibonacci où chaque terme = somme des deux précédents"
            },
            {
                category: "🧩 Raisonnement Analogique",
                question: "Complétez cette analogie complexe: Einstein est à la Relativité comme Darwin est à ?",
                expected: "évolution|sélection naturelle|origine des espèces",
                points: 2,
                difficulty: "Moyen",
                explanation: "Théories scientifiques révolutionnaires de chaque savant"
            },
            {
                category: "🔢 Calcul Mental Complexe",
                question: "Calculez mentalement: √(144) × (2³ + 3²) - (15% de 80) = ?",
                expected: "132",
                points: 3,
                difficulty: "Difficile",
                explanation: "12 × (8 + 9) - 12 = 12 × 17 - 12 = 204 - 12 = 192... Erreur dans ma formulation, recalculons: 12 × 17 - 12 = 132"
            },
            {
                category: "🎯 Logique Spatiale 3D",
                question: "Un cube est peint en rouge sur toutes ses faces, puis découpé en 27 petits cubes égaux (3×3×3). Combien de petits cubes n'ont aucune face rouge?",
                expected: "1",
                points: 3,
                difficulty: "Très Difficile",
                explanation: "Seul le cube central n'a aucune face exposée"
            },
            {
                category: "🤔 Philosophie & Paradoxes",
                question: "Résolvez le paradoxe de Zénon: Achille court 10 fois plus vite qu'une tortue qui a 100m d'avance. Pourquoi Achille la rattrape-t-il malgré le paradoxe?",
                expected: "série convergente|limite|mathématiques|infini|temps fini",
                points: 3,
                difficulty: "Très Difficile",
                explanation: "La série infinie converge vers une limite finie"
            },
            {
                category: "🎨 Créativité & Innovation",
                question: "Inventez une solution créative pour réduire les embouteillages urbains sans construire de nouvelles routes. Soyez innovant!",
                expected: "créatif|innovant|original|télétravail|horaires|transport|technologie",
                points: 2,
                difficulty: "Moyen",
                explanation: "Évalue la pensée divergente et l'innovation"
            },
            {
                category: "⚖️ Logique Formelle",
                question: "Si 'Tous les programmeurs aiment le café' et 'Jean n'aime pas le café', que peut-on déduire logiquement sur Jean?",
                expected: "jean n'est pas programmeur|pas programmeur|modus tollens",
                points: 2,
                difficulty: "Moyen",
                explanation: "Raisonnement par modus tollens"
            },
            {
                category: "🧠 Mémoire & Manipulation",
                question: "Prenez le mot INTELLIGENCE, supprimez les voyelles, inversez l'ordre, puis ajoutez un X entre chaque lettre. Quel est le résultat?",
                expected: "NXCXNXGXLXLXTXN",
                points: 3,
                difficulty: "Difficile",
                explanation: "INTELLIGENCE → NTLLGNC → CNGLLTNT → C-X-N-X-G-X-L-X-L-X-T-X-N-X-T"
            },
            {
                category: "🔍 Résolution Complexe",
                question: "Trois amis partagent une pizza. Alice mange 1/3, Bob mange 1/4 du reste, Charlie mange 6 parts. Il reste 2 parts. Combien la pizza avait-elle de parts initialement?",
                expected: "16",
                points: 3,
                difficulty: "Très Difficile",
                explanation: "Équation: x - x/3 - (2x/3)/4 - 6 = 2, donc x = 16"
            },
            {
                category: "💝 Intelligence Émotionnelle",
                question: "Votre collègue semble irritable depuis une semaine. Il refuse de parler mais son travail en souffre. Comment gérez-vous cette situation délicate?",
                expected: "empathie|écoute|patience|soutien|discret|respectueux|aide|comprendre",
                points: 2,
                difficulty: "Moyen",
                explanation: "Évalue l'intelligence sociale et émotionnelle"
            },
            {
                category: "🌟 Pensée Critique",
                question: "Analysez cette affirmation: 'L'IA va remplacer tous les emplois humains d'ici 2030.' Donnez 3 arguments pour et 3 contre.",
                expected: "nuancé|arguments|pour|contre|complexe|réfléchi|équilibré",
                points: 3,
                difficulty: "Difficile",
                explanation: "Évalue la capacité d'analyse critique et de nuance"
            },
            {
                category: "🔮 Reconnaissance de Motifs",
                question: "Quelle est la logique de cette séquence: O, T, T, F, F, S, S, E, ?",
                expected: "N|nine|neuf",
                points: 3,
                difficulty: "Très Difficile",
                explanation: "Premières lettres des nombres: One, Two, Three, Four, Five, Six, Seven, Eight, Nine"
            }
        ];

        let currentTest = [];
        let currentQuestion = 0;
        let totalScore = 0;
        let maxScore = 0;
        let responses = [];
        let startTime = null;
        let detailedResults = [];

        const status = document.getElementById('status');
        const scoreDiv = document.getElementById('score');
        const testContainer = document.getElementById('testContainer');
        const startBtn = document.getElementById('startTest');
        const resetBtn = document.getElementById('resetTest');
        const exportBtn = document.getElementById('exportResults');

        function exportResults() {
            if (detailedResults.length === 0) {
                alert('Aucun résultat à exporter. Effectuez d\'abord un test.');
                return;
            }

            const totalTime = Date.now() - startTime;
            const percentage = (totalScore / maxScore) * 100;
            const avgResponseTime = detailedResults.reduce((sum, r) => sum + r.responseTime, 0) / detailedResults.length;

            let estimatedIQ = 100;
            if (percentage >= 95) estimatedIQ = 145;
            else if (percentage >= 90) estimatedIQ = 130;
            else if (percentage >= 80) estimatedIQ = 120;
            else if (percentage >= 70) estimatedIQ = 110;
            else if (percentage >= 60) estimatedIQ = 100;
            else if (percentage >= 50) estimatedIQ = 90;
            else estimatedIQ = 80;

            const report = `
# 🧠 RAPPORT DE TEST QI - DeepSeek R1 8B
Date: ${new Date().toLocaleString('fr-FR')}

## 📊 RÉSULTATS GLOBAUX
- **QI Estimé:** ${estimatedIQ}
- **Score:** ${totalScore.toFixed(1)}/${maxScore} points (${percentage.toFixed(1)}%)
- **Temps total:** ${(totalTime/60000).toFixed(1)} minutes
- **Temps moyen par question:** ${(avgResponseTime/1000).toFixed(1)} secondes

## 📋 DÉTAIL DES RÉPONSES
${detailedResults.map((result, i) => `
### Question ${i + 1}: ${result.question.category}
**Question:** ${result.question.question}
**Réponse DeepSeek:** ${result.answer || 'Erreur'}
**Points:** ${result.pointsEarned.toFixed(1)}/${result.question.points}
**Temps:** ${(result.responseTime/1000).toFixed(1)}s
**Statut:** ${result.evaluation.isCorrect ? '✅ Correct' : '❌ Incorrect'}
${result.question.explanation ? `**Explication:** ${result.question.explanation}` : ''}
`).join('\n')}

## 🎯 CONCLUSION
DeepSeek R1 8B a démontré ${percentage >= 80 ? 'de très bonnes' : percentage >= 60 ? 'de bonnes' : 'des'} capacités cognitives avec un QI estimé de ${estimatedIQ}.

---
Généré par le Test QI Avancé pour DeepSeek R1 8B
            `.trim();

            // Créer et télécharger le fichier
            const blob = new Blob([report], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `DeepSeek_R1_8B_Test_QI_${new Date().toISOString().slice(0,10)}.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('📊 Rapport exporté avec succès !');
        }

        async function testServerConnection() {
            try {
                const response = await fetch('http://localhost:8000/health', { timeout: 3000 });
                status.textContent = response.ok ? '🟢 Serveur OK' : '🔴 Serveur KO';
                return response.ok;
            } catch (error) {
                status.textContent = '🔴 Pas de serveur';
                return false;
            }
        }

        async function askQuestion(question) {
            try {
                const response = await fetch('http://localhost:8000/completion', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: `Question: ${question}\nRéponse courte et précise:`,
                        n_predict: 100,
                        temperature: 0.3,
                        stream: false
                    })
                });

                if (!response.ok) throw new Error(`Erreur ${response.status}`);
                
                const data = await response.json();
                return (data.content || data.text || '').trim();
            } catch (error) {
                throw new Error(`Erreur serveur: ${error.message}`);
            }
        }

        function evaluateResponse(response, expected, question) {
            const responseLower = response.toLowerCase().trim();
            const expectedPatterns = expected.toLowerCase().split('|');

            // Évaluation basique par mots-clés
            let basicMatch = expectedPatterns.some(pattern =>
                responseLower.includes(pattern.trim()) ||
                responseLower === pattern.trim()
            );

            // Évaluation avancée pour certains types de questions
            let advancedScore = 0;

            // Pour les questions mathématiques, vérifier la précision
            if (question.category.includes('Mathématique') || question.category.includes('Calcul')) {
                const numbers = response.match(/\d+/g);
                if (numbers && expectedPatterns.some(p => numbers.includes(p))) {
                    advancedScore += 0.5;
                }
            }

            // Pour les questions créatives, évaluer la richesse
            if (question.category.includes('Créativité') || question.category.includes('Innovation')) {
                const wordCount = response.split(' ').length;
                const uniqueWords = new Set(response.toLowerCase().split(' ')).size;
                if (wordCount > 20 && uniqueWords > 15) advancedScore += 0.3;
                if (response.includes('innovant') || response.includes('créatif') || response.includes('original')) {
                    advancedScore += 0.2;
                }
            }

            // Pour les questions de logique, vérifier le raisonnement
            if (question.category.includes('Logique') || question.category.includes('Raisonnement')) {
                if (response.includes('donc') || response.includes('par conséquent') || response.includes('ainsi')) {
                    advancedScore += 0.2;
                }
                if (response.includes('parce que') || response.includes('car') || response.includes('puisque')) {
                    advancedScore += 0.2;
                }
            }

            // Score final combiné
            return {
                isCorrect: basicMatch || advancedScore > 0.5,
                confidence: basicMatch ? 1.0 : Math.min(advancedScore, 0.8),
                details: {
                    basicMatch,
                    advancedScore: Math.round(advancedScore * 100) / 100,
                    wordCount: response.split(' ').length
                }
            };
        }

        async function runTest() {
            if (!(await testServerConnection())) {
                alert('❌ Serveur non accessible. Démarrez le serveur llama.cpp d\'abord.');
                return;
            }

            startBtn.disabled = true;
            startTime = Date.now();
            currentTest = [...questions];
            currentQuestion = 0;
            totalScore = 0;
            maxScore = questions.reduce((sum, q) => sum + q.points, 0);
            responses = [];
            detailedResults = [];

            testContainer.innerHTML = '';
            updateScore();

            // Ajouter un indicateur de progression
            const progressDiv = document.createElement('div');
            progressDiv.className = 'test-section';
            progressDiv.innerHTML = `
                <div style="text-align: center;">
                    <div style="background: rgba(255,255,255,0.2); border-radius: 10px; height: 20px; margin: 10px 0;">
                        <div id="progressBar" style="background: #ffd700; height: 100%; border-radius: 10px; width: 0%; transition: width 0.5s;"></div>
                    </div>
                    <div id="progressText">Question 0/${questions.length}</div>
                </div>
            `;
            testContainer.appendChild(progressDiv);

            for (let i = 0; i < questions.length; i++) {
                const q = questions[i];

                // Mettre à jour la progression
                document.getElementById('progressBar').style.width = `${(i / questions.length) * 100}%`;
                document.getElementById('progressText').textContent = `Question ${i + 1}/${questions.length}`;

                // Créer l'interface de la question
                const questionDiv = document.createElement('div');
                questionDiv.className = 'test-section';
                questionDiv.innerHTML = `
                    <div class="question">
                        ${i + 1}. ${q.category}
                        <span style="float: right; font-size: 0.8em; color: #ffd700;">
                            ${q.difficulty} • ${q.points} pts
                        </span>
                    </div>
                    <div style="margin: 15px 0; font-size: 1.1em;">${q.question}</div>
                    <div class="response loading" id="response-${i}">🤔 DeepSeek R1 8B analyse la question...</div>
                `;
                testContainer.appendChild(questionDiv);

                const responseDiv = document.getElementById(`response-${i}`);
                const questionStartTime = Date.now();

                try {
                    // Poser la question à DeepSeek avec prompt optimisé
                    const optimizedPrompt = `Question de test de QI (${q.difficulty}):
${q.question}

Répondez de manière précise et détaillée. Montrez votre raisonnement si nécessaire.
Réponse:`;

                    const answer = await askQuestion(optimizedPrompt);
                    const responseTime = Date.now() - questionStartTime;

                    // Évaluer la réponse avec le nouveau système
                    const evaluation = evaluateResponse(answer, q.expected, q);
                    const pointsEarned = evaluation.isCorrect ? q.points * evaluation.confidence : 0;
                    totalScore += pointsEarned;

                    // Afficher le résultat détaillé
                    responseDiv.className = `response ${evaluation.isCorrect ? 'success' : 'error'}`;
                    responseDiv.innerHTML = `
                        <div style="margin-bottom: 10px;">
                            <strong>Réponse DeepSeek:</strong><br>
                            <em>${answer}</em>
                        </div>
                        <div style="border-top: 1px solid rgba(255,255,255,0.3); padding-top: 10px;">
                            <strong>Évaluation:</strong>
                            ${evaluation.isCorrect ? '✅' : '❌'}
                            ${pointsEarned.toFixed(1)}/${q.points} points
                            (Confiance: ${(evaluation.confidence * 100).toFixed(0)}%)
                            <br>
                            <strong>Temps:</strong> ${(responseTime / 1000).toFixed(1)}s
                            ${q.explanation ? `<br><strong>Explication:</strong> ${q.explanation}` : ''}
                        </div>
                    `;

                    detailedResults.push({
                        question: q,
                        answer,
                        evaluation,
                        pointsEarned,
                        responseTime
                    });

                    updateScore();

                } catch (error) {
                    responseDiv.className = 'response error';
                    responseDiv.innerHTML = `❌ Erreur: ${error.message}`;
                    detailedResults.push({
                        question: q,
                        answer: null,
                        evaluation: { isCorrect: false, confidence: 0 },
                        pointsEarned: 0,
                        responseTime: 0,
                        error: error.message
                    });
                }

                // Pause entre les questions
                await new Promise(resolve => setTimeout(resolve, 1500));
            }

            // Finaliser la progression
            document.getElementById('progressBar').style.width = '100%';
            document.getElementById('progressText').textContent = 'Test terminé!';

            // Afficher le résultat final
            showFinalResults();
            startBtn.disabled = false;
            exportBtn.style.display = 'inline-block';
        }

        function updateScore() {
            const percentage = maxScore > 0 ? (totalScore / maxScore) * 100 : 0;
            scoreDiv.innerHTML = `
                <div>Score: ${totalScore.toFixed(1)}/${maxScore} points</div>
                <div style="font-size: 0.8em; opacity: 0.8;">${percentage.toFixed(1)}% • ${detailedResults.length}/${questions.length} questions</div>
            `;
        }

        function showFinalResults() {
            const totalTime = Date.now() - startTime;
            const percentage = (totalScore / maxScore) * 100;
            const avgResponseTime = detailedResults.reduce((sum, r) => sum + r.responseTime, 0) / detailedResults.length;

            // Calcul du QI estimé (basé sur une échelle standard)
            let estimatedIQ = 100; // QI moyen
            if (percentage >= 95) estimatedIQ = 145; // Génie
            else if (percentage >= 90) estimatedIQ = 130; // Très supérieur
            else if (percentage >= 80) estimatedIQ = 120; // Supérieur
            else if (percentage >= 70) estimatedIQ = 110; // Au-dessus de la moyenne
            else if (percentage >= 60) estimatedIQ = 100; // Moyen
            else if (percentage >= 50) estimatedIQ = 90;  // En dessous de la moyenne
            else estimatedIQ = 80; // Faible

            let evaluation = '';
            let color = '#ffd700';

            if (estimatedIQ >= 140) { evaluation = '🏆 Génie Exceptionnel'; color = '#ff6b6b'; }
            else if (estimatedIQ >= 130) { evaluation = '🌟 Très Supérieur'; color = '#4ecdc4'; }
            else if (estimatedIQ >= 120) { evaluation = '💎 Supérieur'; color = '#45b7d1'; }
            else if (estimatedIQ >= 110) { evaluation = '👍 Au-dessus de la Moyenne'; color = '#96ceb4'; }
            else if (estimatedIQ >= 90) { evaluation = '📚 Dans la Moyenne'; color = '#feca57'; }
            else { evaluation = '📖 Besoin d\'Amélioration'; color = '#ff9ff3'; }

            // Analyse par catégorie
            const categoryStats = {};
            detailedResults.forEach(result => {
                const cat = result.question.category;
                if (!categoryStats[cat]) {
                    categoryStats[cat] = { total: 0, earned: 0, count: 0 };
                }
                categoryStats[cat].total += result.question.points;
                categoryStats[cat].earned += result.pointsEarned;
                categoryStats[cat].count++;
            });

            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-section';
            resultDiv.style.textAlign = 'center';
            resultDiv.innerHTML = `
                <h2 style="color: ${color}; margin-bottom: 20px;">🎯 Analyse Complète du QI</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                        <div style="font-size: 2em; color: ${color};">${estimatedIQ}</div>
                        <div>QI Estimé</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                        <div style="font-size: 2em; color: #4ecdc4;">${percentage.toFixed(1)}%</div>
                        <div>Score Global</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                        <div style="font-size: 2em; color: #96ceb4;">${(avgResponseTime/1000).toFixed(1)}s</div>
                        <div>Temps Moyen</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                        <div style="font-size: 2em; color: #feca57;">${(totalTime/60000).toFixed(1)}min</div>
                        <div>Durée Totale</div>
                    </div>
                </div>

                <div style="color: ${color}; font-size: 1.3em; margin: 20px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    ${evaluation}
                </div>

                <div style="text-align: left; margin: 20px 0;">
                    <h3 style="color: white; margin-bottom: 15px;">📊 Analyse par Domaine Cognitif</h3>
                    ${Object.entries(categoryStats).map(([cat, stats]) => {
                        const catPercentage = (stats.earned / stats.total) * 100;
                        const barColor = catPercentage >= 80 ? '#4ecdc4' : catPercentage >= 60 ? '#feca57' : '#ff6b6b';
                        return `
                            <div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>${cat}</span>
                                    <span>${stats.earned.toFixed(1)}/${stats.total} (${catPercentage.toFixed(0)}%)</span>
                                </div>
                                <div style="background: rgba(255,255,255,0.2); border-radius: 5px; height: 8px;">
                                    <div style="background: ${barColor}; height: 100%; border-radius: 5px; width: ${catPercentage}%;"></div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>

                <div style="margin-top: 30px; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 10px; text-align: left;">
                    <h4 style="color: white; margin-bottom: 10px;">🔍 Recommandations pour DeepSeek R1 8B:</h4>
                    <ul style="color: rgba(255,255,255,0.9); line-height: 1.6;">
                        ${percentage >= 90 ? '<li>✅ Excellentes capacités de raisonnement général</li>' : '<li>⚠️ Peut améliorer le raisonnement complexe</li>'}
                        ${avgResponseTime < 5000 ? '<li>✅ Temps de réponse rapide et efficace</li>' : '<li>⚠️ Temps de réflexion parfois long</li>'}
                        ${categoryStats['🔢 Logique Mathématique Avancée']?.earned / categoryStats['🔢 Logique Mathématique Avancée']?.total > 0.8 ? '<li>✅ Très bon en mathématiques</li>' : '<li>📚 Peut progresser en calcul mental</li>'}
                        ${categoryStats['🎨 Créativité & Innovation']?.earned / categoryStats['🎨 Créativité & Innovation']?.total > 0.7 ? '<li>✅ Créativité satisfaisante</li>' : '<li>🎨 Potentiel créatif à développer</li>'}
                    </ul>
                </div>
            `;
            testContainer.appendChild(resultDiv);
        }

        function resetTest() {
            testContainer.innerHTML = '';
            totalScore = 0;
            maxScore = 0;
            currentQuestion = 0;
            responses = [];
            detailedResults = [];
            startTime = null;
            updateScore();
            startBtn.disabled = false;

            // Message de reset
            const resetDiv = document.createElement('div');
            resetDiv.className = 'test-section';
            resetDiv.style.textAlign = 'center';
            resetDiv.innerHTML = `
                <div style="color: #ffd700; font-size: 1.2em;">
                    🔄 Test réinitialisé - Prêt pour une nouvelle évaluation
                </div>
            `;
            testContainer.appendChild(resetDiv);

            setTimeout(() => {
                if (testContainer.children.length === 1) {
                    testContainer.innerHTML = '';
                }
            }, 3000);
        }

        startBtn.addEventListener('click', runTest);
        resetBtn.addEventListener('click', resetTest);
        exportBtn.addEventListener('click', exportResults);

        // Test initial de connexion
        testServerConnection();
    </script>
</body>
</html>
